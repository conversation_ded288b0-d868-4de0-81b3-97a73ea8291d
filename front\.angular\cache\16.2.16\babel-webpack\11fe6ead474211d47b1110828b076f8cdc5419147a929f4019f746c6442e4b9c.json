{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  redirectTo: '/login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: '**',\n  redirectTo: '/login'\n} // Route wildcard pour les URLs non trouvées\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LoginComponent", "DashboardComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "component", "canActivate", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { SearchFlightComponent } from './components/search-flight/search-flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { AuthGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: '', redirectTo: '/login', pathMatch: 'full' },\n  { path: 'login', component: LoginComponent },\n  {\n    path: 'dashboard',\n    component: DashboardComponent,\n    canActivate: [AuthGuard]\n  },\n  { path: '**', redirectTo: '/login' } // Route wildcard pour les URLs non trouvées\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,kBAAkB,QAAQ,4CAA4C;AAG/E,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAM,CAAE,EACrD;EAAEF,IAAI,EAAE,OAAO;EAAEG,SAAS,EAAEP;AAAc,CAAE,EAC5C;EACEI,IAAI,EAAE,WAAW;EACjBG,SAAS,EAAEN,kBAAkB;EAC7BO,WAAW,EAAE,CAACN,SAAS;CACxB,EACD;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAQ,CAAE,CAAC;AAAA,CACtC;;AAMD,OAAM,MAAOI,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBV,YAAY,CAACW,OAAO,CAACP,MAAM,CAAC,EAC5BJ,YAAY;IAAA;EAAA;;;2EAEXU,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFjBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}