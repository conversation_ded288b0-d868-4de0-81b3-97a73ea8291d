{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/flight-search.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../services/airline-logo.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction FlightResultsComponent_div_3__svg_svg_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 8);\n    i0.ɵɵelement(1, \"path\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_3_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r9.searchSummary == null ? null : ctx_r9.searchSummary.returnDate, \"\");\n  }\n}\nfunction FlightResultsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 8);\n    i0.ɵɵelement(7, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FlightResultsComponent_div_3__svg_svg_8_Template, 2, 0, \"svg\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"span\", 60);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FlightResultsComponent_div_3_span_14_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementStart(15, \"span\", 62);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 63)(18, \"span\", 64);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 65);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 66)(23, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.modifySearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 8);\n    i0.ɵɵelement(25, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Modifier la recherche \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(27, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_3_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.backToSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 8);\n    i0.ɵɵelement(29, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Nouvelle recherche \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.from);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.searchType) === \"roundtrip\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.to);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.departureDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.returnDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getPassengerText());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.totalResults, \" vols trouv\\u00E9s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Recherche effectu\\u00E9e \\u00E0 \", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.searchTime, \"\");\n  }\n}\nfunction FlightResultsComponent_label_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 22)(1, \"input\", 72);\n    i0.ɵɵlistener(\"change\", function FlightResultsComponent_label_31_Template_input_change_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const airline_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleAirlineFilter(airline_r13.code));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 24);\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const airline_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", airline_r13.code)(\"checked\", ctx_r1.filters.airlines.includes(airline_r13.code));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(airline_r13.name);\n  }\n}\nfunction FlightResultsComponent__svg_path_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 74);\n  }\n}\nfunction FlightResultsComponent__svg_path_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 75);\n  }\n}\nfunction FlightResultsComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Recherche de vols en cours...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightResultsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Erreur lors de la recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_86_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.backToSearch());\n    });\n    i0.ɵɵtext(9, \"Retour \\u00E0 la recherche\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.error);\n  }\n}\nfunction FlightResultsComponent_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Aucun vol trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Essayez de modifier vos crit\\u00E8res de recherche ou vos filtres.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.clearFilters());\n    });\n    i0.ɵɵtext(9, \"Effacer les filtres\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 126);\n    i0.ɵɵlistener(\"error\", function FlightResultsComponent_div_87_div_2_img_4_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const flight_r22 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onLogoError(flight_r22));\n    })(\"load\", function FlightResultsComponent_div_87_div_2_img_4_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const flight_r22 = i0.ɵɵnextContext().$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.onLogoLoad(flight_r22));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r22.airline.logo, i0.ɵɵsanitizeUrl)(\"alt\", flight_r22.airline.name);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 127);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r22.airline.code, \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 128);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 124);\n    i0.ɵɵelement(2, \"path\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 130);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 124);\n    i0.ɵɵelement(2, \"path\", 131)(3, \"path\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Remboursable \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵtext(1, \"\\u00C9conomique\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 134);\n    i0.ɵɵtext(1, \"Affaires\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 135);\n    i0.ɵɵtext(1, \"Premi\\u00E8re\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_20_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", flight_r22.stops, \" escale\", flight_r22.stops > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"div\", 137)(2, \"div\", 138);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 139);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 140);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 141)(9, \"div\", 142);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 143);\n    i0.ɵɵelement(12, \"div\", 144);\n    i0.ɵɵtemplate(13, FlightResultsComponent_div_87_div_2_div_20_div_13_Template, 2, 2, \"div\", 145);\n    i0.ɵɵelementStart(14, \"div\", 146);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(15, \"svg\", 8);\n    i0.ɵɵelement(16, \"path\", 57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(17, \"div\", 147)(18, \"div\", 138);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 139);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 140);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r22.departure.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.departure.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.departure.city);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r22.duration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.stops > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.city);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 164);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Aller \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 166);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Retour \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 167);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8);\n    i0.ɵɵelement(2, \"path\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r45 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Segment \", i_r45 + 1, \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r55 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 168);\n    i0.ɵɵlistener(\"error\", function FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const segment_r44 = i0.ɵɵnextContext().$implicit;\n      const ctx_r53 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r53.onSegmentLogoError(segment_r44));\n    })(\"load\", function FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r55);\n      const segment_r44 = i0.ɵɵnextContext().$implicit;\n      const ctx_r56 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r56.onSegmentLogoLoad(segment_r44));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r44 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", segment_r44.airline.logo, i0.ɵɵsanitizeUrl)(\"alt\", segment_r44.airline.name);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 169);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r44 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(segment_r44.airline.code);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 148);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const segment_r44 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", segment_r44.stops, \" escale\", segment_r44.stops > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 155)(1, \"div\", 156);\n    i0.ɵɵtemplate(2, FlightResultsComponent_div_87_div_2_div_21_div_2_span_2_Template, 4, 0, \"span\", 157);\n    i0.ɵɵtemplate(3, FlightResultsComponent_div_87_div_2_div_21_div_2_span_3_Template, 4, 0, \"span\", 158);\n    i0.ɵɵtemplate(4, FlightResultsComponent_div_87_div_2_div_21_div_2_span_4_Template, 4, 1, \"span\", 159);\n    i0.ɵɵelementStart(5, \"span\", 160);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 161)(8, \"div\", 137)(9, \"div\", 138);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 139);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 140);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 141)(16, \"div\", 88);\n    i0.ɵɵtemplate(17, FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template, 1, 2, \"img\", 162);\n    i0.ɵɵtemplate(18, FlightResultsComponent_div_87_div_2_div_21_div_2_div_18_Template, 2, 1, \"div\", 163);\n    i0.ɵɵelementStart(19, \"div\", 93);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 142);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 143);\n    i0.ɵɵelement(24, \"div\", 144);\n    i0.ɵɵtemplate(25, FlightResultsComponent_div_87_div_2_div_21_div_2_div_25_Template, 2, 2, \"div\", 145);\n    i0.ɵɵelementStart(26, \"div\", 146);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(27, \"svg\", 8);\n    i0.ɵɵelement(28, \"path\", 57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(29, \"div\", 147)(30, \"div\", 138);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 139);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 140);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const segment_r44 = ctx.$implicit;\n    const i_r45 = ctx.index;\n    const flight_r22 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassProp(\"outbound\", i_r45 === 0)(\"return\", i_r45 === 1 && flight_r22.searchType === \"roundtrip\")(\"multicity-segment\", flight_r22.searchType === \"multicity\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.searchType === \"roundtrip\" && i_r45 === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.searchType === \"roundtrip\" && i_r45 === 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.searchType === \"multicity\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.departure.date);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(segment_r44.departure.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.departure.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.departure.city);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", segment_r44.airline.logo && !segment_r44.airline.logoError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !segment_r44.airline.logo || segment_r44.airline.logoError);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.flightNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.duration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", segment_r44.stops > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(segment_r44.arrival.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.arrival.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(segment_r44.arrival.city);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 149)(1, \"div\", 150);\n    i0.ɵɵtemplate(2, FlightResultsComponent_div_87_div_2_div_21_div_2_Template, 36, 21, \"div\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 152)(4, \"span\", 153);\n    i0.ɵɵtext(5, \"Dur\\u00E9e totale:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 154);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", flight_r22.segments)(\"ngForTrackBy\", ctx_r31.trackBySegmentIndex);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r22.totalDuration);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 123);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 124);\n    i0.ɵɵelement(2, \"path\", 131)(3, \"path\", 132);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Remboursable \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r64 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r64);\n      const flight_r22 = restoredCtx.$implicit;\n      const ctx_r63 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r63.selectFlight(flight_r22));\n    });\n    i0.ɵɵelementStart(1, \"div\", 87)(2, \"div\", 88)(3, \"div\", 89);\n    i0.ɵɵtemplate(4, FlightResultsComponent_div_87_div_2_img_4_Template, 1, 2, \"img\", 90);\n    i0.ɵɵtemplate(5, FlightResultsComponent_div_87_div_2_div_5_Template, 2, 1, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 92)(7, \"span\", 73);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 93);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 94);\n    i0.ɵɵtext(12, \"Boeing 737-800\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 95);\n    i0.ɵɵtemplate(14, FlightResultsComponent_div_87_div_2_span_14_Template, 4, 0, \"span\", 96);\n    i0.ɵɵtemplate(15, FlightResultsComponent_div_87_div_2_span_15_Template, 5, 0, \"span\", 97);\n    i0.ɵɵtemplate(16, FlightResultsComponent_div_87_div_2_span_16_Template, 2, 0, \"span\", 98);\n    i0.ɵɵtemplate(17, FlightResultsComponent_div_87_div_2_span_17_Template, 2, 0, \"span\", 99);\n    i0.ɵɵtemplate(18, FlightResultsComponent_div_87_div_2_span_18_Template, 2, 0, \"span\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 101);\n    i0.ɵɵtemplate(20, FlightResultsComponent_div_87_div_2_div_20_Template, 24, 8, \"div\", 102);\n    i0.ɵɵtemplate(21, FlightResultsComponent_div_87_div_2_div_21_Template, 8, 3, \"div\", 103);\n    i0.ɵɵelementStart(22, \"div\", 104)(23, \"div\", 105)(24, \"div\", 106);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 107);\n    i0.ɵɵtext(27, \"par personne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 108);\n    i0.ɵɵtext(29, \"TTC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 109)(31, \"button\", 110);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_2_Template_button_click_31_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r64);\n      const flight_r22 = restoredCtx.$implicit;\n      const ctx_r65 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r65.showFlightDetails(flight_r22));\n    });\n    i0.ɵɵtext(32, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 111);\n    i0.ɵɵtext(34, \"S\\u00E9lectionner\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(35, \"div\", 112)(36, \"div\", 113)(37, \"div\", 114);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(38, \"svg\", 8);\n    i0.ɵɵelement(39, \"path\", 115);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(40, \"div\", 116)(41, \"span\", 117);\n    i0.ɵɵtext(42, \"Bagage cabine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 118);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"div\", 119);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(46, \"svg\", 8);\n    i0.ɵɵelement(47, \"path\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(48, \"div\", 116)(49, \"span\", 117);\n    i0.ɵɵtext(50, \"Bagage soute\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 118);\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(53, \"div\", 121);\n    i0.ɵɵtemplate(54, FlightResultsComponent_div_87_div_2_span_54_Template, 5, 0, \"span\", 122);\n    i0.ɵɵelementStart(55, \"span\", 123);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(56, \"svg\", 124);\n    i0.ɵɵelement(57, \"path\", 125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(58, \" Modifiable \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const flight_r22 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.airline.logo && !flight_r22.airline.logoError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r22.airline.logo || flight_r22.airline.logoError);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r22.airline.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", flight_r22.airline.code, \" \", flight_r22.id.split(\"-\")[1] || \"1234\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.stops === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.refundable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"Economy\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"Business\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"First\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.searchType === \"oneway\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.searchType === \"roundtrip\" || flight_r22.searchType === \"multicity\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r22.price.formatted);\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate((flight_r22.baggage == null ? null : flight_r22.baggage.carryOn) || \"1 x 8kg\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((flight_r22.baggage == null ? null : flight_r22.baggage.checked) || \"1 x 23kg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.refundable);\n  }\n}\nfunction FlightResultsComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtemplate(1, FlightResultsComponent_div_87_div_1_Template, 10, 0, \"div\", 82);\n    i0.ɵɵtemplate(2, FlightResultsComponent_div_87_div_2_Template, 59, 16, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"grid-view\", ctx_r6.viewMode === \"grid\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.filteredFlights.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getPaginatedFlights())(\"ngForTrackBy\", ctx_r6.trackByFlightId);\n  }\n}\nfunction FlightResultsComponent_div_88_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 176);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_button_6_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r69);\n      const page_r67 = restoredCtx.$implicit;\n      const ctx_r68 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r68.changePage(page_r67));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r67 = ctx.$implicit;\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r67 === ctx_r66.currentPage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", page_r67, \" \");\n  }\n}\nfunction FlightResultsComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 170)(1, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r70.changePage(ctx_r70.currentPage - 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 172);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Pr\\u00E9c\\u00E9dent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 173);\n    i0.ɵɵtemplate(6, FlightResultsComponent_div_88_button_6_Template, 2, 3, \"button\", 174);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r72 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r72.changePage(ctx_r72.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \" Suivant \");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 8);\n    i0.ɵɵelement(10, \"path\", 175);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.currentPage === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getPageNumbers());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.currentPage === ctx_r7.totalPages);\n  }\n}\nexport class FlightResultsComponent {\n  constructor(flightSearchService, router, route, airlineLogoService) {\n    this.flightSearchService = flightSearchService;\n    this.router = router;\n    this.route = route;\n    this.airlineLogoService = airlineLogoService;\n    this.destroy$ = new Subject();\n    // État du composant\n    this.loading = false;\n    this.error = null;\n    // Données de recherche\n    this.searchSummary = null;\n    this.flights = [];\n    this.filteredFlights = [];\n    // Filtres et tri\n    this.filters = {\n      minPrice: 0,\n      maxPrice: 0,\n      currentPrice: 0,\n      airlines: [],\n      stops: [],\n      departureTime: {\n        min: 0,\n        max: 24\n      },\n      duration: {\n        min: 0,\n        max: 24\n      }\n    };\n    this.sortBy = 'price'; // price, duration, departure\n    this.sortOrder = 'asc'; // asc, desc\n    // Options d'affichage\n    this.viewMode = 'list'; // list, grid\n    this.showFilters = true;\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalPages = 1;\n  }\n  ngOnInit() {\n    this.loadSearchResults();\n    this.initializeFilters();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Charge les résultats de recherche depuis le service\n   */\n  loadSearchResults() {\n    // Récupérer les paramètres de recherche depuis l'URL ou le service\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['searchData']) {\n        try {\n          const searchData = JSON.parse(decodeURIComponent(params['searchData']));\n          this.performSearch(searchData);\n        } catch (error) {\n          console.error('Erreur lors du parsing des données de recherche:', error);\n          this.router.navigate(['/search-flight']);\n        }\n      } else {\n        // Rediriger vers la page de recherche si aucune donnée\n        this.router.navigate(['/search-flight']);\n      }\n    });\n  }\n  /**\n   * Effectue la recherche de vols\n   */\n  performSearch(searchData) {\n    this.loading = true;\n    this.error = null;\n    // Simuler une recherche pour le moment\n    setTimeout(() => {\n      this.processSearchResults(this.generateMockResults(), searchData);\n      this.loading = false;\n    }, 2000);\n  }\n  /**\n   * Traite les résultats de recherche\n   */\n  processSearchResults(response, searchData) {\n    // Créer le résumé de recherche\n    this.searchSummary = {\n      searchType: searchData.type,\n      from: searchData.params.departureLocation,\n      to: searchData.params.arrivalLocation,\n      departureDate: searchData.params.departureDate,\n      returnDate: searchData.params.returnDate,\n      passengers: searchData.params.passengers,\n      totalResults: 0,\n      searchTime: new Date().toLocaleTimeString()\n    };\n    // Traiter les vols selon le type de réponse\n    this.flights = this.extractFlights(response, searchData.type);\n    this.searchSummary.totalResults = this.flights.length;\n    // Appliquer les filtres initiaux\n    this.applyFilters();\n    this.updatePagination();\n  }\n  /**\n   * Extrait les vols de la réponse API selon le type de recherche\n   */\n  extractFlights(response, searchType) {\n    const flights = [];\n    if (!response.body?.flights) {\n      return flights;\n    }\n    response.body.flights.forEach((flight, index) => {\n      try {\n        const flightResult = this.createFlightResult(flight, searchType, index);\n        if (flightResult) {\n          flights.push(flightResult);\n        }\n      } catch (error) {\n        console.error('Erreur lors du traitement du vol:', error, flight);\n      }\n    });\n    return flights;\n  }\n  /**\n   * Crée un FlightResult à partir des données de l'API\n   */\n  createFlightResult(flight, searchType, index) {\n    if (!flight.items || flight.items.length === 0) {\n      return null;\n    }\n    // Extraire tous les segments\n    const segments = flight.items.map(item => this.createFlightSegment(item));\n    // Calculer la durée totale\n    const totalDuration = this.calculateTotalDuration(segments);\n    // Obtenir le prix depuis les offres\n    const price = this.extractPrice(flight.offers);\n    // Segment principal (premier segment pour compatibilité)\n    const mainSegment = segments[0];\n    // Segment final (dernier segment pour l'arrivée finale)\n    const finalSegment = segments[segments.length - 1];\n    return {\n      id: flight.id || `flight-${index}`,\n      searchType,\n      segments,\n      totalDuration,\n      price,\n      class: mainSegment.airline.name || 'Economy',\n      refundable: this.isRefundable(flight),\n      changeable: this.isChangeable(flight),\n      // Propriétés de compatibilité (basées sur le segment principal)\n      airline: mainSegment.airline,\n      departure: mainSegment.departure,\n      arrival: finalSegment.arrival,\n      duration: totalDuration,\n      stops: this.calculateTotalStops(segments),\n      baggage: mainSegment.baggage\n    };\n  }\n  /**\n   * Crée un segment de vol à partir des données de l'API\n   */\n  createFlightSegment(item) {\n    const airline = {\n      code: item.airline?.internationalCode || item.airline?.code || 'XX',\n      name: item.airline?.name || 'Compagnie inconnue',\n      logo: item.airline?.logo || item.airline?.logoFull || this.airlineLogoService.getAirlineLogo(item.airline?.internationalCode || item.airline?.code || ''),\n      logoError: false\n    };\n    return {\n      airline,\n      departure: {\n        airport: item.departure?.airport?.code || item.departure?.code || '',\n        city: item.departure?.airport?.city || item.departure?.city || '',\n        time: this.formatTime(item.departure?.time || item.flightDate),\n        date: this.formatDate(item.departure?.date || item.flightDate)\n      },\n      arrival: {\n        airport: item.arrival?.airport?.code || item.arrival?.code || '',\n        city: item.arrival?.airport?.city || item.arrival?.city || '',\n        time: this.formatTime(item.arrival?.time),\n        date: this.formatDate(item.arrival?.date)\n      },\n      duration: this.formatDuration(item.duration),\n      stops: item.stopCount || 0,\n      flightNumber: item.flightNo || '',\n      aircraft: item.aircraft || '',\n      baggage: this.extractBaggage(item.baggageInformations)\n    };\n  }\n  /**\n   * Calcule la durée totale de tous les segments\n   */\n  calculateTotalDuration(segments) {\n    if (segments.length === 0) return '0h 00m';\n    // Pour un seul segment, retourner sa durée\n    if (segments.length === 1) {\n      return segments[0].duration;\n    }\n    // Pour plusieurs segments, calculer le temps total entre le départ du premier et l'arrivée du dernier\n    const firstDeparture = new Date(`${segments[0].departure.date}T${segments[0].departure.time}`);\n    const lastArrival = new Date(`${segments[segments.length - 1].arrival.date}T${segments[segments.length - 1].arrival.time}`);\n    const totalMinutes = Math.floor((lastArrival.getTime() - firstDeparture.getTime()) / (1000 * 60));\n    return this.formatDuration(totalMinutes);\n  }\n  /**\n   * Calcule le nombre total d'escales\n   */\n  calculateTotalStops(segments) {\n    return segments.reduce((total, segment) => total + segment.stops, 0);\n  }\n  /**\n   * Extrait le prix depuis les offres\n   */\n  extractPrice(offers) {\n    if (!offers || offers.length === 0) {\n      return {\n        amount: 0,\n        currency: 'EUR',\n        formatted: this.formatPrice(0)\n      };\n    }\n    // Prendre la première offre ou la moins chère\n    const offer = offers[0];\n    const amount = offer.price?.amount || offer.totalPrice || 0;\n    const currency = offer.price?.currency || offer.currency || 'EUR';\n    return {\n      amount,\n      currency,\n      formatted: this.formatPrice(amount)\n    };\n  }\n  /**\n   * Vérifie si le vol est remboursable\n   */\n  isRefundable(flight) {\n    // Logique à adapter selon la structure des données\n    return flight.refundable || false;\n  }\n  /**\n   * Vérifie si le vol est modifiable\n   */\n  isChangeable(flight) {\n    // Logique à adapter selon la structure des données\n    return flight.changeable || false;\n  }\n  /**\n   * Extrait les informations de bagages\n   */\n  extractBaggage(baggageInfos) {\n    if (!baggageInfos || baggageInfos.length === 0) {\n      return {\n        carryOn: '8kg',\n        checked: '23kg'\n      };\n    }\n    let carryOn = '';\n    let checked = '';\n    baggageInfos.forEach(baggage => {\n      if (baggage.baggageType === 0) {\n        // Bagage cabine\n        carryOn = `${baggage.weight || baggage.piece}${baggage.unitType === 0 ? 'kg' : ' pièce(s)'}`;\n      } else if (baggage.baggageType === 1) {\n        // Bagage soute\n        checked = `${baggage.weight || baggage.piece}${baggage.unitType === 0 ? 'kg' : ' pièce(s)'}`;\n      }\n    });\n    return {\n      carryOn: carryOn || '8kg',\n      checked: checked || '23kg'\n    };\n  }\n  /**\n   * Formate l'heure depuis une date ISO ou un timestamp\n   */\n  formatTime(dateTime) {\n    if (!dateTime) return '';\n    try {\n      const date = new Date(dateTime);\n      return date.toLocaleTimeString('fr-FR', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    } catch {\n      return dateTime.substring(11, 16) || ''; // Fallback pour format HH:mm\n    }\n  }\n  /**\n   * Formate la date depuis une date ISO\n   */\n  formatDate(dateTime) {\n    if (!dateTime) return '';\n    try {\n      const date = new Date(dateTime);\n      return date.toLocaleDateString('fr-FR');\n    } catch {\n      return dateTime.substring(0, 10) || ''; // Fallback pour format YYYY-MM-DD\n    }\n  }\n  /**\n   * Formate la durée en minutes vers le format \"Xh Ym\"\n   */\n  formatDuration(minutes) {\n    if (!minutes || minutes <= 0) return '0h 00m';\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins.toString().padStart(2, '0')}m`;\n  }\n  /**\n   * Initialise les filtres avec les valeurs par défaut\n   */\n  initializeFilters() {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice; // Commencer avec le prix maximum\n      // Initialiser les filtres de compagnies aériennes (tous désélectionnés au début)\n      this.filters.airlines = [];\n      // Initialiser les filtres d'escales (tous désélectionnés au début)\n      this.filters.stops = [];\n    } else {\n      // Valeurs par défaut si aucun vol\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n  }\n  /**\n   * Applique les filtres aux vols\n   */\n  applyFilters() {\n    this.filteredFlights = this.flights.filter(flight => {\n      // Filtre par prix (utiliser currentPrice au lieu de maxPrice)\n      if (flight.price.amount > this.filters.currentPrice) return false;\n      // Filtre par compagnie aérienne (si des compagnies sont sélectionnées)\n      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {\n        return false;\n      }\n      // Filtre par nombre d'escales (si des escales sont sélectionnées)\n      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {\n        return false;\n      }\n      return true;\n    });\n    this.sortFlights();\n    this.updatePagination();\n  }\n  /**\n   * Trie les vols selon les critères sélectionnés\n   */\n  sortFlights() {\n    this.filteredFlights.sort((a, b) => {\n      let comparison = 0;\n      switch (this.sortBy) {\n        case 'price':\n          comparison = a.price.amount - b.price.amount;\n          break;\n        case 'duration':\n          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);\n          break;\n        case 'departure':\n          comparison = a.departure.time.localeCompare(b.departure.time);\n          break;\n      }\n      return this.sortOrder === 'asc' ? comparison : -comparison;\n    });\n  }\n  /**\n   * Met à jour la pagination\n   */\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);\n    if (this.currentPage > this.totalPages) {\n      this.currentPage = 1;\n    }\n  }\n  /**\n   * Obtient les vols pour la page actuelle\n   */\n  getPaginatedFlights() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredFlights.slice(startIndex, endIndex);\n  }\n  /**\n   * Change de page\n   */\n  changePage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  /**\n   * Sélectionne un vol\n   */\n  selectFlight(flight) {\n    // Naviguer vers la page de détails ou de réservation\n    console.log('Vol sélectionné:', flight);\n    // TODO: Implémenter la navigation vers la page de réservation\n  }\n  /**\n   * Affiche les détails d'un vol\n   */\n  showFlightDetails(flight) {\n    console.log('Affichage des détails du vol:', flight);\n    // TODO: Ouvrir un modal avec les détails complets du vol\n    // ou naviguer vers une page de détails\n  }\n  /**\n   * Gère l'erreur de chargement du logo\n   */\n  onLogoError(flight) {\n    if (flight.airline.logo) {\n      // Essayer le logo alternatif suivant\n      const nextLogo = this.airlineLogoService.getNextLogo(flight.airline.code, flight.airline.logo);\n      if (nextLogo) {\n        flight.airline.logo = nextLogo;\n        console.log(`Tentative avec logo alternatif pour ${flight.airline.code}: ${nextLogo}`);\n        return;\n      }\n    }\n    // Aucun logo alternatif disponible\n    flight.airline.logoError = true;\n    console.log(`Aucun logo disponible pour ${flight.airline.code}`);\n  }\n  /**\n   * Gère le chargement réussi du logo\n   */\n  onLogoLoad(flight) {\n    flight.airline.logoError = false;\n  }\n  /**\n   * Retourne à la recherche\n   */\n  backToSearch() {\n    this.router.navigate(['/search-flight']);\n  }\n  /**\n   * Modifie la recherche\n   */\n  modifySearch() {\n    // Pré-remplir le formulaire avec les critères actuels\n    this.router.navigate(['/search-flight'], {\n      queryParams: {\n        modify: true,\n        searchData: JSON.stringify(this.searchSummary)\n      }\n    });\n  }\n  /**\n   * Formate le prix\n   */\n  formatPrice(amount) {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  }\n  /**\n   * Parse la durée en minutes\n   */\n  parseDuration(duration) {\n    const match = duration.match(/(\\d+)h\\s*(\\d+)m/);\n    if (match) {\n      return parseInt(match[1]) * 60 + parseInt(match[2]);\n    }\n    return 0;\n  }\n  /**\n   * Obtient le texte des passagers\n   */\n  getPassengerText() {\n    if (!this.searchSummary) return '';\n    const parts = [];\n    const p = this.searchSummary.passengers;\n    if (p.adults > 0) {\n      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);\n    }\n    if (p.children > 0) {\n      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);\n    }\n    if (p.infants > 0) {\n      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);\n    }\n    return parts.join(', ');\n  }\n  /**\n   * Obtient les compagnies aériennes uniques\n   */\n  getUniqueAirlines() {\n    const airlines = new Map();\n    this.flights.forEach(flight => {\n      airlines.set(flight.airline.code, flight.airline.name);\n    });\n    return Array.from(airlines.entries()).map(([code, name]) => ({\n      code,\n      name\n    }));\n  }\n  /**\n   * Toggle le filtre de compagnie aérienne\n   */\n  toggleAirlineFilter(airlineCode) {\n    const index = this.filters.airlines.indexOf(airlineCode);\n    if (index > -1) {\n      this.filters.airlines.splice(index, 1);\n    } else {\n      this.filters.airlines.push(airlineCode);\n    }\n    this.applyFilters();\n  }\n  /**\n   * Toggle le filtre d'escales\n   */\n  toggleStopsFilter(stops) {\n    const index = this.filters.stops.indexOf(stops);\n    if (index > -1) {\n      this.filters.stops.splice(index, 1);\n    } else {\n      this.filters.stops.push(stops);\n    }\n    this.applyFilters();\n  }\n  /**\n   * Efface tous les filtres\n   */\n  clearFilters() {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice;\n    } else {\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n    this.filters.airlines = [];\n    this.filters.stops = [];\n    this.filters.departureTime = {\n      min: 0,\n      max: 24\n    };\n    this.filters.duration = {\n      min: 0,\n      max: 24\n    };\n    this.applyFilters();\n  }\n  /**\n   * Toggle l'ordre de tri\n   */\n  toggleSortOrder() {\n    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    this.sortFlights();\n  }\n  /**\n   * Obtient les numéros de page pour la pagination\n   */\n  getPageNumbers() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  /**\n   * TrackBy function pour la performance\n   */\n  trackByFlightId(index, flight) {\n    return flight.id;\n  }\n  /**\n   * TrackBy function pour les segments\n   */\n  trackBySegmentIndex(index, segment) {\n    return index;\n  }\n  /**\n   * Gère l'erreur de chargement du logo d'un segment\n   */\n  onSegmentLogoError(segment) {\n    if (segment.airline.logo) {\n      // Essayer le logo alternatif suivant\n      const nextLogo = this.airlineLogoService.getNextLogo(segment.airline.code, segment.airline.logo);\n      if (nextLogo) {\n        segment.airline.logo = nextLogo;\n        console.log(`Tentative avec logo alternatif pour ${segment.airline.code}: ${nextLogo}`);\n        return;\n      }\n    }\n    // Aucun logo alternatif disponible\n    segment.airline.logoError = true;\n    console.log(`Aucun logo disponible pour ${segment.airline.code}`);\n  }\n  /**\n   * Gère le chargement réussi du logo d'un segment\n   */\n  onSegmentLogoLoad(segment) {\n    segment.airline.logoError = false;\n  }\n  /**\n   * Obtient le prix minimum pour le slider\n   */\n  getMinPrice() {\n    if (this.flights.length === 0) return 0;\n    return Math.min(...this.flights.map(f => f.price.amount));\n  }\n  /**\n   * Obtient le prix maximum pour le slider\n   */\n  getMaxPrice() {\n    if (this.flights.length === 0) return 1000;\n    return Math.max(...this.flights.map(f => f.price.amount));\n  }\n  /**\n   * Met à jour le prix courant du filtre\n   */\n  updateCurrentPrice(price) {\n    this.filters.currentPrice = price;\n    this.applyFilters();\n  }\n  /**\n   * Génère des résultats de test\n   */\n  generateMockResults() {\n    return {\n      body: {\n        flights: [{\n          id: 'flight-1',\n          airline: {\n            code: 'TK',\n            name: 'Turkish Airlines'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '08:30',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '11:45',\n            date: '2025-06-17'\n          },\n          duration: '3h 15m',\n          stops: 0,\n          price: {\n            amount: 450,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: true,\n          baggage: {\n            carryOn: '8kg',\n            checked: '23kg'\n          }\n        }, {\n          id: 'flight-2',\n          airline: {\n            code: 'TU',\n            name: 'Tunisair'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '14:20',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '17:35',\n            date: '2025-06-17'\n          },\n          duration: '3h 15m',\n          stops: 0,\n          price: {\n            amount: 380,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: false,\n          baggage: {\n            carryOn: '8kg',\n            checked: '20kg'\n          }\n        }, {\n          id: 'flight-3',\n          airline: {\n            code: 'AF',\n            name: 'Air France'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '10:15',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '16:30',\n            date: '2025-06-17'\n          },\n          duration: '6h 15m',\n          stops: 1,\n          price: {\n            amount: 520,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: true,\n          baggage: {\n            carryOn: '12kg',\n            checked: '23kg'\n          }\n        }, {\n          id: 'flight-4',\n          airline: {\n            code: 'LH',\n            name: 'Lufthansa'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '16:45',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '22:10',\n            date: '2025-06-17'\n          },\n          duration: '5h 25m',\n          stops: 1,\n          price: {\n            amount: 610,\n            currency: 'EUR'\n          },\n          class: 'Business',\n          refundable: true,\n          baggage: {\n            carryOn: '8kg',\n            checked: '32kg'\n          }\n        }, {\n          id: 'flight-5',\n          airline: {\n            code: 'EK',\n            name: 'Emirates'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '22:30',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '08:45',\n            date: '2025-06-18'\n          },\n          duration: '10h 15m',\n          stops: 1,\n          price: {\n            amount: 750,\n            currency: 'EUR'\n          },\n          class: 'Business',\n          refundable: true,\n          baggage: {\n            carryOn: '7kg',\n            checked: '30kg'\n          }\n        }]\n      }\n    };\n  }\n  static {\n    this.ɵfac = function FlightResultsComponent_Factory(t) {\n      return new (t || FlightResultsComponent)(i0.ɵɵdirectiveInject(i1.FlightSearchService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AirlineLogoService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightResultsComponent,\n      selectors: [[\"app-flight-results\"]],\n      decls: 89,\n      vars: 26,\n      consts: [[1, \"flight-results-container\"], [1, \"results-header\"], [1, \"header-content\"], [\"class\", \"search-summary\", 4, \"ngIf\"], [1, \"results-content\"], [1, \"filters-sidebar\"], [1, \"filters-header\"], [1, \"btn-toggle\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"d\", \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"], [1, \"filter-section\"], [1, \"price-filter\"], [1, \"price-range-info\"], [1, \"price-min\"], [1, \"price-max\"], [\"type\", \"range\", 1, \"price-slider\", 3, \"ngModel\", \"min\", \"max\", \"step\", \"ngModelChange\", \"input\"], [1, \"price-current\"], [1, \"price-label\"], [1, \"price-value\"], [1, \"airline-filters\"], [\"class\", \"checkbox-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"stops-filters\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", \"value\", \"0\", 3, \"checked\", \"change\"], [1, \"checkmark\"], [\"type\", \"checkbox\", \"value\", \"1\", 3, \"checked\", \"change\"], [\"type\", \"checkbox\", \"value\", \"2\", 3, \"checked\", \"change\"], [1, \"filter-actions\"], [1, \"btn-clear\", 3, \"click\"], [1, \"results-main\"], [1, \"results-toolbar\"], [1, \"toolbar-left\"], [1, \"btn-filter-toggle\", 3, \"click\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h10V6H3zm0 7h8v-2H3v2zm16-1v8h2v-8h-2zm-3-3h2V6h-2v3zm3-3v3h2V6h-2z\"], [1, \"view-toggle\"], [1, \"view-btn\", 3, \"click\"], [\"d\", \"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z\"], [\"d\", \"M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z\"], [1, \"toolbar-right\"], [1, \"sort-controls\"], [3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [1, \"sort-order-btn\", 3, \"click\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\", 4, \"ngIf\"], [\"d\", \"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\", 4, \"ngIf\"], [1, \"results-info\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [\"class\", \"flights-container\", 3, \"grid-view\", 4, \"ngIf\"], [\"class\", \"pagination\", 4, \"ngIf\"], [1, \"search-summary\"], [1, \"route-info\"], [1, \"route-main\"], [1, \"airport-code\"], [1, \"route-arrow\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 4, \"ngIf\"], [1, \"route-details\"], [1, \"date\"], [\"class\", \"date\", 4, \"ngIf\"], [1, \"passengers\"], [1, \"results-count\"], [1, \"count\"], [1, \"search-time\"], [1, \"header-actions\"], [1, \"btn-secondary\", 3, \"click\"], [\"d\", \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"], [1, \"btn-primary\", 3, \"click\"], [\"d\", \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\", \"transform\", \"rotate(180 12 12)\"], [\"type\", \"checkbox\", 3, \"value\", \"checked\", \"change\"], [1, \"airline-name\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\"], [\"d\", \"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\"], [1, \"loading-state\"], [1, \"loading-spinner\"], [1, \"error-state\"], [1, \"error-icon\"], [\"d\", \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"], [1, \"flights-container\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"flight-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"flight-card\", 3, \"click\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"class\", \"airline-logo\", 3, \"src\", \"alt\", \"error\", \"load\", 4, \"ngIf\"], [\"class\", \"airline-placeholder\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"flight-number\"], [1, \"aircraft-type\"], [1, \"flight-badges\"], [\"class\", \"badge direct\", 4, \"ngIf\"], [\"class\", \"badge refundable\", 4, \"ngIf\"], [\"class\", \"badge economy\", 4, \"ngIf\"], [\"class\", \"badge business\", 4, \"ngIf\"], [\"class\", \"badge first\", 4, \"ngIf\"], [1, \"flight-details\"], [\"class\", \"flight-route single-segment\", 4, \"ngIf\"], [\"class\", \"flight-route multi-segment\", 4, \"ngIf\"], [1, \"flight-price\"], [1, \"price-container\"], [1, \"price-amount\"], [1, \"price-details\"], [1, \"price-taxes\"], [1, \"action-buttons\"], [1, \"btn-details\", 3, \"click\"], [1, \"btn-select\"], [1, \"flight-baggage\"], [1, \"baggage-info\"], [1, \"baggage-item\", \"carry-on\"], [\"d\", \"M8.5 6h7l1.5 9H7l1.5-9zM10 4V2h4v2h-4z\"], [1, \"baggage-details\"], [1, \"baggage-type\"], [1, \"baggage-allowance\"], [1, \"baggage-item\", \"checked\"], [\"d\", \"M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z\"], [1, \"flight-extras\"], [\"class\", \"extra-item\", 4, \"ngIf\"], [1, \"extra-item\"], [\"viewBox\", \"0 0 16 16\", \"fill\", \"currentColor\"], [\"d\", \"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z\"], [1, \"airline-logo\", 3, \"src\", \"alt\", \"error\", \"load\"], [1, \"airline-placeholder\"], [1, \"badge\", \"direct\"], [\"d\", \"M8 0L6.5 1.5L10 5H3v2h7l-3.5 3.5L8 12l6-6L8 0z\"], [1, \"badge\", \"refundable\"], [\"d\", \"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"], [\"d\", \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"], [1, \"badge\", \"economy\"], [1, \"badge\", \"business\"], [1, \"badge\", \"first\"], [1, \"flight-route\", \"single-segment\"], [1, \"departure\"], [1, \"time\"], [1, \"airport\"], [1, \"city\"], [1, \"flight-info\"], [1, \"duration\"], [1, \"route-line\"], [1, \"line\"], [\"class\", \"stops\", 4, \"ngIf\"], [1, \"plane-icon\"], [1, \"arrival\"], [1, \"stops\"], [1, \"flight-route\", \"multi-segment\"], [1, \"segments-container\"], [\"class\", \"segment\", 3, \"outbound\", \"return\", \"multicity-segment\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"total-duration\"], [1, \"total-label\"], [1, \"total-time\"], [1, \"segment\"], [1, \"segment-header\"], [\"class\", \"segment-label outbound-label\", 4, \"ngIf\"], [\"class\", \"segment-label return-label\", 4, \"ngIf\"], [\"class\", \"segment-label multicity-label\", 4, \"ngIf\"], [1, \"segment-date\"], [1, \"segment-route\"], [\"class\", \"airline-logo-small\", 3, \"src\", \"alt\", \"error\", \"load\", 4, \"ngIf\"], [\"class\", \"airline-code-small\", 4, \"ngIf\"], [1, \"segment-label\", \"outbound-label\"], [\"d\", \"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"], [1, \"segment-label\", \"return-label\"], [1, \"segment-label\", \"multicity-label\"], [1, \"airline-logo-small\", 3, \"src\", \"alt\", \"error\", \"load\"], [1, \"airline-code-small\"], [1, \"pagination\"], [1, \"page-btn\", 3, \"disabled\", \"click\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [1, \"page-numbers\"], [\"class\", \"page-number\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [1, \"page-number\", 3, \"click\"]],\n      template: function FlightResultsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, FlightResultsComponent_div_3_Template, 31, 8, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"aside\", 5)(6, \"div\", 6)(7, \"h3\");\n          i0.ɵɵtext(8, \"Filtres\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_9_listener() {\n            return ctx.showFilters = !ctx.showFilters;\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 8);\n          i0.ɵɵelement(11, \"path\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"h4\");\n          i0.ɵɵtext(14, \"Prix maximum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"span\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 14);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function FlightResultsComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.filters.currentPrice = $event;\n          })(\"input\", function FlightResultsComponent_Template_input_input_21_listener($event) {\n            return ctx.updateCurrentPrice($event.target.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"span\", 17);\n          i0.ɵɵtext(24, \"Prix maximum:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 18);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 10)(28, \"h4\");\n          i0.ɵɵtext(29, \"Compagnies a\\u00E9riennes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 19);\n          i0.ɵɵtemplate(31, FlightResultsComponent_label_31_Template, 5, 3, \"label\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 10)(33, \"h4\");\n          i0.ɵɵtext(34, \"Nombre d'escales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 21)(36, \"label\", 22)(37, \"input\", 23);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_37_listener() {\n            return ctx.toggleStopsFilter(0);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"span\", 24);\n          i0.ɵɵelementStart(39, \"span\");\n          i0.ɵɵtext(40, \"Vol direct\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"label\", 22)(42, \"input\", 25);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_42_listener() {\n            return ctx.toggleStopsFilter(1);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"span\", 24);\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"1 escale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"label\", 22)(47, \"input\", 26);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_47_listener() {\n            return ctx.toggleStopsFilter(2);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"span\", 24);\n          i0.ɵɵelementStart(49, \"span\");\n          i0.ɵɵtext(50, \"2+ escales\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 27)(52, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_52_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵtext(53, \"Effacer les filtres\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"main\", 29)(55, \"div\", 30)(56, \"div\", 31)(57, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_57_listener() {\n            return ctx.showFilters = !ctx.showFilters;\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(58, \"svg\", 8);\n          i0.ɵɵelement(59, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Filtres \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(61, \"div\", 34)(62, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_62_listener() {\n            return ctx.viewMode = \"list\";\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 8);\n          i0.ɵɵelement(64, \"path\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(65, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_65_listener() {\n            return ctx.viewMode = \"grid\";\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(66, \"svg\", 8);\n          i0.ɵɵelement(67, \"path\", 37);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(68, \"div\", 38)(69, \"div\", 39)(70, \"label\");\n          i0.ɵɵtext(71, \"Trier par:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"select\", 40);\n          i0.ɵɵlistener(\"ngModelChange\", function FlightResultsComponent_Template_select_ngModelChange_72_listener($event) {\n            return ctx.sortBy = $event;\n          })(\"change\", function FlightResultsComponent_Template_select_change_72_listener() {\n            ctx.sortFlights();\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(73, \"option\", 41);\n          i0.ɵɵtext(74, \"Prix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"option\", 42);\n          i0.ɵɵtext(76, \"Dur\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"option\", 43);\n          i0.ɵɵtext(78, \"Heure de d\\u00E9part\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_79_listener() {\n            return ctx.toggleSortOrder();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(80, \"svg\", 8);\n          i0.ɵɵtemplate(81, FlightResultsComponent__svg_path_81_Template, 1, 0, \"path\", 45);\n          i0.ɵɵtemplate(82, FlightResultsComponent__svg_path_82_Template, 1, 0, \"path\", 46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(83, \"span\", 47);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(85, FlightResultsComponent_div_85_Template, 4, 0, \"div\", 48);\n          i0.ɵɵtemplate(86, FlightResultsComponent_div_86_Template, 10, 1, \"div\", 49);\n          i0.ɵɵtemplate(87, FlightResultsComponent_div_87_Template, 3, 5, \"div\", 50);\n          i0.ɵɵtemplate(88, FlightResultsComponent_div_88_Template, 11, 3, \"div\", 51);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchSummary);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showFilters);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getMinPrice(), \"\\u20AC\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getMaxPrice(), \"\\u20AC\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngModel\", ctx.filters.currentPrice)(\"min\", ctx.getMinPrice())(\"max\", ctx.getMaxPrice())(\"step\", 10);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.filters.currentPrice, \"\\u20AC\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getUniqueAirlines());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(2));\n          i0.ɵɵadvance(15);\n          i0.ɵɵclassProp(\"active\", ctx.viewMode === \"list\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.viewMode === \"grid\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.sortBy);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.sortOrder === \"asc\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.sortOrder === \"desc\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.filteredFlights.length, \" r\\u00E9sultats \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.RangeValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".flight-results-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n\\n\\n.results-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 2rem 0;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n}\\n\\n.search-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 2rem;\\n}\\n\\n.route-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n}\\n\\n.route-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.airport-code[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 0.5rem 1rem;\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.route-arrow[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.route-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  opacity: 0.8;\\n}\\n\\n.route-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.count[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.search-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  opacity: 0.8;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: none;\\n  font-size: 0.9rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2c5aa0;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.results-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  gap: 2rem;\\n  padding: 2rem;\\n}\\n\\n\\n\\n.filters-sidebar[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background: white;\\n  border-radius: 20px;\\n  padding: 2rem;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.filters-sidebar.hidden[_ngcontent-%COMP%] {\\n  transform: translateX(-100%);\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f7fafc;\\n}\\n\\n.filters-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n}\\n\\n.btn-toggle[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-toggle[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  color: #2d3748;\\n}\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.filter-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #4a5568;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n\\n.price-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.price-range-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.8rem;\\n  color: #718096;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.price-min[_ngcontent-%COMP%], .price-max[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.price-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  border-radius: 3px;\\n  background: linear-gradient(90deg, #e2e8f0 0%, #2c5aa0 50%, #e2e8f0 100%);\\n  outline: none;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.price-slider[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #cbd5e0 0%, #2c5aa0 50%, #cbd5e0 100%);\\n}\\n\\n.price-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  appearance: none;\\n  width: 22px;\\n  height: 22px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);\\n  cursor: pointer;\\n  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.4);\\n  border: 2px solid white;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n}\\n\\n.price-slider[_ngcontent-%COMP%]::-webkit-slider-thumb:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 16px rgba(44, 90, 160, 0.5);\\n}\\n\\n.price-current[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\\n  border-radius: 12px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.price-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 500;\\n}\\n\\n.price-value[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2c5aa0;\\n  font-size: 1.1rem;\\n  background: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-filters[_ngcontent-%COMP%], .stops-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  accent-color: #2c5aa0;\\n  cursor: pointer;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 4px;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.airline-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #4a5568;\\n}\\n\\n.btn-clear[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  background: transparent;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 12px;\\n  color: #718096;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-clear[_ngcontent-%COMP%]:hover {\\n  border-color: #dc3545;\\n  color: #dc3545;\\n}\\n\\n\\n\\n.results-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.results-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 1.5rem;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 12px;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  border-color: #cbd5e0;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  padding: 4px;\\n}\\n\\n.view-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: none;\\n  background: transparent;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.view-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #2c5aa0;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.view-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.toolbar-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  color: #2c5aa0;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.results-info[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 2rem;\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #2c5aa0;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 1rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .no-results-icon[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  margin: 0 auto 1rem;\\n  color: #e53e3e;\\n}\\n\\n.no-results-icon[_ngcontent-%COMP%] {\\n  color: #718096;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #2d3748;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 2rem 0;\\n  color: #718096;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.flights-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.flights-container.grid-view[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.flight-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n}\\n\\n.flight-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n  border-color: #6366f1;\\n}\\n\\n.flight-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.5rem 1.5rem 1rem 1.5rem;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.airline-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.airline-logo-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.airline-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.airline-logo[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 6px;\\n  object-fit: contain;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.airline-placeholder[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);\\n  color: white;\\n  border-radius: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.75rem;\\n  border: 1px solid #e2e8f0;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.2s ease;\\n}\\n\\n.airline-placeholder[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n}\\n\\n.airline-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n  font-size: 0.9rem;\\n}\\n\\n.flight-number[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.aircraft-type[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #94a3b8;\\n}\\n\\n.flight-badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 6px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.3px;\\n}\\n\\n.badge[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.badge.direct[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #166534;\\n  border: 1px solid #bbf7d0;\\n}\\n\\n.badge.refundable[_ngcontent-%COMP%] {\\n  background: #dbeafe;\\n  color: #1e40af;\\n  border: 1px solid #bfdbfe;\\n}\\n\\n.badge.economy[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #475569;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.badge.business[_ngcontent-%COMP%] {\\n  background: #fef3c7;\\n  color: #92400e;\\n  border: 1px solid #fde68a;\\n}\\n\\n.badge.first[_ngcontent-%COMP%] {\\n  background: #fce7f3;\\n  color: #be185d;\\n  border: 1px solid #f9a8d4;\\n}\\n\\n.flight-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 2rem;\\n  padding: 1.5rem;\\n}\\n\\n.flight-route[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 2rem;\\n}\\n\\n.departure[_ngcontent-%COMP%], .arrival[_ngcontent-%COMP%] {\\n  text-align: center;\\n  min-width: 80px;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.airport[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: #64748b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.city[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #94a3b8;\\n}\\n\\n.flight-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n\\n.duration[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #4a5568;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.route-line[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.line[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, #e2e8f0 0%, #2c5aa0 50%, #e2e8f0 100%);\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.stops[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -1.5rem;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 0.75rem;\\n  color: #718096;\\n  background: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.plane-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 24px;\\n  height: 24px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #2c5aa0;\\n  border: 2px solid #e2e8f0;\\n}\\n\\n.plane-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.flight-price[_ngcontent-%COMP%] {\\n  text-align: right;\\n  min-width: 140px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 1rem;\\n}\\n\\n.price-container[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.price-amount[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.price-details[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.price-taxes[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #94a3b8;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  width: 100%;\\n}\\n\\n.btn-details[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #6366f1;\\n  border: 1px solid #6366f1;\\n  padding: 0.5rem 1rem;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-details[_ngcontent-%COMP%]:hover {\\n  background: #6366f1;\\n  color: white;\\n}\\n\\n.btn-select[_ngcontent-%COMP%] {\\n  background: #6366f1;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-select[_ngcontent-%COMP%]:hover {\\n  background: #4f46e5;\\n  transform: translateY(-1px);\\n}\\n\\n.flight-baggage[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #e2e8f0;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.baggage-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  flex-wrap: wrap;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  flex: 1;\\n  min-width: 150px;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: #64748b;\\n}\\n\\n.baggage-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.baggage-type[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n\\n.baggage-allowance[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n}\\n\\n.carry-on[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n\\n.checked[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n}\\n\\n.flight-extras[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n\\n.extra-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.25rem 0.75rem;\\n  background: white;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: #374151;\\n}\\n\\n.extra-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n  color: #059669;\\n}\\n\\n\\n\\n.pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-top: 3rem;\\n  padding: 2rem;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 12px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #f7fafc;\\n  border-color: #2c5aa0;\\n  color: #2c5aa0;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.page-numbers[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.page-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.page-number.active[_ngcontent-%COMP%] {\\n  background: #2c5aa0;\\n  color: white;\\n  border-color: #2c5aa0;\\n}\\n\\n.page-number[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #f7fafc;\\n  border-color: #2c5aa0;\\n  color: #2c5aa0;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .results-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .filters-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    order: 2;\\n  }\\n  \\n  .results-main[_ngcontent-%COMP%] {\\n    order: 1;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .search-summary[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 1rem;\\n  }\\n  \\n  .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  \\n  .btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  \\n  .results-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .toolbar-left[_ngcontent-%COMP%], .toolbar-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  \\n  .flight-details[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .flight-route[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .flights-container.grid-view[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .pagination[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  \\n  .page-numbers[_ngcontent-%COMP%] {\\n    order: -1;\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r9", "searchSummary", "returnDate", "ɵɵnamespaceSVG", "ɵɵtemplate", "FlightResultsComponent_div_3__svg_svg_8_Template", "ɵɵnamespaceHTML", "FlightResultsComponent_div_3_span_14_Template", "ɵɵlistener", "FlightResultsComponent_div_3_Template_button_click_23_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "modifySearch", "FlightResultsComponent_div_3_Template_button_click_27_listener", "ctx_r12", "backToSearch", "ɵɵtextInterpolate", "ctx_r0", "from", "ɵɵproperty", "searchType", "to", "departureDate", "getPassengerText", "totalResults", "searchTime", "FlightResultsComponent_label_31_Template_input_change_1_listener", "restoredCtx", "_r15", "airline_r13", "$implicit", "ctx_r14", "toggleAirlineFilter", "code", "ctx_r1", "filters", "airlines", "includes", "name", "FlightResultsComponent_div_86_Template_button_click_8_listener", "_r17", "ctx_r16", "ctx_r5", "error", "FlightResultsComponent_div_87_div_1_Template_button_click_8_listener", "_r21", "ctx_r20", "clearFilters", "FlightResultsComponent_div_87_div_2_img_4_Template_img_error_0_listener", "_r35", "flight_r22", "ctx_r33", "onLogoError", "FlightResultsComponent_div_87_div_2_img_4_Template_img_load_0_listener", "ctx_r36", "onLogoLoad", "airline", "logo", "ɵɵsanitizeUrl", "ɵɵtextInterpolate2", "stops", "FlightResultsComponent_div_87_div_2_div_20_div_13_Template", "departure", "time", "airport", "city", "duration", "arrival", "i_r45", "FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template_img_error_0_listener", "_r55", "segment_r44", "ctx_r53", "onSegmentLogoError", "FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template_img_load_0_listener", "ctx_r56", "onSegmentLogoLoad", "FlightResultsComponent_div_87_div_2_div_21_div_2_span_2_Template", "FlightResultsComponent_div_87_div_2_div_21_div_2_span_3_Template", "FlightResultsComponent_div_87_div_2_div_21_div_2_span_4_Template", "FlightResultsComponent_div_87_div_2_div_21_div_2_img_17_Template", "FlightResultsComponent_div_87_div_2_div_21_div_2_div_18_Template", "FlightResultsComponent_div_87_div_2_div_21_div_2_div_25_Template", "ɵɵclassProp", "date", "logoError", "flightNumber", "FlightResultsComponent_div_87_div_2_div_21_div_2_Template", "segments", "ctx_r31", "trackBySegmentIndex", "totalDuration", "FlightResultsComponent_div_87_div_2_Template_div_click_0_listener", "_r64", "ctx_r63", "selectFlight", "FlightResultsComponent_div_87_div_2_img_4_Template", "FlightResultsComponent_div_87_div_2_div_5_Template", "FlightResultsComponent_div_87_div_2_span_14_Template", "FlightResultsComponent_div_87_div_2_span_15_Template", "FlightResultsComponent_div_87_div_2_span_16_Template", "FlightResultsComponent_div_87_div_2_span_17_Template", "FlightResultsComponent_div_87_div_2_span_18_Template", "FlightResultsComponent_div_87_div_2_div_20_Template", "FlightResultsComponent_div_87_div_2_div_21_Template", "FlightResultsComponent_div_87_div_2_Template_button_click_31_listener", "$event", "ctx_r65", "stopPropagation", "showFlightDetails", "FlightResultsComponent_div_87_div_2_span_54_Template", "id", "split", "refundable", "class", "price", "formatted", "baggage", "carryOn", "checked", "FlightResultsComponent_div_87_div_1_Template", "FlightResultsComponent_div_87_div_2_Template", "ctx_r6", "viewMode", "filteredFlights", "length", "getPaginatedFlights", "trackByFlightId", "FlightResultsComponent_div_88_button_6_Template_button_click_0_listener", "_r69", "page_r67", "ctx_r68", "changePage", "ctx_r66", "currentPage", "FlightResultsComponent_div_88_Template_button_click_1_listener", "_r71", "ctx_r70", "FlightResultsComponent_div_88_button_6_Template", "FlightResultsComponent_div_88_Template_button_click_7_listener", "ctx_r72", "ctx_r7", "getPageNumbers", "totalPages", "FlightResultsComponent", "constructor", "flightSearchService", "router", "route", "airlineLogoService", "destroy$", "loading", "flights", "minPrice", "maxPrice", "currentPrice", "departureTime", "min", "max", "sortBy", "sortOrder", "showFilters", "itemsPerPage", "ngOnInit", "loadSearchResults", "initializeFilters", "ngOnDestroy", "next", "complete", "queryParams", "pipe", "subscribe", "params", "searchData", "JSON", "parse", "decodeURIComponent", "performSearch", "console", "navigate", "setTimeout", "processSearchResults", "generateMockResults", "response", "type", "departureLocation", "arrivalLocation", "passengers", "Date", "toLocaleTimeString", "extractFlights", "applyFilters", "updatePagination", "body", "for<PERSON>ach", "flight", "index", "flightResult", "createFlightResult", "push", "items", "map", "item", "createFlightSegment", "calculateTotalDuration", "extractPrice", "offers", "mainSegment", "finalSegment", "isRefundable", "changeable", "isChangeable", "calculateTotalStops", "internationalCode", "logoFull", "getAirlineLogo", "formatTime", "flightDate", "formatDate", "formatDuration", "stopCount", "flightNo", "aircraft", "extractBaggage", "baggageInformations", "firstDeparture", "lastArrival", "totalMinutes", "Math", "floor", "getTime", "reduce", "total", "segment", "amount", "currency", "formatPrice", "offer", "totalPrice", "baggageInfos", "baggageType", "weight", "piece", "unitType", "dateTime", "hour", "minute", "substring", "toLocaleDateString", "minutes", "hours", "mins", "toString", "padStart", "prices", "f", "filter", "sortFlights", "sort", "a", "b", "comparison", "parseDuration", "localeCompare", "ceil", "startIndex", "endIndex", "slice", "page", "log", "nextLogo", "getNextLogo", "modify", "stringify", "Intl", "NumberFormat", "style", "format", "match", "parseInt", "parts", "p", "adults", "children", "infants", "join", "getUniqueAirlines", "Map", "set", "Array", "entries", "airlineCode", "indexOf", "splice", "toggleStopsFilter", "toggleSortOrder", "pages", "maxVisible", "start", "end", "i", "getMinPrice", "getMaxPrice", "updateCurrentPrice", "ɵɵdirectiveInject", "i1", "FlightSearchService", "i2", "Router", "ActivatedRoute", "i3", "AirlineLogoService", "selectors", "decls", "vars", "consts", "template", "FlightResultsComponent_Template", "rf", "ctx", "FlightResultsComponent_div_3_Template", "FlightResultsComponent_Template_button_click_9_listener", "FlightResultsComponent_Template_input_ngModelChange_21_listener", "FlightResultsComponent_Template_input_input_21_listener", "target", "value", "FlightResultsComponent_label_31_Template", "FlightResultsComponent_Template_input_change_37_listener", "FlightResultsComponent_Template_input_change_42_listener", "FlightResultsComponent_Template_input_change_47_listener", "FlightResultsComponent_Template_button_click_52_listener", "FlightResultsComponent_Template_button_click_57_listener", "FlightResultsComponent_Template_button_click_62_listener", "FlightResultsComponent_Template_button_click_65_listener", "FlightResultsComponent_Template_select_ngModelChange_72_listener", "FlightResultsComponent_Template_select_change_72_listener", "FlightResultsComponent_Template_button_click_79_listener", "FlightResultsComponent__svg_path_81_Template", "FlightResultsComponent__svg_path_82_Template", "FlightResultsComponent_div_85_Template", "FlightResultsComponent_div_86_Template", "FlightResultsComponent_div_87_Template", "FlightResultsComponent_div_88_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-results\\flight-results.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-results\\flight-results.component.html"], "sourcesContent": ["import { Component, OnIni<PERSON>, On<PERSON>estroy } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FlightSearchService } from '../../services/flight-search.service';\nimport { AirlineLogoService } from '../../services/airline-logo.service';\nimport { OneWayResponse } from '../../models/oneway-response.interface';\nimport { RoundTripResponse } from '../../models/roundtrip-response.interface';\nimport { MulticityResponse } from '../../models/multicity-response.interface';\n\nexport interface FlightSegment {\n  airline: {\n    code: string;\n    name: string;\n    logo?: string;\n    logoError?: boolean;\n  };\n  departure: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  arrival: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  duration: string;\n  stops: number;\n  flightNumber: string;\n  aircraft?: string;\n  baggage?: {\n    carryOn: string;\n    checked: string;\n  };\n}\n\nexport interface FlightResult {\n  id: string;\n  searchType: 'oneway' | 'roundtrip' | 'multicity';\n  segments: FlightSegment[];\n  totalDuration: string;\n  price: {\n    amount: number;\n    currency: string;\n    formatted: string;\n  };\n  class: string;\n  refundable: boolean;\n  changeable?: boolean;\n  // Pour compatibilité avec le code existant (segment principal)\n  airline: {\n    code: string;\n    name: string;\n    logo?: string;\n    logoError?: boolean;\n  };\n  departure: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  arrival: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  duration: string;\n  stops: number;\n  baggage?: {\n    carryOn: string;\n    checked: string;\n  };\n}\n\nexport interface SearchSummary {\n  searchType: 'oneway' | 'roundtrip' | 'multicity';\n  from: string;\n  to: string;\n  departureDate: string;\n  returnDate?: string;\n  passengers: {\n    adults: number;\n    children: number;\n    infants: number;\n  };\n  totalResults: number;\n  searchTime: string;\n}\n\n@Component({\n  selector: 'app-flight-results',\n  templateUrl: './flight-results.component.html',\n  styleUrls: ['./flight-results.component.css']\n})\nexport class FlightResultsComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // État du composant\n  loading = false;\n  error: string | null = null;\n  \n  // Données de recherche\n  searchSummary: SearchSummary | null = null;\n  flights: FlightResult[] = [];\n  filteredFlights: FlightResult[] = [];\n  \n  // Filtres et tri\n  filters = {\n    minPrice: 0,\n    maxPrice: 0,\n    currentPrice: 0,\n    airlines: [] as string[],\n    stops: [] as number[],\n    departureTime: { min: 0, max: 24 },\n    duration: { min: 0, max: 24 }\n  };\n  \n  sortBy = 'price'; // price, duration, departure\n  sortOrder = 'asc'; // asc, desc\n  \n  // Options d'affichage\n  viewMode = 'list'; // list, grid\n  showFilters = true;\n  \n  // Pagination\n  currentPage = 1;\n  itemsPerPage = 10;\n  totalPages = 1;\n\n  constructor(\n    private flightSearchService: FlightSearchService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private airlineLogoService: AirlineLogoService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadSearchResults();\n    this.initializeFilters();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Charge les résultats de recherche depuis le service\n   */\n  private loadSearchResults(): void {\n    // Récupérer les paramètres de recherche depuis l'URL ou le service\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['searchData']) {\n        try {\n          const searchData = JSON.parse(decodeURIComponent(params['searchData']));\n          this.performSearch(searchData);\n        } catch (error) {\n          console.error('Erreur lors du parsing des données de recherche:', error);\n          this.router.navigate(['/search-flight']);\n        }\n      } else {\n        // Rediriger vers la page de recherche si aucune donnée\n        this.router.navigate(['/search-flight']);\n      }\n    });\n  }\n\n  /**\n   * Effectue la recherche de vols\n   */\n  private performSearch(searchData: any): void {\n    this.loading = true;\n    this.error = null;\n    \n    // Simuler une recherche pour le moment\n    setTimeout(() => {\n      this.processSearchResults(this.generateMockResults(), searchData);\n      this.loading = false;\n    }, 2000);\n  }\n\n  /**\n   * Traite les résultats de recherche\n   */\n  private processSearchResults(response: any, searchData: any): void {\n    // Créer le résumé de recherche\n    this.searchSummary = {\n      searchType: searchData.type,\n      from: searchData.params.departureLocation,\n      to: searchData.params.arrivalLocation,\n      departureDate: searchData.params.departureDate,\n      returnDate: searchData.params.returnDate,\n      passengers: searchData.params.passengers,\n      totalResults: 0,\n      searchTime: new Date().toLocaleTimeString()\n    };\n\n    // Traiter les vols selon le type de réponse\n    this.flights = this.extractFlights(response, searchData.type);\n    this.searchSummary.totalResults = this.flights.length;\n\n    // Appliquer les filtres initiaux\n    this.applyFilters();\n    this.updatePagination();\n  }\n\n  /**\n   * Extrait les vols de la réponse API selon le type de recherche\n   */\n  private extractFlights(response: any, searchType: 'oneway' | 'roundtrip' | 'multicity'): FlightResult[] {\n    const flights: FlightResult[] = [];\n\n    if (!response.body?.flights) {\n      return flights;\n    }\n\n    response.body.flights.forEach((flight: any, index: number) => {\n      try {\n        const flightResult = this.createFlightResult(flight, searchType, index);\n        if (flightResult) {\n          flights.push(flightResult);\n        }\n      } catch (error) {\n        console.error('Erreur lors du traitement du vol:', error, flight);\n      }\n    });\n\n    return flights;\n  }\n\n  /**\n   * Crée un FlightResult à partir des données de l'API\n   */\n  private createFlightResult(flight: any, searchType: 'oneway' | 'roundtrip' | 'multicity', index: number): FlightResult | null {\n    if (!flight.items || flight.items.length === 0) {\n      return null;\n    }\n\n    // Extraire tous les segments\n    const segments: FlightSegment[] = flight.items.map((item: any) => this.createFlightSegment(item));\n\n    // Calculer la durée totale\n    const totalDuration = this.calculateTotalDuration(segments);\n\n    // Obtenir le prix depuis les offres\n    const price = this.extractPrice(flight.offers);\n\n    // Segment principal (premier segment pour compatibilité)\n    const mainSegment = segments[0];\n\n    // Segment final (dernier segment pour l'arrivée finale)\n    const finalSegment = segments[segments.length - 1];\n\n    return {\n      id: flight.id || `flight-${index}`,\n      searchType,\n      segments,\n      totalDuration,\n      price,\n      class: mainSegment.airline.name || 'Economy',\n      refundable: this.isRefundable(flight),\n      changeable: this.isChangeable(flight),\n\n      // Propriétés de compatibilité (basées sur le segment principal)\n      airline: mainSegment.airline,\n      departure: mainSegment.departure,\n      arrival: finalSegment.arrival,\n      duration: totalDuration,\n      stops: this.calculateTotalStops(segments),\n      baggage: mainSegment.baggage\n    };\n  }\n\n  /**\n   * Crée un segment de vol à partir des données de l'API\n   */\n  private createFlightSegment(item: any): FlightSegment {\n    const airline = {\n      code: item.airline?.internationalCode || item.airline?.code || 'XX',\n      name: item.airline?.name || 'Compagnie inconnue',\n      logo: item.airline?.logo || item.airline?.logoFull || this.airlineLogoService.getAirlineLogo(item.airline?.internationalCode || item.airline?.code || ''),\n      logoError: false\n    };\n\n    return {\n      airline,\n      departure: {\n        airport: item.departure?.airport?.code || item.departure?.code || '',\n        city: item.departure?.airport?.city || item.departure?.city || '',\n        time: this.formatTime(item.departure?.time || item.flightDate),\n        date: this.formatDate(item.departure?.date || item.flightDate)\n      },\n      arrival: {\n        airport: item.arrival?.airport?.code || item.arrival?.code || '',\n        city: item.arrival?.airport?.city || item.arrival?.city || '',\n        time: this.formatTime(item.arrival?.time),\n        date: this.formatDate(item.arrival?.date)\n      },\n      duration: this.formatDuration(item.duration),\n      stops: item.stopCount || 0,\n      flightNumber: item.flightNo || '',\n      aircraft: item.aircraft || '',\n      baggage: this.extractBaggage(item.baggageInformations)\n    };\n  }\n\n  /**\n   * Calcule la durée totale de tous les segments\n   */\n  private calculateTotalDuration(segments: FlightSegment[]): string {\n    if (segments.length === 0) return '0h 00m';\n\n    // Pour un seul segment, retourner sa durée\n    if (segments.length === 1) {\n      return segments[0].duration;\n    }\n\n    // Pour plusieurs segments, calculer le temps total entre le départ du premier et l'arrivée du dernier\n    const firstDeparture = new Date(`${segments[0].departure.date}T${segments[0].departure.time}`);\n    const lastArrival = new Date(`${segments[segments.length - 1].arrival.date}T${segments[segments.length - 1].arrival.time}`);\n\n    const totalMinutes = Math.floor((lastArrival.getTime() - firstDeparture.getTime()) / (1000 * 60));\n    return this.formatDuration(totalMinutes);\n  }\n\n  /**\n   * Calcule le nombre total d'escales\n   */\n  private calculateTotalStops(segments: FlightSegment[]): number {\n    return segments.reduce((total, segment) => total + segment.stops, 0);\n  }\n\n  /**\n   * Extrait le prix depuis les offres\n   */\n  private extractPrice(offers: any[]): { amount: number; currency: string; formatted: string } {\n    if (!offers || offers.length === 0) {\n      return { amount: 0, currency: 'EUR', formatted: this.formatPrice(0) };\n    }\n\n    // Prendre la première offre ou la moins chère\n    const offer = offers[0];\n    const amount = offer.price?.amount || offer.totalPrice || 0;\n    const currency = offer.price?.currency || offer.currency || 'EUR';\n\n    return {\n      amount,\n      currency,\n      formatted: this.formatPrice(amount)\n    };\n  }\n\n  /**\n   * Vérifie si le vol est remboursable\n   */\n  private isRefundable(flight: any): boolean {\n    // Logique à adapter selon la structure des données\n    return flight.refundable || false;\n  }\n\n  /**\n   * Vérifie si le vol est modifiable\n   */\n  private isChangeable(flight: any): boolean {\n    // Logique à adapter selon la structure des données\n    return flight.changeable || false;\n  }\n\n  /**\n   * Extrait les informations de bagages\n   */\n  private extractBaggage(baggageInfos: any[]): { carryOn: string; checked: string } | undefined {\n    if (!baggageInfos || baggageInfos.length === 0) {\n      return { carryOn: '8kg', checked: '23kg' };\n    }\n\n    let carryOn = '';\n    let checked = '';\n\n    baggageInfos.forEach(baggage => {\n      if (baggage.baggageType === 0) { // Bagage cabine\n        carryOn = `${baggage.weight || baggage.piece}${baggage.unitType === 0 ? 'kg' : ' pièce(s)'}`;\n      } else if (baggage.baggageType === 1) { // Bagage soute\n        checked = `${baggage.weight || baggage.piece}${baggage.unitType === 0 ? 'kg' : ' pièce(s)'}`;\n      }\n    });\n\n    return {\n      carryOn: carryOn || '8kg',\n      checked: checked || '23kg'\n    };\n  }\n\n  /**\n   * Formate l'heure depuis une date ISO ou un timestamp\n   */\n  private formatTime(dateTime: string): string {\n    if (!dateTime) return '';\n\n    try {\n      const date = new Date(dateTime);\n      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });\n    } catch {\n      return dateTime.substring(11, 16) || ''; // Fallback pour format HH:mm\n    }\n  }\n\n  /**\n   * Formate la date depuis une date ISO\n   */\n  private formatDate(dateTime: string): string {\n    if (!dateTime) return '';\n\n    try {\n      const date = new Date(dateTime);\n      return date.toLocaleDateString('fr-FR');\n    } catch {\n      return dateTime.substring(0, 10) || ''; // Fallback pour format YYYY-MM-DD\n    }\n  }\n\n  /**\n   * Formate la durée en minutes vers le format \"Xh Ym\"\n   */\n  private formatDuration(minutes: number): string {\n    if (!minutes || minutes <= 0) return '0h 00m';\n\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours}h ${mins.toString().padStart(2, '0')}m`;\n  }\n\n  /**\n   * Initialise les filtres avec les valeurs par défaut\n   */\n  private initializeFilters(): void {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice; // Commencer avec le prix maximum\n\n      // Initialiser les filtres de compagnies aériennes (tous désélectionnés au début)\n      this.filters.airlines = [];\n\n      // Initialiser les filtres d'escales (tous désélectionnés au début)\n      this.filters.stops = [];\n    } else {\n      // Valeurs par défaut si aucun vol\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n  }\n\n  /**\n   * Applique les filtres aux vols\n   */\n  applyFilters(): void {\n    this.filteredFlights = this.flights.filter(flight => {\n      // Filtre par prix (utiliser currentPrice au lieu de maxPrice)\n      if (flight.price.amount > this.filters.currentPrice) return false;\n\n      // Filtre par compagnie aérienne (si des compagnies sont sélectionnées)\n      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {\n        return false;\n      }\n\n      // Filtre par nombre d'escales (si des escales sont sélectionnées)\n      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {\n        return false;\n      }\n\n      return true;\n    });\n\n    this.sortFlights();\n    this.updatePagination();\n  }\n\n  /**\n   * Trie les vols selon les critères sélectionnés\n   */\n  sortFlights(): void {\n    this.filteredFlights.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (this.sortBy) {\n        case 'price':\n          comparison = a.price.amount - b.price.amount;\n          break;\n        case 'duration':\n          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);\n          break;\n        case 'departure':\n          comparison = a.departure.time.localeCompare(b.departure.time);\n          break;\n      }\n      \n      return this.sortOrder === 'asc' ? comparison : -comparison;\n    });\n  }\n\n  /**\n   * Met à jour la pagination\n   */\n  updatePagination(): void {\n    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);\n    if (this.currentPage > this.totalPages) {\n      this.currentPage = 1;\n    }\n  }\n\n  /**\n   * Obtient les vols pour la page actuelle\n   */\n  getPaginatedFlights(): FlightResult[] {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredFlights.slice(startIndex, endIndex);\n  }\n\n  /**\n   * Change de page\n   */\n  changePage(page: number): void {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n\n  /**\n   * Sélectionne un vol\n   */\n  selectFlight(flight: FlightResult): void {\n    // Naviguer vers la page de détails ou de réservation\n    console.log('Vol sélectionné:', flight);\n    // TODO: Implémenter la navigation vers la page de réservation\n  }\n\n  /**\n   * Affiche les détails d'un vol\n   */\n  showFlightDetails(flight: FlightResult): void {\n    console.log('Affichage des détails du vol:', flight);\n    // TODO: Ouvrir un modal avec les détails complets du vol\n    // ou naviguer vers une page de détails\n  }\n\n  /**\n   * Gère l'erreur de chargement du logo\n   */\n  onLogoError(flight: FlightResult): void {\n    if (flight.airline.logo) {\n      // Essayer le logo alternatif suivant\n      const nextLogo = this.airlineLogoService.getNextLogo(flight.airline.code, flight.airline.logo);\n      if (nextLogo) {\n        flight.airline.logo = nextLogo;\n        console.log(`Tentative avec logo alternatif pour ${flight.airline.code}: ${nextLogo}`);\n        return;\n      }\n    }\n\n    // Aucun logo alternatif disponible\n    flight.airline.logoError = true;\n    console.log(`Aucun logo disponible pour ${flight.airline.code}`);\n  }\n\n  /**\n   * Gère le chargement réussi du logo\n   */\n  onLogoLoad(flight: FlightResult): void {\n    flight.airline.logoError = false;\n  }\n\n  /**\n   * Retourne à la recherche\n   */\n  backToSearch(): void {\n    this.router.navigate(['/search-flight']);\n  }\n\n  /**\n   * Modifie la recherche\n   */\n  modifySearch(): void {\n    // Pré-remplir le formulaire avec les critères actuels\n    this.router.navigate(['/search-flight'], {\n      queryParams: { modify: true, searchData: JSON.stringify(this.searchSummary) }\n    });\n  }\n\n  /**\n   * Formate le prix\n   */\n  private formatPrice(amount: number): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  }\n\n  /**\n   * Parse la durée en minutes\n   */\n  private parseDuration(duration: string): number {\n    const match = duration.match(/(\\d+)h\\s*(\\d+)m/);\n    if (match) {\n      return parseInt(match[1]) * 60 + parseInt(match[2]);\n    }\n    return 0;\n  }\n\n  /**\n   * Obtient le texte des passagers\n   */\n  getPassengerText(): string {\n    if (!this.searchSummary) return '';\n\n    const parts: string[] = [];\n    const p = this.searchSummary.passengers;\n\n    if (p.adults > 0) {\n      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);\n    }\n    if (p.children > 0) {\n      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);\n    }\n    if (p.infants > 0) {\n      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);\n    }\n\n    return parts.join(', ');\n  }\n\n  /**\n   * Obtient les compagnies aériennes uniques\n   */\n  getUniqueAirlines(): { code: string; name: string }[] {\n    const airlines = new Map<string, string>();\n    this.flights.forEach(flight => {\n      airlines.set(flight.airline.code, flight.airline.name);\n    });\n\n    return Array.from(airlines.entries()).map(([code, name]) => ({ code, name }));\n  }\n\n  /**\n   * Toggle le filtre de compagnie aérienne\n   */\n  toggleAirlineFilter(airlineCode: string): void {\n    const index = this.filters.airlines.indexOf(airlineCode);\n    if (index > -1) {\n      this.filters.airlines.splice(index, 1);\n    } else {\n      this.filters.airlines.push(airlineCode);\n    }\n    this.applyFilters();\n  }\n\n  /**\n   * Toggle le filtre d'escales\n   */\n  toggleStopsFilter(stops: number): void {\n    const index = this.filters.stops.indexOf(stops);\n    if (index > -1) {\n      this.filters.stops.splice(index, 1);\n    } else {\n      this.filters.stops.push(stops);\n    }\n    this.applyFilters();\n  }\n\n  /**\n   * Efface tous les filtres\n   */\n  clearFilters(): void {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice;\n    } else {\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n\n    this.filters.airlines = [];\n    this.filters.stops = [];\n    this.filters.departureTime = { min: 0, max: 24 };\n    this.filters.duration = { min: 0, max: 24 };\n\n    this.applyFilters();\n  }\n\n  /**\n   * Toggle l'ordre de tri\n   */\n  toggleSortOrder(): void {\n    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    this.sortFlights();\n  }\n\n  /**\n   * Obtient les numéros de page pour la pagination\n   */\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n  /**\n   * TrackBy function pour la performance\n   */\n  trackByFlightId(index: number, flight: FlightResult): string {\n    return flight.id;\n  }\n\n  /**\n   * TrackBy function pour les segments\n   */\n  trackBySegmentIndex(index: number, segment: FlightSegment): number {\n    return index;\n  }\n\n  /**\n   * Gère l'erreur de chargement du logo d'un segment\n   */\n  onSegmentLogoError(segment: FlightSegment): void {\n    if (segment.airline.logo) {\n      // Essayer le logo alternatif suivant\n      const nextLogo = this.airlineLogoService.getNextLogo(segment.airline.code, segment.airline.logo);\n      if (nextLogo) {\n        segment.airline.logo = nextLogo;\n        console.log(`Tentative avec logo alternatif pour ${segment.airline.code}: ${nextLogo}`);\n        return;\n      }\n    }\n\n    // Aucun logo alternatif disponible\n    segment.airline.logoError = true;\n    console.log(`Aucun logo disponible pour ${segment.airline.code}`);\n  }\n\n  /**\n   * Gère le chargement réussi du logo d'un segment\n   */\n  onSegmentLogoLoad(segment: FlightSegment): void {\n    segment.airline.logoError = false;\n  }\n\n  /**\n   * Obtient le prix minimum pour le slider\n   */\n  getMinPrice(): number {\n    if (this.flights.length === 0) return 0;\n    return Math.min(...this.flights.map(f => f.price.amount));\n  }\n\n  /**\n   * Obtient le prix maximum pour le slider\n   */\n  getMaxPrice(): number {\n    if (this.flights.length === 0) return 1000;\n    return Math.max(...this.flights.map(f => f.price.amount));\n  }\n\n  /**\n   * Met à jour le prix courant du filtre\n   */\n  updateCurrentPrice(price: number): void {\n    this.filters.currentPrice = price;\n    this.applyFilters();\n  }\n\n  /**\n   * Génère des résultats de test\n   */\n  private generateMockResults(): any {\n    return {\n      body: {\n        flights: [\n          {\n            id: 'flight-1',\n            airline: { code: 'TK', name: 'Turkish Airlines' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },\n            duration: '3h 15m',\n            stops: 0,\n            price: { amount: 450, currency: 'EUR' },\n            class: 'Economy',\n            refundable: true,\n            baggage: { carryOn: '8kg', checked: '23kg' }\n          },\n          {\n            id: 'flight-2',\n            airline: { code: 'TU', name: 'Tunisair' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '14:20', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '17:35', date: '2025-06-17' },\n            duration: '3h 15m',\n            stops: 0,\n            price: { amount: 380, currency: 'EUR' },\n            class: 'Economy',\n            refundable: false,\n            baggage: { carryOn: '8kg', checked: '20kg' }\n          },\n          {\n            id: 'flight-3',\n            airline: { code: 'AF', name: 'Air France' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '10:15', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '16:30', date: '2025-06-17' },\n            duration: '6h 15m',\n            stops: 1,\n            price: { amount: 520, currency: 'EUR' },\n            class: 'Economy',\n            refundable: true,\n            baggage: { carryOn: '12kg', checked: '23kg' }\n          },\n          {\n            id: 'flight-4',\n            airline: { code: 'LH', name: 'Lufthansa' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '16:45', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '22:10', date: '2025-06-17' },\n            duration: '5h 25m',\n            stops: 1,\n            price: { amount: 610, currency: 'EUR' },\n            class: 'Business',\n            refundable: true,\n            baggage: { carryOn: '8kg', checked: '32kg' }\n          },\n          {\n            id: 'flight-5',\n            airline: { code: 'EK', name: 'Emirates' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '22:30', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '08:45', date: '2025-06-18' },\n            duration: '10h 15m',\n            stops: 1,\n            price: { amount: 750, currency: 'EUR' },\n            class: 'Business',\n            refundable: true,\n            baggage: { carryOn: '7kg', checked: '30kg' }\n          }\n        ]\n      }\n    };\n  }\n}\n", "<div class=\"flight-results-container\">\n  <!-- Header avec r<PERSON> de recherche -->\n  <div class=\"results-header\">\n    <div class=\"header-content\">\n      <div class=\"search-summary\" *ngIf=\"searchSummary\">\n        <div class=\"route-info\">\n          <div class=\"route-main\">\n            <span class=\"airport-code\">{{ searchSummary?.from }}</span>\n            <div class=\"route-arrow\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n              </svg>\n              <svg *ngIf=\"searchSummary?.searchType === 'roundtrip'\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\" transform=\"rotate(180 12 12)\"/>\n              </svg>\n            </div>\n            <span class=\"airport-code\">{{ searchSummary?.to }}</span>\n          </div>\n          <div class=\"route-details\">\n            <span class=\"date\">{{ searchSummary?.departureDate }}</span>\n            <span *ngIf=\"searchSummary?.returnDate\" class=\"date\"> - {{ searchSummary?.returnDate }}</span>\n            <span class=\"passengers\">{{ getPassengerText() }}</span>\n          </div>\n        </div>\n\n        <div class=\"results-count\">\n          <span class=\"count\">{{ searchSummary?.totalResults }} vols trouvés</span>\n          <span class=\"search-time\">Recherche effectuée à {{ searchSummary?.searchTime }}</span>\n        </div>\n        \n        <div class=\"header-actions\">\n          <button class=\"btn-secondary\" (click)=\"modifySearch()\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n            </svg>\n            Modifier la recherche\n          </button>\n          <button class=\"btn-primary\" (click)=\"backToSearch()\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\"/>\n            </svg>\n            Nouvelle recherche\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Contenu principal -->\n  <div class=\"results-content\">\n    <!-- Sidebar avec filtres -->\n    <aside class=\"filters-sidebar\" [class.hidden]=\"!showFilters\">\n      <div class=\"filters-header\">\n        <h3>Filtres</h3>\n        <button class=\"btn-toggle\" (click)=\"showFilters = !showFilters\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n          </svg>\n        </button>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Prix maximum</h4>\n        <div class=\"price-filter\">\n          <div class=\"price-range-info\">\n            <span class=\"price-min\">{{ getMinPrice() }}€</span>\n            <span class=\"price-max\">{{ getMaxPrice() }}€</span>\n          </div>\n          <input type=\"range\"\n                 [(ngModel)]=\"filters.currentPrice\"\n                 [min]=\"getMinPrice()\"\n                 [max]=\"getMaxPrice()\"\n                 [step]=\"10\"\n                 (input)=\"updateCurrentPrice($any($event.target).value)\"\n                 class=\"price-slider\">\n          <div class=\"price-current\">\n            <span class=\"price-label\">Prix maximum:</span>\n            <span class=\"price-value\">{{ filters.currentPrice }}€</span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Compagnies aériennes</h4>\n        <div class=\"airline-filters\">\n          <label *ngFor=\"let airline of getUniqueAirlines()\" class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   [value]=\"airline.code\"\n                   (change)=\"toggleAirlineFilter(airline.code)\"\n                   [checked]=\"filters.airlines.includes(airline.code)\">\n            <span class=\"checkmark\"></span>\n            <span class=\"airline-name\">{{ airline.name }}</span>\n          </label>\n        </div>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Nombre d'escales</h4>\n        <div class=\"stops-filters\">\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"0\"\n                   (change)=\"toggleStopsFilter(0)\"\n                   [checked]=\"filters.stops.includes(0)\">\n            <span class=\"checkmark\"></span>\n            <span>Vol direct</span>\n          </label>\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"1\"\n                   (change)=\"toggleStopsFilter(1)\"\n                   [checked]=\"filters.stops.includes(1)\">\n            <span class=\"checkmark\"></span>\n            <span>1 escale</span>\n          </label>\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"2\"\n                   (change)=\"toggleStopsFilter(2)\"\n                   [checked]=\"filters.stops.includes(2)\">\n            <span class=\"checkmark\"></span>\n            <span>2+ escales</span>\n          </label>\n        </div>\n      </div>\n      \n      <div class=\"filter-actions\">\n        <button class=\"btn-clear\" (click)=\"clearFilters()\">Effacer les filtres</button>\n      </div>\n    </aside>\n\n    <!-- Zone des résultats -->\n    <main class=\"results-main\">\n      <!-- Barre d'outils -->\n      <div class=\"results-toolbar\">\n        <div class=\"toolbar-left\">\n          <button class=\"btn-filter-toggle\" (click)=\"showFilters = !showFilters\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3 18h6v-2H3v2zM3 6v2h10V6H3zm0 7h8v-2H3v2zm16-1v8h2v-8h-2zm-3-3h2V6h-2v3zm3-3v3h2V6h-2z\"/>\n            </svg>\n            Filtres\n          </button>\n          \n          <div class=\"view-toggle\">\n            <button class=\"view-btn\" \n                    [class.active]=\"viewMode === 'list'\"\n                    (click)=\"viewMode = 'list'\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z\"/>\n              </svg>\n            </button>\n            <button class=\"view-btn\" \n                    [class.active]=\"viewMode === 'grid'\"\n                    (click)=\"viewMode = 'grid'\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"toolbar-right\">\n          <div class=\"sort-controls\">\n            <label>Trier par:</label>\n            <select [(ngModel)]=\"sortBy\" (change)=\"sortFlights(); applyFilters()\">\n              <option value=\"price\">Prix</option>\n              <option value=\"duration\">Durée</option>\n              <option value=\"departure\">Heure de départ</option>\n            </select>\n            <button class=\"sort-order-btn\" (click)=\"toggleSortOrder()\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path *ngIf=\"sortOrder === 'asc'\" d=\"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\"/>\n                <path *ngIf=\"sortOrder === 'desc'\" d=\"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\"/>\n              </svg>\n            </button>\n          </div>\n          \n          <span class=\"results-info\">\n            {{ filteredFlights.length }} résultats\n          </span>\n        </div>\n      </div>\n\n      <!-- État de chargement -->\n      <div *ngIf=\"loading\" class=\"loading-state\">\n        <div class=\"loading-spinner\"></div>\n        <p>Recherche de vols en cours...</p>\n      </div>\n\n      <!-- État d'erreur -->\n      <div *ngIf=\"error\" class=\"error-state\">\n        <div class=\"error-icon\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n          </svg>\n        </div>\n        <h3>Erreur lors de la recherche</h3>\n        <p>{{ error }}</p>\n        <button class=\"btn-primary\" (click)=\"backToSearch()\">Retour à la recherche</button>\n      </div>\n\n      <!-- Liste des vols -->\n      <div *ngIf=\"!loading && !error\" class=\"flights-container\" [class.grid-view]=\"viewMode === 'grid'\">\n        <div *ngIf=\"filteredFlights.length === 0\" class=\"no-results\">\n          <div class=\"no-results-icon\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n            </svg>\n          </div>\n          <h3>Aucun vol trouvé</h3>\n          <p>Essayez de modifier vos critères de recherche ou vos filtres.</p>\n          <button class=\"btn-primary\" (click)=\"clearFilters()\">Effacer les filtres</button>\n        </div>\n\n        <div *ngFor=\"let flight of getPaginatedFlights(); trackBy: trackByFlightId\" \n             class=\"flight-card\"\n             (click)=\"selectFlight(flight)\">\n          \n          <div class=\"flight-header\">\n            <div class=\"airline-info\">\n              <div class=\"airline-logo-container\">\n                <img *ngIf=\"flight.airline.logo && !flight.airline.logoError\"\n                     [src]=\"flight.airline.logo\"\n                     [alt]=\"flight.airline.name\"\n                     class=\"airline-logo\"\n                     (error)=\"onLogoError(flight)\"\n                     (load)=\"onLogoLoad(flight)\">\n                <div *ngIf=\"!flight.airline.logo || flight.airline.logoError\" class=\"airline-placeholder\">\n                  {{ flight.airline.code }}\n                </div>\n              </div>\n              <div class=\"airline-details\">\n                <span class=\"airline-name\">{{ flight.airline.name }}</span>\n                <span class=\"flight-number\">{{ flight.airline.code }} {{ flight.id.split('-')[1] || '1234' }}</span>\n                <span class=\"aircraft-type\">Boeing 737-800</span>\n              </div>\n            </div>\n\n            <div class=\"flight-badges\">\n              <span *ngIf=\"flight.stops === 0\" class=\"badge direct\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 0L6.5 1.5L10 5H3v2h7l-3.5 3.5L8 12l6-6L8 0z\"/>\n                </svg>\n                Direct\n              </span>\n              <span *ngIf=\"flight.refundable\" class=\"badge refundable\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"/>\n                  <path d=\"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"/>\n                </svg>\n                Remboursable\n              </span>\n              <span class=\"badge economy\" *ngIf=\"flight.class === 'Economy'\">Économique</span>\n              <span class=\"badge business\" *ngIf=\"flight.class === 'Business'\">Affaires</span>\n              <span class=\"badge first\" *ngIf=\"flight.class === 'First'\">Première</span>\n            </div>\n          </div>\n\n          <div class=\"flight-details\">\n            <!-- Affichage pour One Way (un seul segment) -->\n            <div *ngIf=\"flight.searchType === 'oneway'\" class=\"flight-route single-segment\">\n              <div class=\"departure\">\n                <div class=\"time\">{{ flight.departure.time }}</div>\n                <div class=\"airport\">{{ flight.departure.airport }}</div>\n                <div class=\"city\">{{ flight.departure.city }}</div>\n              </div>\n\n              <div class=\"flight-info\">\n                <div class=\"duration\">{{ flight.duration }}</div>\n                <div class=\"route-line\">\n                  <div class=\"line\"></div>\n                  <div *ngIf=\"flight.stops > 0\" class=\"stops\">\n                    {{ flight.stops }} escale{{ flight.stops > 1 ? 's' : '' }}\n                  </div>\n                  <div class=\"plane-icon\">\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"arrival\">\n                <div class=\"time\">{{ flight.arrival.time }}</div>\n                <div class=\"airport\">{{ flight.arrival.airport }}</div>\n                <div class=\"city\">{{ flight.arrival.city }}</div>\n              </div>\n            </div>\n\n            <!-- Affichage pour Round Trip et Multicity (plusieurs segments) -->\n            <div *ngIf=\"flight.searchType === 'roundtrip' || flight.searchType === 'multicity'\" class=\"flight-route multi-segment\">\n              <div class=\"segments-container\">\n                <div *ngFor=\"let segment of flight.segments; let i = index; trackBy: trackBySegmentIndex\"\n                     class=\"segment\"\n                     [class.outbound]=\"i === 0\"\n                     [class.return]=\"i === 1 && flight.searchType === 'roundtrip'\"\n                     [class.multicity-segment]=\"flight.searchType === 'multicity'\">\n\n                  <!-- En-tête du segment -->\n                  <div class=\"segment-header\">\n                    <span *ngIf=\"flight.searchType === 'roundtrip' && i === 0\" class=\"segment-label outbound-label\">\n                      <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                        <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                      </svg>\n                      Aller\n                    </span>\n                    <span *ngIf=\"flight.searchType === 'roundtrip' && i === 1\" class=\"segment-label return-label\">\n                      <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                        <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                      </svg>\n                      Retour\n                    </span>\n                    <span *ngIf=\"flight.searchType === 'multicity'\" class=\"segment-label multicity-label\">\n                      <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                        <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n                      </svg>\n                      Segment {{ i + 1 }}\n                    </span>\n                    <span class=\"segment-date\">{{ segment.departure.date }}</span>\n                  </div>\n\n                  <!-- Détails du segment -->\n                  <div class=\"segment-route\">\n                    <div class=\"departure\">\n                      <div class=\"time\">{{ segment.departure.time }}</div>\n                      <div class=\"airport\">{{ segment.departure.airport }}</div>\n                      <div class=\"city\">{{ segment.departure.city }}</div>\n                    </div>\n\n                    <div class=\"flight-info\">\n                      <div class=\"airline-info\">\n                        <img *ngIf=\"segment.airline.logo && !segment.airline.logoError\"\n                             [src]=\"segment.airline.logo\"\n                             [alt]=\"segment.airline.name\"\n                             class=\"airline-logo-small\"\n                             (error)=\"onSegmentLogoError(segment)\"\n                             (load)=\"onSegmentLogoLoad(segment)\">\n                        <div *ngIf=\"!segment.airline.logo || segment.airline.logoError\"\n                             class=\"airline-code-small\">{{ segment.airline.code }}</div>\n                        <div class=\"flight-number\">{{ segment.flightNumber }}</div>\n                      </div>\n                      <div class=\"duration\">{{ segment.duration }}</div>\n                      <div class=\"route-line\">\n                        <div class=\"line\"></div>\n                        <div *ngIf=\"segment.stops > 0\" class=\"stops\">\n                          {{ segment.stops }} escale{{ segment.stops > 1 ? 's' : '' }}\n                        </div>\n                        <div class=\"plane-icon\">\n                          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                            <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n                          </svg>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div class=\"arrival\">\n                      <div class=\"time\">{{ segment.arrival.time }}</div>\n                      <div class=\"airport\">{{ segment.arrival.airport }}</div>\n                      <div class=\"city\">{{ segment.arrival.city }}</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Durée totale pour multi-segments -->\n              <div class=\"total-duration\">\n                <span class=\"total-label\">Durée totale:</span>\n                <span class=\"total-time\">{{ flight.totalDuration }}</span>\n              </div>\n            </div>\n            \n            <div class=\"flight-price\">\n              <div class=\"price-container\">\n                <div class=\"price-amount\">{{ flight.price.formatted }}</div>\n                <div class=\"price-details\">par personne</div>\n                <div class=\"price-taxes\">TTC</div>\n              </div>\n              <div class=\"action-buttons\">\n                <button class=\"btn-details\" (click)=\"$event.stopPropagation(); showFlightDetails(flight)\">\n                  Détails\n                </button>\n                <button class=\"btn-select\">Sélectionner</button>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"flight-baggage\">\n            <div class=\"baggage-info\">\n              <div class=\"baggage-item carry-on\">\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M8.5 6h7l1.5 9H7l1.5-9zM10 4V2h4v2h-4z\"/>\n                </svg>\n                <div class=\"baggage-details\">\n                  <span class=\"baggage-type\">Bagage cabine</span>\n                  <span class=\"baggage-allowance\">{{ flight.baggage?.carryOn || '1 x 8kg' }}</span>\n                </div>\n              </div>\n              <div class=\"baggage-item checked\">\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z\"/>\n                </svg>\n                <div class=\"baggage-details\">\n                  <span class=\"baggage-type\">Bagage soute</span>\n                  <span class=\"baggage-allowance\">{{ flight.baggage?.checked || '1 x 23kg' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flight-extras\">\n              <span class=\"extra-item\" *ngIf=\"flight.refundable\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"/>\n                  <path d=\"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"/>\n                </svg>\n                Remboursable\n              </span>\n              <span class=\"extra-item\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z\"/>\n                </svg>\n                Modifiable\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pagination -->\n      <div *ngIf=\"totalPages > 1\" class=\"pagination\">\n        <button class=\"page-btn\" \n                [disabled]=\"currentPage === 1\"\n                (click)=\"changePage(currentPage - 1)\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n          </svg>\n          Précédent\n        </button>\n        \n        <div class=\"page-numbers\">\n          <button *ngFor=\"let page of getPageNumbers()\" \n                  class=\"page-number\"\n                  [class.active]=\"page === currentPage\"\n                  (click)=\"changePage(page)\">\n            {{ page }}\n          </button>\n        </div>\n        \n        <button class=\"page-btn\" \n                [disabled]=\"currentPage === totalPages\"\n                (click)=\"changePage(currentPage + 1)\">\n          Suivant\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n          </svg>\n        </button>\n      </div>\n    </main>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICU3BC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAgK;IAClKF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMRH,EAAA,CAAAC,cAAA,eAAqD;IAACD,EAAA,CAAAI,MAAA,GAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,kBAAA,QAAAC,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAiC;;;;;;IAhB7FT,EAAA,CAAAC,cAAA,cAAkD;IAGjBD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,IAAAC,gDAAA,kBAEM;IACRZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA2B;IAA3Bb,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAA2B;IACND,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAW,UAAA,KAAAG,6CAAA,mBAA8F;IAC9Fd,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI5DH,EAAA,CAAAC,cAAA,eAA2B;IACLD,EAAA,CAAAI,MAAA,IAA8C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAqD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGxFH,EAAA,CAAAC,cAAA,eAA4B;IACID,EAAA,CAAAe,UAAA,mBAAAC,+DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACpDtB,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiK;IACnKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAa,eAAA,EAAqD;IAArDb,EAAA,CAAAC,cAAA,kBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAQ,+DAAA;MAAAvB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAxB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAClDzB,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAnCoBH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAoB,IAAA,CAAyB;IAK5C5B,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAA6B,UAAA,UAAAF,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAsB,UAAA,kBAA+C;IAI5B9B,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAuB,EAAA,CAAuB;IAG/B/B,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAwB,aAAA,CAAkC;IAC9ChC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAF,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAC,UAAA,CAA+B;IACbT,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAM,gBAAA,GAAwB;IAK/BjC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAM,kBAAA,KAAAqB,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAA0B,YAAA,uBAA8C;IACxClC,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAM,kBAAA,qCAAAqB,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAA2B,UAAA,KAAqD;;;;;;IA0D/EnC,EAAA,CAAAC,cAAA,gBAA0E;IAGjED,EAAA,CAAAe,UAAA,oBAAAqB,iEAAA;MAAA,MAAAC,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAqB,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA;MAAA,OAAUpB,EAAA,CAAAqB,WAAA,CAAAoB,OAAA,CAAAC,mBAAA,CAAAH,WAAA,CAAAI,IAAA,CAAiC;IAAA,EAAC;IAFnD3C,EAAA,CAAAG,YAAA,EAG2D;IAC3DH,EAAA,CAAAE,SAAA,eAA+B;IAC/BF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAJ7CH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,UAAAU,WAAA,CAAAI,IAAA,CAAsB,YAAAC,MAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAR,WAAA,CAAAI,IAAA;IAIF3C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA0B,iBAAA,CAAAa,WAAA,CAAAS,IAAA,CAAkB;;;;;;IAgFzChD,EAAA,CAAAE,SAAA,eAAoF;;;;;;IACpFF,EAAA,CAAAE,SAAA,eAAoF;;;;;IAY9FF,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,oCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAItCH,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAiI;IACnIF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAa,eAAA,EAAI;IAAJb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,kCAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAkC,+DAAA;MAAAjD,EAAA,CAAAiB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA8B,OAAA,CAAA1B,YAAA,EAAc;IAAA,EAAC;IAACzB,EAAA,CAAAI,MAAA,iCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IADhFH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA0B,iBAAA,CAAA0B,MAAA,CAAAC,KAAA,CAAW;;;;;;IAMdrD,EAAA,CAAAC,cAAA,cAA6D;IAEzDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAa,eAAA,EAAI;IAAJb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,4BAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,yEAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACpEH,EAAA,CAAAC,cAAA,iBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAuC,qEAAA;MAAAtD,EAAA,CAAAiB,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAmC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAACzD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAU3EH,EAAA,CAAAC,cAAA,eAKiC;IAD5BD,EAAA,CAAAe,UAAA,mBAAA2C,wEAAA;MAAA1D,EAAA,CAAAiB,aAAA,CAAA0C,IAAA;MAAA,MAAAC,UAAA,GAAA5D,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAAqB,OAAA,GAAA7D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwC,OAAA,CAAAC,WAAA,CAAAF,UAAA,CAAmB;IAAA,EAAC,kBAAAG,uEAAA;MAAA/D,EAAA,CAAAiB,aAAA,CAAA0C,IAAA;MAAA,MAAAC,UAAA,GAAA5D,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAAwB,OAAA,GAAAhE,EAAA,CAAAoB,aAAA;MAAA,OACrBpB,EAAA,CAAAqB,WAAA,CAAA2C,OAAA,CAAAC,UAAA,CAAAL,UAAA,CAAkB;IAAA,EADG;IAJlC5D,EAAA,CAAAG,YAAA,EAKiC;;;;IAJ5BH,EAAA,CAAA6B,UAAA,QAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,EAAAnE,EAAA,CAAAoE,aAAA,CAA2B,QAAAR,UAAA,CAAAM,OAAA,CAAAlB,IAAA;;;;;IAKhChD,EAAA,CAAAC,cAAA,eAA0F;IACxFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsD,UAAA,CAAAM,OAAA,CAAAvB,IAAA,MACF;;;;;IAUF3C,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAA0D;IAC5DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAyD;IACvDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiF;IAEnFF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAA+D;IAAAD,EAAA,CAAAI,MAAA,sBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAChFH,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAChFH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAI,MAAA,oBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBtEH,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqE,kBAAA,MAAAT,UAAA,CAAAU,KAAA,aAAAV,UAAA,CAAAU,KAAA,qBACF;;;;;IAbNtE,EAAA,CAAAC,cAAA,eAAgF;IAE1DD,EAAA,CAAAI,MAAA,GAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAC,cAAA,eAAqB;IAAAD,EAAA,CAAAI,MAAA,GAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAI,MAAA,GAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGrDH,EAAA,CAAAC,cAAA,eAAyB;IACDD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAwB;IACxBF,EAAA,CAAAW,UAAA,KAAA4D,0DAAA,mBAEM;IACNvE,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IAKZH,EAAA,CAAAa,eAAA,EAAqB;IAArBb,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAI,MAAA,IAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAI,MAAA,IAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAvB/BH,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAY,SAAA,CAAAC,IAAA,CAA2B;IACxBzE,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAY,SAAA,CAAAE,OAAA,CAA8B;IACjC1E,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAY,SAAA,CAAAG,IAAA,CAA2B;IAIvB3E,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAgB,QAAA,CAAqB;IAGnC5E,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAU,KAAA,KAAsB;IAYZtE,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAiB,OAAA,CAAAJ,IAAA,CAAyB;IACtBzE,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAiB,OAAA,CAAAH,OAAA,CAA4B;IAC/B1E,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAiB,OAAA,CAAAF,IAAA,CAAyB;;;;;IAevC3E,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,cACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAA8F;IAC5FD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAsF;IACpFD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAwG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,cAAAwE,KAAA,UACF;;;;;;IAcI9E,EAAA,CAAAC,cAAA,eAKyC;IADpCD,EAAA,CAAAe,UAAA,mBAAAgE,sFAAA;MAAA/E,EAAA,CAAAiB,aAAA,CAAA+D,IAAA;MAAA,MAAAC,WAAA,GAAAjF,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAA0C,OAAA,GAAAlF,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA6D,OAAA,CAAAC,kBAAA,CAAAF,WAAA,CAA2B;IAAA,EAAC,kBAAAG,qFAAA;MAAApF,EAAA,CAAAiB,aAAA,CAAA+D,IAAA;MAAA,MAAAC,WAAA,GAAAjF,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAA6C,OAAA,GAAArF,EAAA,CAAAoB,aAAA;MAAA,OAC7BpB,EAAA,CAAAqB,WAAA,CAAAgE,OAAA,CAAAC,iBAAA,CAAAL,WAAA,CAA0B;IAAA,EADG;IAJ1CjF,EAAA,CAAAG,YAAA,EAKyC;;;;IAJpCH,EAAA,CAAA6B,UAAA,QAAAoD,WAAA,CAAAf,OAAA,CAAAC,IAAA,EAAAnE,EAAA,CAAAoE,aAAA,CAA4B,QAAAa,WAAA,CAAAf,OAAA,CAAAlB,IAAA;;;;;IAKjChD,EAAA,CAAAC,cAAA,eACgC;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IAAhCH,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAf,OAAA,CAAAvB,IAAA,CAA0B;;;;;IAM1D3C,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqE,kBAAA,MAAAY,WAAA,CAAAX,KAAA,aAAAW,WAAA,CAAAX,KAAA,qBACF;;;;;IAtDRtE,EAAA,CAAAC,cAAA,eAImE;IAI/DD,EAAA,CAAAW,UAAA,IAAA4E,gEAAA,oBAKO;IACPvF,EAAA,CAAAW,UAAA,IAAA6E,gEAAA,oBAKO;IACPxF,EAAA,CAAAW,UAAA,IAAA8E,gEAAA,oBAKO;IACPzF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,GAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIhEH,EAAA,CAAAC,cAAA,eAA2B;IAELD,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAA+B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGtDH,EAAA,CAAAC,cAAA,gBAAyB;IAErBD,EAAA,CAAAW,UAAA,KAAA+E,gEAAA,mBAKyC;IACzC1F,EAAA,CAAAW,UAAA,KAAAgF,gEAAA,mBACgE;IAChE3F,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,IAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE7DH,EAAA,CAAAC,cAAA,gBAAsB;IAAAD,EAAA,CAAAI,MAAA,IAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAwB;IACxBF,EAAA,CAAAW,UAAA,KAAAiF,gEAAA,mBAEM;IACN5F,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IAKZH,EAAA,CAAAa,eAAA,EAAqB;IAArBb,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAI,MAAA,IAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAI,MAAA,IAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;;IAhEnDH,EAAA,CAAA6F,WAAA,aAAAf,KAAA,OAA0B,WAAAA,KAAA,UAAAlB,UAAA,CAAA9B,UAAA,uCAAA8B,UAAA,CAAA9B,UAAA;IAMpB9B,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA9B,UAAA,oBAAAgD,KAAA,OAAkD;IAMlD9E,EAAA,CAAAK,SAAA,GAAkD;IAAlDL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA9B,UAAA,oBAAAgD,KAAA,OAAkD;IAMlD9E,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA9B,UAAA,iBAAuC;IAMnB9B,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAT,SAAA,CAAAsB,IAAA,CAA4B;IAMnC9F,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAT,SAAA,CAAAC,IAAA,CAA4B;IACzBzE,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAT,SAAA,CAAAE,OAAA,CAA+B;IAClC1E,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAT,SAAA,CAAAG,IAAA,CAA4B;IAKtC3E,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAA6B,UAAA,SAAAoD,WAAA,CAAAf,OAAA,CAAAC,IAAA,KAAAc,WAAA,CAAAf,OAAA,CAAA6B,SAAA,CAAwD;IAMxD/F,EAAA,CAAAK,SAAA,GAAwD;IAAxDL,EAAA,CAAA6B,UAAA,UAAAoD,WAAA,CAAAf,OAAA,CAAAC,IAAA,IAAAc,WAAA,CAAAf,OAAA,CAAA6B,SAAA,CAAwD;IAEnC/F,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAe,YAAA,CAA0B;IAEjChG,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAL,QAAA,CAAsB;IAGpC5E,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAAoD,WAAA,CAAAX,KAAA,KAAuB;IAYbtE,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAJ,OAAA,CAAAJ,IAAA,CAA0B;IACvBzE,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAJ,OAAA,CAAAH,OAAA,CAA6B;IAChC1E,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA0B,iBAAA,CAAAuD,WAAA,CAAAJ,OAAA,CAAAF,IAAA,CAA0B;;;;;IApEtD3E,EAAA,CAAAC,cAAA,eAAuH;IAEnHD,EAAA,CAAAW,UAAA,IAAAsF,yDAAA,qBAqEM;IACRjG,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA4B;IACAD,EAAA,CAAAI,MAAA,yBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,GAA0B;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IA3EjCH,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAA6B,UAAA,YAAA+B,UAAA,CAAAsC,QAAA,CAAoB,iBAAAC,OAAA,CAAAC,mBAAA;IA2EpBpG,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAyC,aAAA,CAA0B;;;;;IA0CrDrG,EAAA,CAAAC,cAAA,gBAAmD;IACjDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiF;IAEnFF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAzMbH,EAAA,CAAAC,cAAA,cAEoC;IAA/BD,EAAA,CAAAe,UAAA,mBAAAuF,kEAAA;MAAA,MAAAjE,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAsF,IAAA;MAAA,MAAA3C,UAAA,GAAAvB,WAAA,CAAAG,SAAA;MAAA,MAAAgE,OAAA,GAAAxG,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAmF,OAAA,CAAAC,YAAA,CAAA7C,UAAA,CAAoB;IAAA,EAAC;IAEjC5D,EAAA,CAAAC,cAAA,cAA2B;IAGrBD,EAAA,CAAAW,UAAA,IAAA+F,kDAAA,kBAKiC;IACjC1G,EAAA,CAAAW,UAAA,IAAAgG,kDAAA,kBAEM;IACR3G,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,IAAiE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpGH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAAiG,oDAAA,mBAKO;IACP5G,EAAA,CAAAW,UAAA,KAAAkG,oDAAA,mBAMO;IACP7G,EAAA,CAAAW,UAAA,KAAAmG,oDAAA,mBAAgF;IAChF9G,EAAA,CAAAW,UAAA,KAAAoG,oDAAA,mBAAgF;IAChF/G,EAAA,CAAAW,UAAA,KAAAqG,oDAAA,oBAA0E;IAC5EhH,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IAE1BD,EAAA,CAAAW,UAAA,KAAAsG,mDAAA,oBA2BM;IAGNjH,EAAA,CAAAW,UAAA,KAAAuG,mDAAA,mBA+EM;IAENlH,EAAA,CAAAC,cAAA,gBAA0B;IAEID,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,WAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEpCH,EAAA,CAAAC,cAAA,gBAA4B;IACED,EAAA,CAAAe,UAAA,mBAAAoG,sEAAAC,MAAA;MAAA,MAAA/E,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAsF,IAAA;MAAA,MAAA3C,UAAA,GAAAvB,WAAA,CAAAG,SAAA;MAAA,MAAA6E,OAAA,GAAArH,EAAA,CAAAoB,aAAA;MAASgG,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAEtH,EAAA,CAAAqB,WAAA,CAAAgG,OAAA,CAAAE,iBAAA,CAAA3D,UAAA,CAAyB;IAAA,EAAC;IACvF5D,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAI,MAAA,yBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAKtDH,EAAA,CAAAC,cAAA,gBAA4B;IAGtBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAAkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA6B;IAA7Bb,EAAA,CAAAC,cAAA,gBAA6B;IACAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,iBAAgC;IAAAD,EAAA,CAAAI,MAAA,IAA0C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGrFH,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA6B;IAA7Bb,EAAA,CAAAC,cAAA,gBAA6B;IACAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,iBAAgC;IAAAD,EAAA,CAAAI,MAAA,IAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKxFH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAA6G,oDAAA,oBAMO;IACPxH,EAAA,CAAAC,cAAA,iBAAyB;IACvBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAA2K;IAC7KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAxMCH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,KAAAP,UAAA,CAAAM,OAAA,CAAA6B,SAAA,CAAsD;IAMtD/F,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA6B,UAAA,UAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,IAAAP,UAAA,CAAAM,OAAA,CAAA6B,SAAA,CAAsD;IAKjC/F,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAM,OAAA,CAAAlB,IAAA,CAAyB;IACxBhD,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAqE,kBAAA,KAAAT,UAAA,CAAAM,OAAA,CAAAvB,IAAA,OAAAiB,UAAA,CAAA6D,EAAA,CAAAC,KAAA,uBAAiE;IAMxF1H,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAU,KAAA,OAAwB;IAMxBtE,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA+D,UAAA,CAAuB;IAOD3H,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAgE,KAAA,eAAgC;IAC/B5H,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAgE,KAAA,gBAAiC;IACpC5H,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAgE,KAAA,aAA8B;IAMrD5H,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA9B,UAAA,cAAoC;IA8BpC9B,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA9B,UAAA,oBAAA8B,UAAA,CAAA9B,UAAA,iBAA4E;IAmFpD9B,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAiE,KAAA,CAAAC,SAAA,CAA4B;IAqBpB9H,EAAA,CAAAK,SAAA,IAA0C;IAA1CL,EAAA,CAAA0B,iBAAA,EAAAkC,UAAA,CAAAmE,OAAA,kBAAAnE,UAAA,CAAAmE,OAAA,CAAAC,OAAA,eAA0C;IAS1ChI,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAA0B,iBAAA,EAAAkC,UAAA,CAAAmE,OAAA,kBAAAnE,UAAA,CAAAmE,OAAA,CAAAE,OAAA,gBAA2C;IAMrDjI,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAA+D,UAAA,CAAuB;;;;;IA/MzD3H,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAW,UAAA,IAAAuH,4CAAA,mBASM;IAENlI,EAAA,CAAAW,UAAA,IAAAwH,4CAAA,oBAkNM;IACRnI,EAAA,CAAAG,YAAA,EAAM;;;;IA/NoDH,EAAA,CAAA6F,WAAA,cAAAuC,MAAA,CAAAC,QAAA,YAAuC;IACzFrI,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA6B,UAAA,SAAAuG,MAAA,CAAAE,eAAA,CAAAC,MAAA,OAAkC;IAWhBvI,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA6B,UAAA,YAAAuG,MAAA,CAAAI,mBAAA,GAA0B,iBAAAJ,MAAA,CAAAK,eAAA;;;;;;IAiOhDzI,EAAA,CAAAC,cAAA,kBAGmC;IAA3BD,EAAA,CAAAe,UAAA,mBAAA2H,wEAAA;MAAA,MAAArG,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAA0H,IAAA;MAAA,MAAAC,QAAA,GAAAvG,WAAA,CAAAG,SAAA;MAAA,MAAAqG,OAAA,GAAA7I,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwH,OAAA,CAAAC,UAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAChC5I,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAHDH,EAAA,CAAA6F,WAAA,WAAA+C,QAAA,KAAAG,OAAA,CAAAC,WAAA,CAAqC;IAE3ChJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsI,QAAA,MACF;;;;;;IAhBJ5I,EAAA,CAAAC,cAAA,eAA+C;IAGrCD,EAAA,CAAAe,UAAA,mBAAAkI,+DAAA;MAAAjJ,EAAA,CAAAiB,aAAA,CAAAiI,IAAA;MAAA,MAAAC,OAAA,GAAAnJ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA8H,OAAA,CAAAL,UAAA,CAAAK,OAAA,CAAAH,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC3ChJ,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAyD;IAC3DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAa,eAAA,EAA0B;IAA1Bb,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAW,UAAA,IAAAyI,+CAAA,sBAKS;IACXpJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAE8C;IAAtCD,EAAA,CAAAe,UAAA,mBAAAsI,+DAAA;MAAArJ,EAAA,CAAAiB,aAAA,CAAAiI,IAAA;MAAA,MAAAI,OAAA,GAAAtJ,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAiI,OAAA,CAAAR,UAAA,CAAAQ,OAAA,CAAAN,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC3ChJ,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAA0D;IAC5DF,EAAA,CAAAG,YAAA,EAAM;;;;IAvBAH,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6B,UAAA,aAAA0H,MAAA,CAAAP,WAAA,OAA8B;IASXhJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA6B,UAAA,YAAA0H,MAAA,CAAAC,cAAA,GAAmB;IAStCxJ,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA6B,UAAA,aAAA0H,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAE,UAAA,CAAuC;;;AD9VvD,OAAM,MAAOC,sBAAsB;EAmCjCC,YACUC,mBAAwC,EACxCC,MAAc,EACdC,KAAqB,EACrBC,kBAAsC;IAHtC,KAAAH,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAtCpB,KAAAC,QAAQ,GAAG,IAAIlK,OAAO,EAAQ;IAEtC;IACA,KAAAmK,OAAO,GAAG,KAAK;IACf,KAAA5G,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAA7C,aAAa,GAAyB,IAAI;IAC1C,KAAA0J,OAAO,GAAmB,EAAE;IAC5B,KAAA5B,eAAe,GAAmB,EAAE;IAEpC;IACA,KAAAzF,OAAO,GAAG;MACRsH,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE,CAAC;MACfvH,QAAQ,EAAE,EAAc;MACxBwB,KAAK,EAAE,EAAc;MACrBgG,aAAa,EAAE;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAClC5F,QAAQ,EAAE;QAAE2F,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE;KAC5B;IAED,KAAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAClB,KAAAC,SAAS,GAAG,KAAK,CAAC,CAAC;IAEnB;IACA,KAAArC,QAAQ,GAAG,MAAM,CAAC,CAAC;IACnB,KAAAsC,WAAW,GAAG,IAAI;IAElB;IACA,KAAA3B,WAAW,GAAG,CAAC;IACf,KAAA4B,YAAY,GAAG,EAAE;IACjB,KAAAnB,UAAU,GAAG,CAAC;EAOX;EAEHoB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,EAAE;EAC1B;EAEA;;;EAGQJ,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAChB,KAAK,CAACqB,WAAW,CAACC,IAAI,CAACrL,SAAS,CAAC,IAAI,CAACiK,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAACC,MAAM,IAAG;MACvE,IAAIA,MAAM,CAAC,YAAY,CAAC,EAAE;QACxB,IAAI;UACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACJ,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;UACvE,IAAI,CAACK,aAAa,CAACJ,UAAU,CAAC;SAC/B,CAAC,OAAOlI,KAAK,EAAE;UACduI,OAAO,CAACvI,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACwG,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;OAE3C,MAAM;QACL;QACA,IAAI,CAAChC,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEA;;;EAGQF,aAAaA,CAACJ,UAAe;IACnC,IAAI,CAACtB,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC5G,KAAK,GAAG,IAAI;IAEjB;IACAyI,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,mBAAmB,EAAE,EAAET,UAAU,CAAC;MACjE,IAAI,CAACtB,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQ8B,oBAAoBA,CAACE,QAAa,EAAEV,UAAe;IACzD;IACA,IAAI,CAAC/K,aAAa,GAAG;MACnBsB,UAAU,EAAEyJ,UAAU,CAACW,IAAI;MAC3BtK,IAAI,EAAE2J,UAAU,CAACD,MAAM,CAACa,iBAAiB;MACzCpK,EAAE,EAAEwJ,UAAU,CAACD,MAAM,CAACc,eAAe;MACrCpK,aAAa,EAAEuJ,UAAU,CAACD,MAAM,CAACtJ,aAAa;MAC9CvB,UAAU,EAAE8K,UAAU,CAACD,MAAM,CAAC7K,UAAU;MACxC4L,UAAU,EAAEd,UAAU,CAACD,MAAM,CAACe,UAAU;MACxCnK,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,IAAImK,IAAI,EAAE,CAACC,kBAAkB;KAC1C;IAED;IACA,IAAI,CAACrC,OAAO,GAAG,IAAI,CAACsC,cAAc,CAACP,QAAQ,EAAEV,UAAU,CAACW,IAAI,CAAC;IAC7D,IAAI,CAAC1L,aAAa,CAAC0B,YAAY,GAAG,IAAI,CAACgI,OAAO,CAAC3B,MAAM;IAErD;IACA,IAAI,CAACkE,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQF,cAAcA,CAACP,QAAa,EAAEnK,UAAgD;IACpF,MAAMoI,OAAO,GAAmB,EAAE;IAElC,IAAI,CAAC+B,QAAQ,CAACU,IAAI,EAAEzC,OAAO,EAAE;MAC3B,OAAOA,OAAO;;IAGhB+B,QAAQ,CAACU,IAAI,CAACzC,OAAO,CAAC0C,OAAO,CAAC,CAACC,MAAW,EAAEC,KAAa,KAAI;MAC3D,IAAI;QACF,MAAMC,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACH,MAAM,EAAE/K,UAAU,EAAEgL,KAAK,CAAC;QACvE,IAAIC,YAAY,EAAE;UAChB7C,OAAO,CAAC+C,IAAI,CAACF,YAAY,CAAC;;OAE7B,CAAC,OAAO1J,KAAK,EAAE;QACduI,OAAO,CAACvI,KAAK,CAAC,mCAAmC,EAAEA,KAAK,EAAEwJ,MAAM,CAAC;;IAErE,CAAC,CAAC;IAEF,OAAO3C,OAAO;EAChB;EAEA;;;EAGQ8C,kBAAkBA,CAACH,MAAW,EAAE/K,UAAgD,EAAEgL,KAAa;IACrG,IAAI,CAACD,MAAM,CAACK,KAAK,IAAIL,MAAM,CAACK,KAAK,CAAC3E,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;;IAGb;IACA,MAAMrC,QAAQ,GAAoB2G,MAAM,CAACK,KAAK,CAACC,GAAG,CAAEC,IAAS,IAAK,IAAI,CAACC,mBAAmB,CAACD,IAAI,CAAC,CAAC;IAEjG;IACA,MAAM/G,aAAa,GAAG,IAAI,CAACiH,sBAAsB,CAACpH,QAAQ,CAAC;IAE3D;IACA,MAAM2B,KAAK,GAAG,IAAI,CAAC0F,YAAY,CAACV,MAAM,CAACW,MAAM,CAAC;IAE9C;IACA,MAAMC,WAAW,GAAGvH,QAAQ,CAAC,CAAC,CAAC;IAE/B;IACA,MAAMwH,YAAY,GAAGxH,QAAQ,CAACA,QAAQ,CAACqC,MAAM,GAAG,CAAC,CAAC;IAElD,OAAO;MACLd,EAAE,EAAEoF,MAAM,CAACpF,EAAE,IAAI,UAAUqF,KAAK,EAAE;MAClChL,UAAU;MACVoE,QAAQ;MACRG,aAAa;MACbwB,KAAK;MACLD,KAAK,EAAE6F,WAAW,CAACvJ,OAAO,CAAClB,IAAI,IAAI,SAAS;MAC5C2E,UAAU,EAAE,IAAI,CAACgG,YAAY,CAACd,MAAM,CAAC;MACrCe,UAAU,EAAE,IAAI,CAACC,YAAY,CAAChB,MAAM,CAAC;MAErC;MACA3I,OAAO,EAAEuJ,WAAW,CAACvJ,OAAO;MAC5BM,SAAS,EAAEiJ,WAAW,CAACjJ,SAAS;MAChCK,OAAO,EAAE6I,YAAY,CAAC7I,OAAO;MAC7BD,QAAQ,EAAEyB,aAAa;MACvB/B,KAAK,EAAE,IAAI,CAACwJ,mBAAmB,CAAC5H,QAAQ,CAAC;MACzC6B,OAAO,EAAE0F,WAAW,CAAC1F;KACtB;EACH;EAEA;;;EAGQsF,mBAAmBA,CAACD,IAAS;IACnC,MAAMlJ,OAAO,GAAG;MACdvB,IAAI,EAAEyK,IAAI,CAAClJ,OAAO,EAAE6J,iBAAiB,IAAIX,IAAI,CAAClJ,OAAO,EAAEvB,IAAI,IAAI,IAAI;MACnEK,IAAI,EAAEoK,IAAI,CAAClJ,OAAO,EAAElB,IAAI,IAAI,oBAAoB;MAChDmB,IAAI,EAAEiJ,IAAI,CAAClJ,OAAO,EAAEC,IAAI,IAAIiJ,IAAI,CAAClJ,OAAO,EAAE8J,QAAQ,IAAI,IAAI,CAACjE,kBAAkB,CAACkE,cAAc,CAACb,IAAI,CAAClJ,OAAO,EAAE6J,iBAAiB,IAAIX,IAAI,CAAClJ,OAAO,EAAEvB,IAAI,IAAI,EAAE,CAAC;MACzJoD,SAAS,EAAE;KACZ;IAED,OAAO;MACL7B,OAAO;MACPM,SAAS,EAAE;QACTE,OAAO,EAAE0I,IAAI,CAAC5I,SAAS,EAAEE,OAAO,EAAE/B,IAAI,IAAIyK,IAAI,CAAC5I,SAAS,EAAE7B,IAAI,IAAI,EAAE;QACpEgC,IAAI,EAAEyI,IAAI,CAAC5I,SAAS,EAAEE,OAAO,EAAEC,IAAI,IAAIyI,IAAI,CAAC5I,SAAS,EAAEG,IAAI,IAAI,EAAE;QACjEF,IAAI,EAAE,IAAI,CAACyJ,UAAU,CAACd,IAAI,CAAC5I,SAAS,EAAEC,IAAI,IAAI2I,IAAI,CAACe,UAAU,CAAC;QAC9DrI,IAAI,EAAE,IAAI,CAACsI,UAAU,CAAChB,IAAI,CAAC5I,SAAS,EAAEsB,IAAI,IAAIsH,IAAI,CAACe,UAAU;OAC9D;MACDtJ,OAAO,EAAE;QACPH,OAAO,EAAE0I,IAAI,CAACvI,OAAO,EAAEH,OAAO,EAAE/B,IAAI,IAAIyK,IAAI,CAACvI,OAAO,EAAElC,IAAI,IAAI,EAAE;QAChEgC,IAAI,EAAEyI,IAAI,CAACvI,OAAO,EAAEH,OAAO,EAAEC,IAAI,IAAIyI,IAAI,CAACvI,OAAO,EAAEF,IAAI,IAAI,EAAE;QAC7DF,IAAI,EAAE,IAAI,CAACyJ,UAAU,CAACd,IAAI,CAACvI,OAAO,EAAEJ,IAAI,CAAC;QACzCqB,IAAI,EAAE,IAAI,CAACsI,UAAU,CAAChB,IAAI,CAACvI,OAAO,EAAEiB,IAAI;OACzC;MACDlB,QAAQ,EAAE,IAAI,CAACyJ,cAAc,CAACjB,IAAI,CAACxI,QAAQ,CAAC;MAC5CN,KAAK,EAAE8I,IAAI,CAACkB,SAAS,IAAI,CAAC;MAC1BtI,YAAY,EAAEoH,IAAI,CAACmB,QAAQ,IAAI,EAAE;MACjCC,QAAQ,EAAEpB,IAAI,CAACoB,QAAQ,IAAI,EAAE;MAC7BzG,OAAO,EAAE,IAAI,CAAC0G,cAAc,CAACrB,IAAI,CAACsB,mBAAmB;KACtD;EACH;EAEA;;;EAGQpB,sBAAsBA,CAACpH,QAAyB;IACtD,IAAIA,QAAQ,CAACqC,MAAM,KAAK,CAAC,EAAE,OAAO,QAAQ;IAE1C;IACA,IAAIrC,QAAQ,CAACqC,MAAM,KAAK,CAAC,EAAE;MACzB,OAAOrC,QAAQ,CAAC,CAAC,CAAC,CAACtB,QAAQ;;IAG7B;IACA,MAAM+J,cAAc,GAAG,IAAIrC,IAAI,CAAC,GAAGpG,QAAQ,CAAC,CAAC,CAAC,CAAC1B,SAAS,CAACsB,IAAI,IAAII,QAAQ,CAAC,CAAC,CAAC,CAAC1B,SAAS,CAACC,IAAI,EAAE,CAAC;IAC9F,MAAMmK,WAAW,GAAG,IAAItC,IAAI,CAAC,GAAGpG,QAAQ,CAACA,QAAQ,CAACqC,MAAM,GAAG,CAAC,CAAC,CAAC1D,OAAO,CAACiB,IAAI,IAAII,QAAQ,CAACA,QAAQ,CAACqC,MAAM,GAAG,CAAC,CAAC,CAAC1D,OAAO,CAACJ,IAAI,EAAE,CAAC;IAE3H,MAAMoK,YAAY,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,WAAW,CAACI,OAAO,EAAE,GAAGL,cAAc,CAACK,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IACjG,OAAO,IAAI,CAACX,cAAc,CAACQ,YAAY,CAAC;EAC1C;EAEA;;;EAGQf,mBAAmBA,CAAC5H,QAAyB;IACnD,OAAOA,QAAQ,CAAC+I,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAKD,KAAK,GAAGC,OAAO,CAAC7K,KAAK,EAAE,CAAC,CAAC;EACtE;EAEA;;;EAGQiJ,YAAYA,CAACC,MAAa;IAChC,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACjF,MAAM,KAAK,CAAC,EAAE;MAClC,OAAO;QAAE6G,MAAM,EAAE,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEvH,SAAS,EAAE,IAAI,CAACwH,WAAW,CAAC,CAAC;MAAC,CAAE;;IAGvE;IACA,MAAMC,KAAK,GAAG/B,MAAM,CAAC,CAAC,CAAC;IACvB,MAAM4B,MAAM,GAAGG,KAAK,CAAC1H,KAAK,EAAEuH,MAAM,IAAIG,KAAK,CAACC,UAAU,IAAI,CAAC;IAC3D,MAAMH,QAAQ,GAAGE,KAAK,CAAC1H,KAAK,EAAEwH,QAAQ,IAAIE,KAAK,CAACF,QAAQ,IAAI,KAAK;IAEjE,OAAO;MACLD,MAAM;MACNC,QAAQ;MACRvH,SAAS,EAAE,IAAI,CAACwH,WAAW,CAACF,MAAM;KACnC;EACH;EAEA;;;EAGQzB,YAAYA,CAACd,MAAW;IAC9B;IACA,OAAOA,MAAM,CAAClF,UAAU,IAAI,KAAK;EACnC;EAEA;;;EAGQkG,YAAYA,CAAChB,MAAW;IAC9B;IACA,OAAOA,MAAM,CAACe,UAAU,IAAI,KAAK;EACnC;EAEA;;;EAGQa,cAAcA,CAACgB,YAAmB;IACxC,IAAI,CAACA,YAAY,IAAIA,YAAY,CAAClH,MAAM,KAAK,CAAC,EAAE;MAC9C,OAAO;QAAEP,OAAO,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAM,CAAE;;IAG5C,IAAID,OAAO,GAAG,EAAE;IAChB,IAAIC,OAAO,GAAG,EAAE;IAEhBwH,YAAY,CAAC7C,OAAO,CAAC7E,OAAO,IAAG;MAC7B,IAAIA,OAAO,CAAC2H,WAAW,KAAK,CAAC,EAAE;QAAE;QAC/B1H,OAAO,GAAG,GAAGD,OAAO,CAAC4H,MAAM,IAAI5H,OAAO,CAAC6H,KAAK,GAAG7H,OAAO,CAAC8H,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,WAAW,EAAE;OAC7F,MAAM,IAAI9H,OAAO,CAAC2H,WAAW,KAAK,CAAC,EAAE;QAAE;QACtCzH,OAAO,GAAG,GAAGF,OAAO,CAAC4H,MAAM,IAAI5H,OAAO,CAAC6H,KAAK,GAAG7H,OAAO,CAAC8H,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAG,WAAW,EAAE;;IAEhG,CAAC,CAAC;IAEF,OAAO;MACL7H,OAAO,EAAEA,OAAO,IAAI,KAAK;MACzBC,OAAO,EAAEA,OAAO,IAAI;KACrB;EACH;EAEA;;;EAGQiG,UAAUA,CAAC4B,QAAgB;IACjC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAI;MACF,MAAMhK,IAAI,GAAG,IAAIwG,IAAI,CAACwD,QAAQ,CAAC;MAC/B,OAAOhK,IAAI,CAACyG,kBAAkB,CAAC,OAAO,EAAE;QAAEwD,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAS,CAAE,CAAC;KAChF,CAAC,MAAM;MACN,OAAOF,QAAQ,CAACG,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;EAE7C;EAEA;;;EAGQ7B,UAAUA,CAAC0B,QAAgB;IACjC,IAAI,CAACA,QAAQ,EAAE,OAAO,EAAE;IAExB,IAAI;MACF,MAAMhK,IAAI,GAAG,IAAIwG,IAAI,CAACwD,QAAQ,CAAC;MAC/B,OAAOhK,IAAI,CAACoK,kBAAkB,CAAC,OAAO,CAAC;KACxC,CAAC,MAAM;MACN,OAAOJ,QAAQ,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;EAE5C;EAEA;;;EAGQ5B,cAAcA,CAAC8B,OAAe;IACpC,IAAI,CAACA,OAAO,IAAIA,OAAO,IAAI,CAAC,EAAE,OAAO,QAAQ;IAE7C,MAAMC,KAAK,GAAGtB,IAAI,CAACC,KAAK,CAACoB,OAAO,GAAG,EAAE,CAAC;IACtC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,KAAK,KAAKC,IAAI,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG;EACzD;EAEA;;;EAGQxF,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACb,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMiI,MAAM,GAAG,IAAI,CAACtG,OAAO,CAACiD,GAAG,CAACsD,CAAC,IAAIA,CAAC,CAAC5I,KAAK,CAACuH,MAAM,CAAC;MACpD,IAAI,CAACvM,OAAO,CAACsH,QAAQ,GAAG2E,IAAI,CAACvE,GAAG,CAAC,GAAGiG,MAAM,CAAC;MAC3C,IAAI,CAAC3N,OAAO,CAACuH,QAAQ,GAAG0E,IAAI,CAACtE,GAAG,CAAC,GAAGgG,MAAM,CAAC;MAC3C,IAAI,CAAC3N,OAAO,CAACwH,YAAY,GAAG,IAAI,CAACxH,OAAO,CAACuH,QAAQ,CAAC,CAAC;MAEnD;MACA,IAAI,CAACvH,OAAO,CAACC,QAAQ,GAAG,EAAE;MAE1B;MACA,IAAI,CAACD,OAAO,CAACyB,KAAK,GAAG,EAAE;KACxB,MAAM;MACL;MACA,IAAI,CAACzB,OAAO,CAACsH,QAAQ,GAAG,CAAC;MACzB,IAAI,CAACtH,OAAO,CAACuH,QAAQ,GAAG,IAAI;MAC5B,IAAI,CAACvH,OAAO,CAACwH,YAAY,GAAG,IAAI;;EAEpC;EAEA;;;EAGAoC,YAAYA,CAAA;IACV,IAAI,CAACnE,eAAe,GAAG,IAAI,CAAC4B,OAAO,CAACwG,MAAM,CAAC7D,MAAM,IAAG;MAClD;MACA,IAAIA,MAAM,CAAChF,KAAK,CAACuH,MAAM,GAAG,IAAI,CAACvM,OAAO,CAACwH,YAAY,EAAE,OAAO,KAAK;MAEjE;MACA,IAAI,IAAI,CAACxH,OAAO,CAACC,QAAQ,CAACyF,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC1F,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAAC8J,MAAM,CAAC3I,OAAO,CAACvB,IAAI,CAAC,EAAE;QAC5F,OAAO,KAAK;;MAGd;MACA,IAAI,IAAI,CAACE,OAAO,CAACyB,KAAK,CAACiE,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC1F,OAAO,CAACyB,KAAK,CAACvB,QAAQ,CAAC8J,MAAM,CAACvI,KAAK,CAAC,EAAE;QAC/E,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAI,CAACqM,WAAW,EAAE;IAClB,IAAI,CAACjE,gBAAgB,EAAE;EACzB;EAEA;;;EAGAiE,WAAWA,CAAA;IACT,IAAI,CAACrI,eAAe,CAACsI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQ,IAAI,CAACtG,MAAM;QACjB,KAAK,OAAO;UACVsG,UAAU,GAAGF,CAAC,CAAChJ,KAAK,CAACuH,MAAM,GAAG0B,CAAC,CAACjJ,KAAK,CAACuH,MAAM;UAC5C;QACF,KAAK,UAAU;UACb2B,UAAU,GAAG,IAAI,CAACC,aAAa,CAACH,CAAC,CAACjM,QAAQ,CAAC,GAAG,IAAI,CAACoM,aAAa,CAACF,CAAC,CAAClM,QAAQ,CAAC;UAC5E;QACF,KAAK,WAAW;UACdmM,UAAU,GAAGF,CAAC,CAACrM,SAAS,CAACC,IAAI,CAACwM,aAAa,CAACH,CAAC,CAACtM,SAAS,CAACC,IAAI,CAAC;UAC7D;;MAGJ,OAAO,IAAI,CAACiG,SAAS,KAAK,KAAK,GAAGqG,UAAU,GAAG,CAACA,UAAU;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGArE,gBAAgBA,CAAA;IACd,IAAI,CAACjD,UAAU,GAAGqF,IAAI,CAACoC,IAAI,CAAC,IAAI,CAAC5I,eAAe,CAACC,MAAM,GAAG,IAAI,CAACqC,YAAY,CAAC;IAC5E,IAAI,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACS,UAAU,EAAE;MACtC,IAAI,CAACT,WAAW,GAAG,CAAC;;EAExB;EAEA;;;EAGAR,mBAAmBA,CAAA;IACjB,MAAM2I,UAAU,GAAG,CAAC,IAAI,CAACnI,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC4B,YAAY;IAC7D,MAAMwG,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACvG,YAAY;IAC/C,OAAO,IAAI,CAACtC,eAAe,CAAC+I,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACzD;EAEA;;;EAGAtI,UAAUA,CAACwI,IAAY;IACrB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC7H,UAAU,EAAE;MACxC,IAAI,CAACT,WAAW,GAAGsI,IAAI;;EAE3B;EAEA;;;EAGA7K,YAAYA,CAACoG,MAAoB;IAC/B;IACAjB,OAAO,CAAC2F,GAAG,CAAC,kBAAkB,EAAE1E,MAAM,CAAC;IACvC;EACF;EAEA;;;EAGAtF,iBAAiBA,CAACsF,MAAoB;IACpCjB,OAAO,CAAC2F,GAAG,CAAC,+BAA+B,EAAE1E,MAAM,CAAC;IACpD;IACA;EACF;EAEA;;;EAGA/I,WAAWA,CAAC+I,MAAoB;IAC9B,IAAIA,MAAM,CAAC3I,OAAO,CAACC,IAAI,EAAE;MACvB;MACA,MAAMqN,QAAQ,GAAG,IAAI,CAACzH,kBAAkB,CAAC0H,WAAW,CAAC5E,MAAM,CAAC3I,OAAO,CAACvB,IAAI,EAAEkK,MAAM,CAAC3I,OAAO,CAACC,IAAI,CAAC;MAC9F,IAAIqN,QAAQ,EAAE;QACZ3E,MAAM,CAAC3I,OAAO,CAACC,IAAI,GAAGqN,QAAQ;QAC9B5F,OAAO,CAAC2F,GAAG,CAAC,uCAAuC1E,MAAM,CAAC3I,OAAO,CAACvB,IAAI,KAAK6O,QAAQ,EAAE,CAAC;QACtF;;;IAIJ;IACA3E,MAAM,CAAC3I,OAAO,CAAC6B,SAAS,GAAG,IAAI;IAC/B6F,OAAO,CAAC2F,GAAG,CAAC,8BAA8B1E,MAAM,CAAC3I,OAAO,CAACvB,IAAI,EAAE,CAAC;EAClE;EAEA;;;EAGAsB,UAAUA,CAAC4I,MAAoB;IAC7BA,MAAM,CAAC3I,OAAO,CAAC6B,SAAS,GAAG,KAAK;EAClC;EAEA;;;EAGAtE,YAAYA,CAAA;IACV,IAAI,CAACoI,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;;;EAGAvK,YAAYA,CAAA;IACV;IACA,IAAI,CAACuI,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;MACvCV,WAAW,EAAE;QAAEuG,MAAM,EAAE,IAAI;QAAEnG,UAAU,EAAEC,IAAI,CAACmG,SAAS,CAAC,IAAI,CAACnR,aAAa;MAAC;KAC5E,CAAC;EACJ;EAEA;;;EAGQ8O,WAAWA,CAACF,MAAc;IAChC,OAAO,IAAIwC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBzC,QAAQ,EAAE;KACX,CAAC,CAAC0C,MAAM,CAAC3C,MAAM,CAAC;EACnB;EAEA;;;EAGQ4B,aAAaA,CAACpM,QAAgB;IACpC,MAAMoN,KAAK,GAAGpN,QAAQ,CAACoN,KAAK,CAAC,iBAAiB,CAAC;IAC/C,IAAIA,KAAK,EAAE;MACT,OAAOC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;;IAErD,OAAO,CAAC;EACV;EAEA;;;EAGA/P,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE,OAAO,EAAE;IAElC,MAAM0R,KAAK,GAAa,EAAE;IAC1B,MAAMC,CAAC,GAAG,IAAI,CAAC3R,aAAa,CAAC6L,UAAU;IAEvC,IAAI8F,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAChBF,KAAK,CAACjF,IAAI,CAAC,GAAGkF,CAAC,CAACC,MAAM,UAAUD,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAE5D,IAAID,CAAC,CAACE,QAAQ,GAAG,CAAC,EAAE;MAClBH,KAAK,CAACjF,IAAI,CAAC,GAAGkF,CAAC,CAACE,QAAQ,UAAUF,CAAC,CAACE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAEhE,IAAIF,CAAC,CAACG,OAAO,GAAG,CAAC,EAAE;MACjBJ,KAAK,CAACjF,IAAI,CAAC,GAAGkF,CAAC,CAACG,OAAO,QAAQH,CAAC,CAACG,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAG5D,OAAOJ,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC;EACzB;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAM1P,QAAQ,GAAG,IAAI2P,GAAG,EAAkB;IAC1C,IAAI,CAACvI,OAAO,CAAC0C,OAAO,CAACC,MAAM,IAAG;MAC5B/J,QAAQ,CAAC4P,GAAG,CAAC7F,MAAM,CAAC3I,OAAO,CAACvB,IAAI,EAAEkK,MAAM,CAAC3I,OAAO,CAAClB,IAAI,CAAC;IACxD,CAAC,CAAC;IAEF,OAAO2P,KAAK,CAAC/Q,IAAI,CAACkB,QAAQ,CAAC8P,OAAO,EAAE,CAAC,CAACzF,GAAG,CAAC,CAAC,CAACxK,IAAI,EAAEK,IAAI,CAAC,MAAM;MAAEL,IAAI;MAAEK;IAAI,CAAE,CAAC,CAAC;EAC/E;EAEA;;;EAGAN,mBAAmBA,CAACmQ,WAAmB;IACrC,MAAM/F,KAAK,GAAG,IAAI,CAACjK,OAAO,CAACC,QAAQ,CAACgQ,OAAO,CAACD,WAAW,CAAC;IACxD,IAAI/F,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjK,OAAO,CAACC,QAAQ,CAACiQ,MAAM,CAACjG,KAAK,EAAE,CAAC,CAAC;KACvC,MAAM;MACL,IAAI,CAACjK,OAAO,CAACC,QAAQ,CAACmK,IAAI,CAAC4F,WAAW,CAAC;;IAEzC,IAAI,CAACpG,YAAY,EAAE;EACrB;EAEA;;;EAGAuG,iBAAiBA,CAAC1O,KAAa;IAC7B,MAAMwI,KAAK,GAAG,IAAI,CAACjK,OAAO,CAACyB,KAAK,CAACwO,OAAO,CAACxO,KAAK,CAAC;IAC/C,IAAIwI,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACjK,OAAO,CAACyB,KAAK,CAACyO,MAAM,CAACjG,KAAK,EAAE,CAAC,CAAC;KACpC,MAAM;MACL,IAAI,CAACjK,OAAO,CAACyB,KAAK,CAAC2I,IAAI,CAAC3I,KAAK,CAAC;;IAEhC,IAAI,CAACmI,YAAY,EAAE;EACrB;EAEA;;;EAGAhJ,YAAYA,CAAA;IACV,IAAI,IAAI,CAACyG,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAMiI,MAAM,GAAG,IAAI,CAACtG,OAAO,CAACiD,GAAG,CAACsD,CAAC,IAAIA,CAAC,CAAC5I,KAAK,CAACuH,MAAM,CAAC;MACpD,IAAI,CAACvM,OAAO,CAACsH,QAAQ,GAAG2E,IAAI,CAACvE,GAAG,CAAC,GAAGiG,MAAM,CAAC;MAC3C,IAAI,CAAC3N,OAAO,CAACuH,QAAQ,GAAG0E,IAAI,CAACtE,GAAG,CAAC,GAAGgG,MAAM,CAAC;MAC3C,IAAI,CAAC3N,OAAO,CAACwH,YAAY,GAAG,IAAI,CAACxH,OAAO,CAACuH,QAAQ;KAClD,MAAM;MACL,IAAI,CAACvH,OAAO,CAACsH,QAAQ,GAAG,CAAC;MACzB,IAAI,CAACtH,OAAO,CAACuH,QAAQ,GAAG,IAAI;MAC5B,IAAI,CAACvH,OAAO,CAACwH,YAAY,GAAG,IAAI;;IAGlC,IAAI,CAACxH,OAAO,CAACC,QAAQ,GAAG,EAAE;IAC1B,IAAI,CAACD,OAAO,CAACyB,KAAK,GAAG,EAAE;IACvB,IAAI,CAACzB,OAAO,CAACyH,aAAa,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAE;IAChD,IAAI,CAAC3H,OAAO,CAAC+B,QAAQ,GAAG;MAAE2F,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAE;IAE3C,IAAI,CAACiC,YAAY,EAAE;EACrB;EAEA;;;EAGAwG,eAAeA,CAAA;IACb,IAAI,CAACvI,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IAC1D,IAAI,CAACiG,WAAW,EAAE;EACpB;EAEA;;;EAGAnH,cAAcA,CAAA;IACZ,MAAM0J,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAIC,KAAK,GAAGtE,IAAI,CAACtE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxB,WAAW,GAAG8F,IAAI,CAACC,KAAK,CAACoE,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIE,GAAG,GAAGvE,IAAI,CAACvE,GAAG,CAAC,IAAI,CAACd,UAAU,EAAE2J,KAAK,GAAGD,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIE,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGD,UAAU,EAAE;MAChCC,KAAK,GAAGtE,IAAI,CAACtE,GAAG,CAAC,CAAC,EAAE6I,GAAG,GAAGF,UAAU,GAAG,CAAC,CAAC;;IAG3C,KAAK,IAAIG,CAAC,GAAGF,KAAK,EAAEE,CAAC,IAAID,GAAG,EAAEC,CAAC,EAAE,EAAE;MACjCJ,KAAK,CAACjG,IAAI,CAACqG,CAAC,CAAC;;IAGf,OAAOJ,KAAK;EACd;EAEA;;;EAGAzK,eAAeA,CAACqE,KAAa,EAAED,MAAoB;IACjD,OAAOA,MAAM,CAACpF,EAAE;EAClB;EAEA;;;EAGArB,mBAAmBA,CAAC0G,KAAa,EAAEqC,OAAsB;IACvD,OAAOrC,KAAK;EACd;EAEA;;;EAGA3H,kBAAkBA,CAACgK,OAAsB;IACvC,IAAIA,OAAO,CAACjL,OAAO,CAACC,IAAI,EAAE;MACxB;MACA,MAAMqN,QAAQ,GAAG,IAAI,CAACzH,kBAAkB,CAAC0H,WAAW,CAACtC,OAAO,CAACjL,OAAO,CAACvB,IAAI,EAAEwM,OAAO,CAACjL,OAAO,CAACC,IAAI,CAAC;MAChG,IAAIqN,QAAQ,EAAE;QACZrC,OAAO,CAACjL,OAAO,CAACC,IAAI,GAAGqN,QAAQ;QAC/B5F,OAAO,CAAC2F,GAAG,CAAC,uCAAuCpC,OAAO,CAACjL,OAAO,CAACvB,IAAI,KAAK6O,QAAQ,EAAE,CAAC;QACvF;;;IAIJ;IACArC,OAAO,CAACjL,OAAO,CAAC6B,SAAS,GAAG,IAAI;IAChC6F,OAAO,CAAC2F,GAAG,CAAC,8BAA8BpC,OAAO,CAACjL,OAAO,CAACvB,IAAI,EAAE,CAAC;EACnE;EAEA;;;EAGA2C,iBAAiBA,CAAC6J,OAAsB;IACtCA,OAAO,CAACjL,OAAO,CAAC6B,SAAS,GAAG,KAAK;EACnC;EAEA;;;EAGAwN,WAAWA,CAAA;IACT,IAAI,IAAI,CAACrJ,OAAO,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IACvC,OAAOuG,IAAI,CAACvE,GAAG,CAAC,GAAG,IAAI,CAACL,OAAO,CAACiD,GAAG,CAACsD,CAAC,IAAIA,CAAC,CAAC5I,KAAK,CAACuH,MAAM,CAAC,CAAC;EAC3D;EAEA;;;EAGAoE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACtJ,OAAO,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC1C,OAAOuG,IAAI,CAACtE,GAAG,CAAC,GAAG,IAAI,CAACN,OAAO,CAACiD,GAAG,CAACsD,CAAC,IAAIA,CAAC,CAAC5I,KAAK,CAACuH,MAAM,CAAC,CAAC;EAC3D;EAEA;;;EAGAqE,kBAAkBA,CAAC5L,KAAa;IAC9B,IAAI,CAAChF,OAAO,CAACwH,YAAY,GAAGxC,KAAK;IACjC,IAAI,CAAC4E,YAAY,EAAE;EACrB;EAEA;;;EAGQT,mBAAmBA,CAAA;IACzB,OAAO;MACLW,IAAI,EAAE;QACJzC,OAAO,EAAE,CACP;UACEzC,EAAE,EAAE,UAAU;UACdvD,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAkB,CAAE;UACjDwB,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAClFjB,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAC7ElB,QAAQ,EAAE,QAAQ;UAClBN,KAAK,EAAE,CAAC;UACRuD,KAAK,EAAE;YAAEuH,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvCzH,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,IAAI;UAChBI,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACER,EAAE,EAAE,UAAU;UACdvD,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAU,CAAE;UACzCwB,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAClFjB,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAC7ElB,QAAQ,EAAE,QAAQ;UAClBN,KAAK,EAAE,CAAC;UACRuD,KAAK,EAAE;YAAEuH,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvCzH,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,KAAK;UACjBI,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACER,EAAE,EAAE,UAAU;UACdvD,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAY,CAAE;UAC3CwB,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAClFjB,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAC7ElB,QAAQ,EAAE,QAAQ;UAClBN,KAAK,EAAE,CAAC;UACRuD,KAAK,EAAE;YAAEuH,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvCzH,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,IAAI;UAChBI,OAAO,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAM;SAC5C,EACD;UACER,EAAE,EAAE,UAAU;UACdvD,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAW,CAAE;UAC1CwB,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAClFjB,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAC7ElB,QAAQ,EAAE,QAAQ;UAClBN,KAAK,EAAE,CAAC;UACRuD,KAAK,EAAE;YAAEuH,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvCzH,KAAK,EAAE,UAAU;UACjBD,UAAU,EAAE,IAAI;UAChBI,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACER,EAAE,EAAE,UAAU;UACdvD,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAU,CAAE;UACzCwB,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAClFjB,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAEqB,IAAI,EAAE;UAAY,CAAE;UAC7ElB,QAAQ,EAAE,SAAS;UACnBN,KAAK,EAAE,CAAC;UACRuD,KAAK,EAAE;YAAEuH,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvCzH,KAAK,EAAE,UAAU;UACjBD,UAAU,EAAE,IAAI;UAChBI,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C;;KAGN;EACH;;;uBA5vBWyB,sBAAsB,EAAA1J,EAAA,CAAA0T,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAA5T,EAAA,CAAA0T,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA9T,EAAA,CAAA0T,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAA/T,EAAA,CAAA0T,iBAAA,CAAAM,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAtBvK,sBAAsB;MAAAwK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClGnCxU,EAAA,CAAAC,cAAA,aAAsC;UAIhCD,EAAA,CAAAW,UAAA,IAAA+T,qCAAA,kBAwCM;UACR1U,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,aAA6B;UAInBD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,gBAAgE;UAArCD,EAAA,CAAAe,UAAA,mBAAA4T,wDAAA;YAAA,OAAAF,GAAA,CAAA9J,WAAA,IAAA8J,GAAA,CAAA9J,WAAA;UAAA,EAAoC;UAC7D3K,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,eAAiH;UACnHF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAa,eAAA,EAA4B;UAA5Bb,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,eAA0B;UAEED,EAAA,CAAAI,MAAA,IAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACnDH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAI,MAAA,IAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAErDH,EAAA,CAAAC,cAAA,iBAM4B;UALrBD,EAAA,CAAAe,UAAA,2BAAA6T,gEAAAxN,MAAA;YAAA,OAAAqN,GAAA,CAAA5R,OAAA,CAAAwH,YAAA,GAAAjD,MAAA;UAAA,EAAkC,mBAAAyN,wDAAAzN,MAAA;YAAA,OAIzBqN,GAAA,CAAAhB,kBAAA,CAAArM,MAAA,CAAA0N,MAAA,CAAAC,KAAA,CAA6C;UAAA,EAJpB;UADzC/U,EAAA,CAAAG,YAAA,EAM4B;UAC5BH,EAAA,CAAAC,cAAA,eAA2B;UACCD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,IAA2B;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKlEH,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,iCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAW,UAAA,KAAAqU,wCAAA,oBAOQ;UACVhV,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,wBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,eAA2B;UAIhBD,EAAA,CAAAe,UAAA,oBAAAkU,yDAAA;YAAA,OAAUR,GAAA,CAAAzB,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtChT,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,iBAA8B;UAGrBD,EAAA,CAAAe,UAAA,oBAAAmU,yDAAA;YAAA,OAAUT,GAAA,CAAAzB,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtChT,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,iBAA8B;UAGrBD,EAAA,CAAAe,UAAA,oBAAAoU,yDAAA;YAAA,OAAUV,GAAA,CAAAzB,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtChT,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAK7BH,EAAA,CAAAC,cAAA,eAA4B;UACAD,EAAA,CAAAe,UAAA,mBAAAqU,yDAAA;YAAA,OAASX,GAAA,CAAAhR,YAAA,EAAc;UAAA,EAAC;UAACzD,EAAA,CAAAI,MAAA,2BAAmB;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAKnFH,EAAA,CAAAC,cAAA,gBAA2B;UAIaD,EAAA,CAAAe,UAAA,mBAAAsU,yDAAA;YAAA,OAAAZ,GAAA,CAAA9J,WAAA,IAAA8J,GAAA,CAAA9J,WAAA;UAAA,EAAoC;UACpE3K,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAoG;UACtGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAa,eAAA,EAAyB;UAAzBb,EAAA,CAAAC,cAAA,eAAyB;UAGfD,EAAA,CAAAe,UAAA,mBAAAuU,yDAAA;YAAA,OAAAb,GAAA,CAAApM,QAAA,GAAoB,MAAM;UAAA,EAAC;UACjCrI,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAkG;UACpGF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAa,eAAA,EAEoC;UAFpCb,EAAA,CAAAC,cAAA,kBAEoC;UAA5BD,EAAA,CAAAe,UAAA,mBAAAwU,yDAAA;YAAA,OAAAd,GAAA,CAAApM,QAAA,GAAoB,MAAM;UAAA,EAAC;UACjCrI,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAmG;UACrGF,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAa,eAAA,EAA2B;UAA3Bb,EAAA,CAAAC,cAAA,eAA2B;UAEhBD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAAC,cAAA,kBAAsE;UAA9DD,EAAA,CAAAe,UAAA,2BAAAyU,iEAAApO,MAAA;YAAA,OAAAqN,GAAA,CAAAhK,MAAA,GAAArD,MAAA;UAAA,EAAoB,oBAAAqO,0DAAA;YAAWhB,GAAA,CAAA9D,WAAA,EAAa;YAAA,OAAE8D,GAAA,CAAAhI,YAAA,EAAc;UAAA,EAAxC;UAC1BzM,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAI,MAAA,kBAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,4BAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAEpDH,EAAA,CAAAC,cAAA,kBAA2D;UAA5BD,EAAA,CAAAe,UAAA,mBAAA2U,yDAAA;YAAA,OAASjB,GAAA,CAAAxB,eAAA,EAAiB;UAAA,EAAC;UACxDjT,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAW,UAAA,KAAAgV,4CAAA,mBAAoF;UACpF3V,EAAA,CAAAW,UAAA,KAAAiV,4CAAA,mBAAoF;UACtF5V,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAa,eAAA,EAA2B;UAA3Bb,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKXH,EAAA,CAAAW,UAAA,KAAAkV,sCAAA,kBAGM;UAGN7V,EAAA,CAAAW,UAAA,KAAAmV,sCAAA,mBASM;UAGN9V,EAAA,CAAAW,UAAA,KAAAoV,sCAAA,kBA+NM;UAGN/V,EAAA,CAAAW,UAAA,KAAAqV,sCAAA,mBA2BM;UACRhW,EAAA,CAAAG,YAAA,EAAO;;;UApcwBH,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAAjU,aAAA,CAAmB;UA+CnBR,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAA6F,WAAA,YAAA4O,GAAA,CAAA9J,WAAA,CAA6B;UAc5B3K,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAM,kBAAA,KAAAmU,GAAA,CAAAlB,WAAA,aAAoB;UACpBvT,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAM,kBAAA,KAAAmU,GAAA,CAAAjB,WAAA,aAAoB;UAGvCxT,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAA5R,OAAA,CAAAwH,YAAA,CAAkC,QAAAoK,GAAA,CAAAlB,WAAA,WAAAkB,GAAA,CAAAjB,WAAA;UAQbxT,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,kBAAA,KAAAmU,GAAA,CAAA5R,OAAA,CAAAwH,YAAA,WAA2B;UAQ5BrK,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAAjC,iBAAA,GAAsB;UAkBxCxS,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAA5R,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UAQrC/C,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAA5R,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UAQrC/C,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAA5R,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UA0BpC/C,EAAA,CAAAK,SAAA,IAAoC;UAApCL,EAAA,CAAA6F,WAAA,WAAA4O,GAAA,CAAApM,QAAA,YAAoC;UAOpCrI,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAA6F,WAAA,WAAA4O,GAAA,CAAApM,QAAA,YAAoC;UAYpCrI,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAA6B,UAAA,YAAA4S,GAAA,CAAAhK,MAAA,CAAoB;UAOjBzK,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAA/J,SAAA,WAAyB;UACzB1K,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAA/J,SAAA,YAA0B;UAMrC1K,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAAmU,GAAA,CAAAnM,eAAA,CAAAC,MAAA,qBACF;UAKEvI,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAAxK,OAAA,CAAa;UAMbjK,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAApR,KAAA,CAAW;UAYXrD,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA6B,UAAA,UAAA4S,GAAA,CAAAxK,OAAA,KAAAwK,GAAA,CAAApR,KAAA,CAAwB;UAkOxBrD,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAA6B,UAAA,SAAA4S,GAAA,CAAAhL,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}