{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AirlineLogoService {\n  constructor() {\n    this.logoMap = {\n      'TK': ['https://content.airhex.com/content/logos/airlines_TK_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png'],\n      'TU': ['https://content.airhex.com/content/logos/airlines_TU_200_200_s.png', 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/200px-Tunisair_Logo.svg.png'],\n      'AF': ['https://content.airhex.com/content/logos/airlines_AF_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'],\n      'LH': ['https://content.airhex.com/content/logos/airlines_LH_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'],\n      'EK': ['https://content.airhex.com/content/logos/airlines_EK_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'],\n      'QR': ['https://content.airhex.com/content/logos/airlines_QR_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'],\n      'BA': ['https://content.airhex.com/content/logos/airlines_BA_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'],\n      'KL': ['https://content.airhex.com/content/logos/airlines_KL_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'],\n      'IB': ['https://content.airhex.com/content/logos/airlines_IB_200_200_s.png', 'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'],\n      'AZ': ['https://content.airhex.com/content/logos/airlines_AZ_200_200_s.png', 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/200px-Alitalia_logo_2015.svg.png'],\n      'MS': ['https://content.airhex.com/content/logos/airlines_MS_200_200_s.png'],\n      'SV': ['https://content.airhex.com/content/logos/airlines_SV_200_200_s.png'],\n      'RJ': ['https://content.airhex.com/content/logos/airlines_RJ_200_200_s.png'],\n      'UX': ['https://content.airhex.com/content/logos/airlines_UX_200_200_s.png']\n    };\n  }\n  /**\n   * Obtient le logo principal d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode) {\n    const logos = this.logoMap[airlineCode];\n    return logos && logos.length > 0 ? logos[0] : null;\n  }\n  /**\n   * Obtient tous les logos alternatifs d'une compagnie aérienne\n   */\n  getAlternativeLogos(airlineCode) {\n    const logos = this.logoMap[airlineCode];\n    return logos ? logos.slice(1) : [];\n  }\n  /**\n   * Obtient le logo suivant en cas d'erreur\n   */\n  getNextLogo(airlineCode, currentUrl) {\n    const logos = this.logoMap[airlineCode];\n    if (!logos) return null;\n    const currentIndex = logos.indexOf(currentUrl);\n    if (currentIndex >= 0 && currentIndex < logos.length - 1) {\n      return logos[currentIndex + 1];\n    }\n    return null;\n  }\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode) {\n    return airlineCode in this.logoMap;\n  }\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines() {\n    return Object.keys(this.logoMap);\n  }\n  static {\n    this.ɵfac = function AirlineLogoService_Factory(t) {\n      return new (t || AirlineLogoService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AirlineLogoService,\n      factory: AirlineLogoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AirlineLogoService", "constructor", "logoMap", "getAirlineLogo", "airlineCode", "logos", "length", "getAlternativeLogos", "slice", "getNextLogo", "currentUrl", "currentIndex", "indexOf", "<PERSON><PERSON><PERSON>", "getAvailableAirlines", "Object", "keys", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\airline-logo.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AirlineLogoService {\n  private logoMap: { [key: string]: string[] } = {\n    'TK': [\n      'https://content.airhex.com/content/logos/airlines_TK_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png'\n    ],\n    'TU': [\n      'https://content.airhex.com/content/logos/airlines_TU_200_200_s.png',\n      'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/200px-Tunisair_Logo.svg.png'\n    ],\n    'AF': [\n      'https://content.airhex.com/content/logos/airlines_AF_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'\n    ],\n    'LH': [\n      'https://content.airhex.com/content/logos/airlines_LH_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'\n    ],\n    'EK': [\n      'https://content.airhex.com/content/logos/airlines_EK_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'\n    ],\n    'QR': [\n      'https://content.airhex.com/content/logos/airlines_QR_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'\n    ],\n    'BA': [\n      'https://content.airhex.com/content/logos/airlines_BA_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'\n    ],\n    'KL': [\n      'https://content.airhex.com/content/logos/airlines_KL_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'\n    ],\n    'IB': [\n      'https://content.airhex.com/content/logos/airlines_IB_200_200_s.png',\n      'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'\n    ],\n    'AZ': [\n      'https://content.airhex.com/content/logos/airlines_AZ_200_200_s.png',\n      'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/200px-Alitalia_logo_2015.svg.png'\n    ],\n    'MS': [\n      'https://content.airhex.com/content/logos/airlines_MS_200_200_s.png'\n    ],\n    'SV': [\n      'https://content.airhex.com/content/logos/airlines_SV_200_200_s.png'\n    ],\n    'RJ': [\n      'https://content.airhex.com/content/logos/airlines_RJ_200_200_s.png'\n    ],\n    'UX': [\n      'https://content.airhex.com/content/logos/airlines_UX_200_200_s.png'\n    ]\n  };\n\n  constructor() { }\n\n  /**\n   * Obtient le logo principal d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode: string): string | null {\n    const logos = this.logoMap[airlineCode];\n    return logos && logos.length > 0 ? logos[0] : null;\n  }\n\n  /**\n   * Obtient tous les logos alternatifs d'une compagnie aérienne\n   */\n  getAlternativeLogos(airlineCode: string): string[] {\n    const logos = this.logoMap[airlineCode];\n    return logos ? logos.slice(1) : [];\n  }\n\n  /**\n   * Obtient le logo suivant en cas d'erreur\n   */\n  getNextLogo(airlineCode: string, currentUrl: string): string | null {\n    const logos = this.logoMap[airlineCode];\n    if (!logos) return null;\n\n    const currentIndex = logos.indexOf(currentUrl);\n    if (currentIndex >= 0 && currentIndex < logos.length - 1) {\n      return logos[currentIndex + 1];\n    }\n\n    return null;\n  }\n\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode: string): boolean {\n    return airlineCode in this.logoMap;\n  }\n\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines(): string[] {\n    return Object.keys(this.logoMap);\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAwD7BC,YAAA;IAvDQ,KAAAC,OAAO,GAAgC;MAC7C,IAAI,EAAE,CACJ,oEAAoE,EACpE,8EAA8E,CAC/E;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,yGAAyG,CAC1G;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,wEAAwE,CACzE;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,uEAAuE,CACxE;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,sEAAsE,CACvE;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,2EAA2E,CAC5E;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,6EAA6E,CAC9E;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,iEAAiE,CAClE;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,oEAAoE,CACrE;MACD,IAAI,EAAE,CACJ,oEAAoE,EACpE,mHAAmH,CACpH;MACD,IAAI,EAAE,CACJ,oEAAoE,CACrE;MACD,IAAI,EAAE,CACJ,oEAAoE,CACrE;MACD,IAAI,EAAE,CACJ,oEAAoE,CACrE;MACD,IAAI,EAAE,CACJ,oEAAoE;KAEvE;EAEe;EAEhB;;;EAGAC,cAAcA,CAACC,WAAmB;IAChC,MAAMC,KAAK,GAAG,IAAI,CAACH,OAAO,CAACE,WAAW,CAAC;IACvC,OAAOC,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;EACpD;EAEA;;;EAGAE,mBAAmBA,CAACH,WAAmB;IACrC,MAAMC,KAAK,GAAG,IAAI,CAACH,OAAO,CAACE,WAAW,CAAC;IACvC,OAAOC,KAAK,GAAGA,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EACpC;EAEA;;;EAGAC,WAAWA,CAACL,WAAmB,EAAEM,UAAkB;IACjD,MAAML,KAAK,GAAG,IAAI,CAACH,OAAO,CAACE,WAAW,CAAC;IACvC,IAAI,CAACC,KAAK,EAAE,OAAO,IAAI;IAEvB,MAAMM,YAAY,GAAGN,KAAK,CAACO,OAAO,CAACF,UAAU,CAAC;IAC9C,IAAIC,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAGN,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;MACxD,OAAOD,KAAK,CAACM,YAAY,GAAG,CAAC,CAAC;;IAGhC,OAAO,IAAI;EACb;EAEA;;;EAGAE,OAAOA,CAACT,WAAmB;IACzB,OAAOA,WAAW,IAAI,IAAI,CAACF,OAAO;EACpC;EAEA;;;EAGAY,oBAAoBA,CAAA;IAClB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACd,OAAO,CAAC;EAClC;;;uBArGWF,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAiB,OAAA,EAAlBjB,kBAAkB,CAAAkB,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}