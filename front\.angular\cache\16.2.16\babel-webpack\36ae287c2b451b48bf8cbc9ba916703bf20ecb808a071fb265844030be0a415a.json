{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class DashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 128,\n      vars: 0,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"header-content\"], [1, \"logo-section\"], [1, \"logo\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"logo-icon\"], [\"d\", \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"], [1, \"logo-text\"], [1, \"demo-info\"], [1, \"demo-id\"], [1, \"demo-details\"], [1, \"header-contact\"], [1, \"contact-item\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"contact-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"], [1, \"header-nav\"], [\"href\", \"#\", 1, \"nav-item\", \"active\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"nav-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"], [\"href\", \"#\", 1, \"nav-item\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"], [1, \"logout-btn\", 3, \"click\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"], [1, \"dashboard-main\"], [1, \"dashboard-grid\"], [1, \"sidebar\"], [1, \"sidebar-section\"], [1, \"sidebar-item\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"sidebar-icon\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"], [\"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\", \"stroke-width\", \"2\", \"d\", \"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"], [1, \"main-content\"], [1, \"content-grid\"], [\"routerLink\", \"/flight-search\", 1, \"service-card\", \"flights-card\"], [1, \"card-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"], [1, \"card-description\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(5, \"svg\", 5);\n          i0.ɵɵelement(6, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"span\", 9);\n          i0.ɵɵtext(11, \"223730 - Workfront for Demo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"span\", 10);\n          i0.ɵɵtext(13, \"47988 - demo demo\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 13);\n          i0.ɵɵelement(17, \"path\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(18, \"span\");\n          i0.ɵɵtext(19, \"00962-79923031\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(21, \"svg\", 13);\n          i0.ɵɵelement(22, \"path\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"span\");\n          i0.ɵɵtext(24, \"<EMAIL>\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(26, \"svg\", 13);\n          i0.ɵɵelement(27, \"path\", 16)(28, \"path\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(29, \"span\");\n          i0.ɵɵtext(30, \"Bahrain: 8630 48 TND\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 12);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(32, \"svg\", 13);\n          i0.ɵɵelement(33, \"path\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(34, \"span\");\n          i0.ɵɵtext(35, \"UTC: +3468 52 TND\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"nav\", 19)(37, \"a\", 20);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 21);\n          i0.ɵɵelement(39, \"path\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(40, \" Home \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(41, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(42, \"svg\", 21);\n          i0.ɵɵelement(43, \"path\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" News \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(45, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(46, \"svg\", 21);\n          i0.ɵɵelement(47, \"path\", 25)(48, \"path\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Tools \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(50, \"a\", 23);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(51, \"svg\", 21);\n          i0.ɵɵelement(52, \"path\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Languages \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(54, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_54_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(55, \"svg\", 21);\n          i0.ɵɵelement(56, \"path\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(58, \"main\", 30)(59, \"div\", 31)(60, \"aside\", 32)(61, \"div\", 33)(62, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 35);\n          i0.ɵɵelement(64, \"path\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(65, \"span\");\n          i0.ɵɵtext(66, \"Booking Queue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(68, \"svg\", 35);\n          i0.ɵɵelement(69, \"path\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(70, \"span\");\n          i0.ɵɵtext(71, \"Support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(73, \"svg\", 35);\n          i0.ɵɵelement(74, \"path\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(75, \"span\");\n          i0.ɵɵtext(76, \"Helpdesk\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(77, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(78, \"svg\", 35);\n          i0.ɵɵelement(79, \"path\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(80, \"span\");\n          i0.ɵɵtext(81, \"Finance\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(82, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(83, \"svg\", 35);\n          i0.ɵɵelement(84, \"path\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(85, \"span\");\n          i0.ɵɵtext(86, \"Passengers\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"div\", 33)(88, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(89, \"svg\", 35);\n          i0.ɵɵelement(90, \"path\", 40);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(91, \"span\");\n          i0.ɵɵtext(92, \"Commissions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(94, \"svg\", 35);\n          i0.ɵɵelement(95, \"path\", 41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(96, \"span\");\n          i0.ɵɵtext(97, \"Staff Agent\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(98, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(99, \"svg\", 35);\n          i0.ɵɵelement(100, \"path\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(101, \"span\");\n          i0.ɵɵtext(102, \"Package\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(104, \"svg\", 35);\n          i0.ɵɵelement(105, \"path\", 43);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(106, \"span\");\n          i0.ɵɵtext(107, \"Flight Info\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(108, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(109, \"svg\", 35);\n          i0.ɵɵelement(110, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(111, \"span\");\n          i0.ɵɵtext(112, \"Agency Profile\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(114, \"svg\", 35);\n          i0.ɵɵelement(115, \"path\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(116, \"span\");\n          i0.ɵɵtext(117, \"Credit request\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(118, \"div\", 46)(119, \"div\", 47)(120, \"div\", 48)(121, \"div\", 49);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(122, \"svg\", 50);\n          i0.ɵɵelement(123, \"path\", 51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(124, \"h3\");\n          i0.ɵɵtext(125, \"Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"p\", 52);\n          i0.ɵɵtext(127, \"Search and book flights\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      dependencies: [i2.RouterLink],\n      styles: [\"\\n\\n    [_nghost-%COMP%] {\\n      --primary-blue: #4a90e2;\\n      --secondary-blue: #7bb3f0;\\n      --dark-blue: #2c5aa0;\\n      --light-gray: #f5f7fa;\\n      --medium-gray: #8fa4b3;\\n      --dark-gray: #4a5568;\\n      --white: #ffffff;\\n      --border-color: #e2e8f0;\\n      --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);\\n      --transition: all 0.3s ease;\\n    }\\n\\n    \\n\\n    .dashboard-container[_ngcontent-%COMP%] {\\n      min-height: 100vh;\\n      background: var(--light-gray);\\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n      display: flex;\\n      flex-direction: column;\\n    }\\n\\n    \\n\\n    .dashboard-header[_ngcontent-%COMP%] {\\n      background: var(--white);\\n      border-bottom: 1px solid var(--border-color);\\n      box-shadow: var(--shadow);\\n    }\\n\\n    .header-content[_ngcontent-%COMP%] {\\n      max-width: 1400px;\\n      margin: 0 auto;\\n      padding: 1rem 2rem;\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n      flex-wrap: wrap;\\n      gap: 1rem;\\n    }\\n\\n    \\n\\n    .logo-section[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 2rem;\\n    }\\n\\n    .logo[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.75rem;\\n    }\\n\\n    .logo-icon[_ngcontent-%COMP%] {\\n      width: 2rem;\\n      height: 2rem;\\n      color: var(--primary-blue);\\n    }\\n\\n    .logo-text[_ngcontent-%COMP%] {\\n      font-size: 1.25rem;\\n      font-weight: 700;\\n      color: var(--dark-gray);\\n      letter-spacing: -0.025em;\\n    }\\n\\n    .demo-info[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 0.25rem;\\n    }\\n\\n    .demo-id[_ngcontent-%COMP%] {\\n      font-size: 0.875rem;\\n      font-weight: 600;\\n      color: var(--dark-gray);\\n    }\\n\\n    .demo-details[_ngcontent-%COMP%] {\\n      font-size: 0.75rem;\\n      color: var(--medium-gray);\\n    }\\n\\n    \\n\\n    .header-contact[_ngcontent-%COMP%] {\\n      display: flex;\\n      gap: 2rem;\\n      flex-wrap: wrap;\\n    }\\n\\n    .contact-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n      font-size: 0.875rem;\\n      color: var(--dark-gray);\\n    }\\n\\n    .contact-icon[_ngcontent-%COMP%] {\\n      width: 1rem;\\n      height: 1rem;\\n      color: var(--medium-gray);\\n    }\\n\\n    \\n\\n    .header-nav[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 1.5rem;\\n    }\\n\\n    .nav-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n      padding: 0.5rem 1rem;\\n      border-radius: 0.5rem;\\n      text-decoration: none;\\n      color: var(--dark-gray);\\n      font-size: 0.875rem;\\n      font-weight: 500;\\n      transition: var(--transition);\\n    }\\n\\n    .nav-item[_ngcontent-%COMP%]:hover {\\n      background: var(--light-gray);\\n      color: var(--primary-blue);\\n    }\\n\\n    .nav-item.active[_ngcontent-%COMP%] {\\n      background: var(--primary-blue);\\n      color: var(--white);\\n    }\\n\\n    .nav-icon[_ngcontent-%COMP%] {\\n      width: 1.25rem;\\n      height: 1.25rem;\\n    }\\n\\n    .logout-btn[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.5rem;\\n      padding: 0.5rem 1rem;\\n      background: transparent;\\n      border: 1px solid var(--border-color);\\n      border-radius: 0.5rem;\\n      color: var(--dark-gray);\\n      font-size: 0.875rem;\\n      font-weight: 500;\\n      cursor: pointer;\\n      transition: var(--transition);\\n    }\\n\\n    .logout-btn[_ngcontent-%COMP%]:hover {\\n      background: #fee2e2;\\n      border-color: #fca5a5;\\n      color: #dc2626;\\n    }\\n\\n    \\n\\n    .dashboard-main[_ngcontent-%COMP%] {\\n      flex: 1;\\n      padding: 2rem;\\n    }\\n\\n    .dashboard-grid[_ngcontent-%COMP%] {\\n      max-width: 1400px;\\n      margin: 0 auto;\\n      display: grid;\\n      grid-template-columns: 280px 1fr;\\n      gap: 2rem;\\n      height: calc(100vh - 120px);\\n    }\\n\\n    \\n\\n    .sidebar[_ngcontent-%COMP%] {\\n      background: var(--white);\\n      border-radius: 0.75rem;\\n      padding: 1.5rem;\\n      box-shadow: var(--shadow);\\n      height: -moz-fit-content;\\n      height: fit-content;\\n    }\\n\\n    .sidebar-section[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 0.5rem;\\n      margin-bottom: 2rem;\\n    }\\n\\n    .sidebar-section[_ngcontent-%COMP%]:last-child {\\n      margin-bottom: 0;\\n    }\\n\\n    .sidebar-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 0.75rem;\\n      padding: 0.875rem 1rem;\\n      border-radius: 0.5rem;\\n      color: var(--dark-gray);\\n      font-size: 0.875rem;\\n      font-weight: 500;\\n      cursor: pointer;\\n      transition: var(--transition);\\n    }\\n\\n    .sidebar-item[_ngcontent-%COMP%]:hover {\\n      background: var(--light-gray);\\n      color: var(--primary-blue);\\n    }\\n\\n    .sidebar-icon[_ngcontent-%COMP%] {\\n      width: 1.25rem;\\n      height: 1.25rem;\\n      color: var(--medium-gray);\\n      transition: var(--transition);\\n    }\\n\\n    .sidebar-item[_ngcontent-%COMP%]:hover   .sidebar-icon[_ngcontent-%COMP%] {\\n      color: var(--primary-blue);\\n    }\\n\\n    \\n\\n    .main-content[_ngcontent-%COMP%] {\\n      background: var(--white);\\n      border-radius: 0.75rem;\\n      padding: 2rem;\\n      box-shadow: var(--shadow);\\n      overflow: hidden;\\n    }\\n\\n    .content-grid[_ngcontent-%COMP%] {\\n      display: grid;\\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n      gap: 2rem;\\n      height: 100%;\\n    }\\n\\n    \\n\\n    .service-card[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);\\n      border-radius: 1rem;\\n      padding: 2rem;\\n      color: var(--white);\\n      display: flex;\\n      flex-direction: column;\\n      align-items: center;\\n      justify-content: center;\\n      text-align: center;\\n      cursor: pointer;\\n      transition: var(--transition);\\n      min-height: 200px;\\n      position: relative;\\n      overflow: hidden;\\n    }\\n\\n    .service-card[_ngcontent-%COMP%]::before {\\n      content: '';\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      right: 0;\\n      bottom: 0;\\n      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n      opacity: 0;\\n      transition: var(--transition);\\n    }\\n\\n    .service-card[_ngcontent-%COMP%]:hover::before {\\n      opacity: 1;\\n    }\\n\\n    .service-card[_ngcontent-%COMP%]:hover {\\n      transform: translateY(-4px);\\n      box-shadow: var(--shadow-lg);\\n    }\\n\\n    .card-icon[_ngcontent-%COMP%] {\\n      width: 4rem;\\n      height: 4rem;\\n      margin-bottom: 1rem;\\n      background: rgba(255, 255, 255, 0.2);\\n      border-radius: 50%;\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n    .card-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n      width: 2rem;\\n      height: 2rem;\\n      color: var(--white);\\n    }\\n\\n    .service-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      font-size: 1.5rem;\\n      font-weight: 700;\\n      margin: 0;\\n      letter-spacing: -0.025em;\\n    }\\n\\n    \\n\\n    @media (max-width: 1200px) {\\n      .dashboard-grid[_ngcontent-%COMP%] {\\n        grid-template-columns: 1fr;\\n        gap: 1.5rem;\\n      }\\n\\n      .sidebar[_ngcontent-%COMP%] {\\n        order: 2;\\n      }\\n\\n      .main-content[_ngcontent-%COMP%] {\\n        order: 1;\\n      }\\n    }\\n\\n    @media (max-width: 768px) {\\n      .header-content[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: flex-start;\\n        gap: 1.5rem;\\n      }\\n\\n      .logo-section[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        align-items: flex-start;\\n        gap: 1rem;\\n      }\\n\\n      .header-contact[_ngcontent-%COMP%] {\\n        flex-direction: column;\\n        gap: 0.75rem;\\n      }\\n\\n      .header-nav[_ngcontent-%COMP%] {\\n        flex-wrap: wrap;\\n        gap: 1rem;\\n      }\\n\\n      .dashboard-main[_ngcontent-%COMP%] {\\n        padding: 1rem;\\n      }\\n\\n      .dashboard-grid[_ngcontent-%COMP%] {\\n        gap: 1rem;\\n      }\\n\\n      .sidebar[_ngcontent-%COMP%] {\\n        padding: 1rem;\\n      }\\n\\n      .main-content[_ngcontent-%COMP%] {\\n        padding: 1.5rem;\\n      }\\n\\n      .content-grid[_ngcontent-%COMP%] {\\n        grid-template-columns: 1fr;\\n        gap: 1rem;\\n      }\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "authService", "router", "ngOnInit", "userInfo", "getUserInfo", "logout", "navigate", "i0", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "DashboardComponent_Template_button_click_54_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styles: [`\n    /* Variables CSS pour Block to Book */\n    :host {\n      --primary-blue: #4a90e2;\n      --secondary-blue: #7bb3f0;\n      --dark-blue: #2c5aa0;\n      --light-gray: #f5f7fa;\n      --medium-gray: #8fa4b3;\n      --dark-gray: #4a5568;\n      --white: #ffffff;\n      --border-color: #e2e8f0;\n      --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n      --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);\n      --transition: all 0.3s ease;\n    }\n\n    /* Container principal */\n    .dashboard-container {\n      min-height: 100vh;\n      background: var(--light-gray);\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n      display: flex;\n      flex-direction: column;\n    }\n\n    /* Header */\n    .dashboard-header {\n      background: var(--white);\n      border-bottom: 1px solid var(--border-color);\n      box-shadow: var(--shadow);\n    }\n\n    .header-content {\n      max-width: 1400px;\n      margin: 0 auto;\n      padding: 1rem 2rem;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      flex-wrap: wrap;\n      gap: 1rem;\n    }\n\n    /* Logo Section */\n    .logo-section {\n      display: flex;\n      align-items: center;\n      gap: 2rem;\n    }\n\n    .logo {\n      display: flex;\n      align-items: center;\n      gap: 0.75rem;\n    }\n\n    .logo-icon {\n      width: 2rem;\n      height: 2rem;\n      color: var(--primary-blue);\n    }\n\n    .logo-text {\n      font-size: 1.25rem;\n      font-weight: 700;\n      color: var(--dark-gray);\n      letter-spacing: -0.025em;\n    }\n\n    .demo-info {\n      display: flex;\n      flex-direction: column;\n      gap: 0.25rem;\n    }\n\n    .demo-id {\n      font-size: 0.875rem;\n      font-weight: 600;\n      color: var(--dark-gray);\n    }\n\n    .demo-details {\n      font-size: 0.75rem;\n      color: var(--medium-gray);\n    }\n\n    /* Header Contact */\n    .header-contact {\n      display: flex;\n      gap: 2rem;\n      flex-wrap: wrap;\n    }\n\n    .contact-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      font-size: 0.875rem;\n      color: var(--dark-gray);\n    }\n\n    .contact-icon {\n      width: 1rem;\n      height: 1rem;\n      color: var(--medium-gray);\n    }\n\n    /* Header Navigation */\n    .header-nav {\n      display: flex;\n      align-items: center;\n      gap: 1.5rem;\n    }\n\n    .nav-item {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      padding: 0.5rem 1rem;\n      border-radius: 0.5rem;\n      text-decoration: none;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      transition: var(--transition);\n    }\n\n    .nav-item:hover {\n      background: var(--light-gray);\n      color: var(--primary-blue);\n    }\n\n    .nav-item.active {\n      background: var(--primary-blue);\n      color: var(--white);\n    }\n\n    .nav-icon {\n      width: 1.25rem;\n      height: 1.25rem;\n    }\n\n    .logout-btn {\n      display: flex;\n      align-items: center;\n      gap: 0.5rem;\n      padding: 0.5rem 1rem;\n      background: transparent;\n      border: 1px solid var(--border-color);\n      border-radius: 0.5rem;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      cursor: pointer;\n      transition: var(--transition);\n    }\n\n    .logout-btn:hover {\n      background: #fee2e2;\n      border-color: #fca5a5;\n      color: #dc2626;\n    }\n\n    /* Main Dashboard */\n    .dashboard-main {\n      flex: 1;\n      padding: 2rem;\n    }\n\n    .dashboard-grid {\n      max-width: 1400px;\n      margin: 0 auto;\n      display: grid;\n      grid-template-columns: 280px 1fr;\n      gap: 2rem;\n      height: calc(100vh - 120px);\n    }\n\n    /* Sidebar */\n    .sidebar {\n      background: var(--white);\n      border-radius: 0.75rem;\n      padding: 1.5rem;\n      box-shadow: var(--shadow);\n      height: fit-content;\n    }\n\n    .sidebar-section {\n      display: flex;\n      flex-direction: column;\n      gap: 0.5rem;\n      margin-bottom: 2rem;\n    }\n\n    .sidebar-section:last-child {\n      margin-bottom: 0;\n    }\n\n    .sidebar-item {\n      display: flex;\n      align-items: center;\n      gap: 0.75rem;\n      padding: 0.875rem 1rem;\n      border-radius: 0.5rem;\n      color: var(--dark-gray);\n      font-size: 0.875rem;\n      font-weight: 500;\n      cursor: pointer;\n      transition: var(--transition);\n    }\n\n    .sidebar-item:hover {\n      background: var(--light-gray);\n      color: var(--primary-blue);\n    }\n\n    .sidebar-icon {\n      width: 1.25rem;\n      height: 1.25rem;\n      color: var(--medium-gray);\n      transition: var(--transition);\n    }\n\n    .sidebar-item:hover .sidebar-icon {\n      color: var(--primary-blue);\n    }\n\n    /* Main Content */\n    .main-content {\n      background: var(--white);\n      border-radius: 0.75rem;\n      padding: 2rem;\n      box-shadow: var(--shadow);\n      overflow: hidden;\n    }\n\n    .content-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n      gap: 2rem;\n      height: 100%;\n    }\n\n    /* Service Cards */\n    .service-card {\n      background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);\n      border-radius: 1rem;\n      padding: 2rem;\n      color: var(--white);\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      text-align: center;\n      cursor: pointer;\n      transition: var(--transition);\n      min-height: 200px;\n      position: relative;\n      overflow: hidden;\n    }\n\n    .service-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\n      opacity: 0;\n      transition: var(--transition);\n    }\n\n    .service-card:hover::before {\n      opacity: 1;\n    }\n\n    .service-card:hover {\n      transform: translateY(-4px);\n      box-shadow: var(--shadow-lg);\n    }\n\n    .card-icon {\n      width: 4rem;\n      height: 4rem;\n      margin-bottom: 1rem;\n      background: rgba(255, 255, 255, 0.2);\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n    .card-icon svg {\n      width: 2rem;\n      height: 2rem;\n      color: var(--white);\n    }\n\n    .service-card h3 {\n      font-size: 1.5rem;\n      font-weight: 700;\n      margin: 0;\n      letter-spacing: -0.025em;\n    }\n\n    /* Responsive Design */\n    @media (max-width: 1200px) {\n      .dashboard-grid {\n        grid-template-columns: 1fr;\n        gap: 1.5rem;\n      }\n\n      .sidebar {\n        order: 2;\n      }\n\n      .main-content {\n        order: 1;\n      }\n    }\n\n    @media (max-width: 768px) {\n      .header-content {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 1.5rem;\n      }\n\n      .logo-section {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 1rem;\n      }\n\n      .header-contact {\n        flex-direction: column;\n        gap: 0.75rem;\n      }\n\n      .header-nav {\n        flex-wrap: wrap;\n        gap: 1rem;\n      }\n\n      .dashboard-main {\n        padding: 1rem;\n      }\n\n      .dashboard-grid {\n        gap: 1rem;\n      }\n\n      .sidebar {\n        padding: 1rem;\n      }\n\n      .main-content {\n        padding: 1.5rem;\n      }\n\n      .content-grid {\n        grid-template-columns: 1fr;\n        gap: 1rem;\n      }\n    }\n  `]\n})\nexport class DashboardComponent implements OnInit {\n  userInfo: any;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/login']);\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- Header avec navigation -->\n  <header class=\"dashboard-header\">\n    <div class=\"header-content\">\n      <div class=\"logo-section\">\n        <div class=\"logo\">\n          <svg class=\"logo-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n          </svg>\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <div class=\"demo-info\">\n          <span class=\"demo-id\">223730 - Workfront for Demo</span>\n          <span class=\"demo-details\">47988 - demo demo</span>\n        </div>\n      </div>\n\n      <div class=\"header-contact\">\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"/>\n          </svg>\n          <span>00962-79923031</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\n          </svg>\n          <span>info&#64;blocktobook.com</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n          </svg>\n          <span>Bahrain: 8630 48 TND</span>\n        </div>\n        <div class=\"contact-item\">\n          <svg class=\"contact-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n          </svg>\n          <span>UTC: +3468 52 TND</span>\n        </div>\n      </div>\n\n      <nav class=\"header-nav\">\n        <a href=\"#\" class=\"nav-item active\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\"/>\n          </svg>\n          Home\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\"/>\n          </svg>\n          News\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"/>\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"/>\n          </svg>\n          Tools\n        </a>\n        <a href=\"#\" class=\"nav-item\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"/>\n          </svg>\n          Languages\n        </a>\n        <button class=\"logout-btn\" (click)=\"logout()\">\n          <svg class=\"nav-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"/>\n          </svg>\n          Logout\n        </button>\n      </nav>\n    </div>\n  </header>\n\n  <!-- Main Dashboard Content -->\n  <main class=\"dashboard-main\">\n    <div class=\"dashboard-grid\">\n      <!-- Left Sidebar -->\n      <aside class=\"sidebar\">\n        <div class=\"sidebar-section\">\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"/>\n            </svg>\n            <span>Booking Queue</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n            </svg>\n            <span>Support</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\"/>\n            </svg>\n            <span>Helpdesk</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"/>\n            </svg>\n            <span>Finance</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\n            </svg>\n            <span>Passengers</span>\n          </div>\n        </div>\n\n        <div class=\"sidebar-section\">\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"/>\n            </svg>\n            <span>Commissions</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"/>\n            </svg>\n            <span>Staff Agent</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"/>\n            </svg>\n            <span>Package</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n            </svg>\n            <span>Flight Info</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"/>\n            </svg>\n            <span>Agency Profile</span>\n          </div>\n\n          <div class=\"sidebar-item\">\n            <svg class=\"sidebar-icon\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\"/>\n            </svg>\n            <span>Credit request</span>\n          </div>\n        </div>\n      </aside>\n\n      <!-- Main Content Area -->\n      <div class=\"main-content\">\n        <div class=\"content-grid\">\n          <!-- Flights Card -->\n          <div class=\"service-card flights-card\" routerLink=\"/flight-search\">\n            <div class=\"card-icon\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n              </svg>\n            </div>\n            <h3>Flights</h3>\n            <p class=\"card-description\">Search and book flights</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": ";;;AAuXA,OAAM,MAAOA,kBAAkB;EAG7BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACH,WAAW,CAACI,WAAW,EAAE;EAChD;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACL,WAAW,CAACK,MAAM,EAAE;IACzB,IAAI,CAACJ,MAAM,CAACK,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;;;uBAfWR,kBAAkB,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBd,kBAAkB;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvX/BZ,EAAA,CAAAc,cAAA,aAAiC;UAMvBd,EAAA,CAAAe,cAAA,EAA+D;UAA/Df,EAAA,CAAAc,cAAA,aAA+D;UAC7Dd,EAAA,CAAAgB,SAAA,cAAmE;UACrEhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAwB;UAAxBlB,EAAA,CAAAc,cAAA,cAAwB;UAAAd,EAAA,CAAAmB,MAAA,oBAAa;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAE9CjB,EAAA,CAAAc,cAAA,aAAuB;UACCd,EAAA,CAAAmB,MAAA,mCAA2B;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UACxDjB,EAAA,CAAAc,cAAA,gBAA2B;UAAAd,EAAA,CAAAmB,MAAA,yBAAiB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAIvDjB,EAAA,CAAAc,cAAA,eAA4B;UAExBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAiS;UACnShB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,sBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAE7BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgL;UAClLhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,4BAAwB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAEvCjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA8J;UAEhKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,4BAAoB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAEnCjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAuH;UACzHhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,yBAAiB;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAIlCjB,EAAA,CAAAc,cAAA,eAAwB;UAEpBd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAA4N;UAC9NhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,cACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAkN;UACpNhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,cACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAA+iB;UAEjjBhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,eACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,aAA6B;UAC3Bd,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAiM;UACnMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,mBACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAI;UACJjB,EAAA,CAAAkB,eAAA,EAA8C;UAA9ClB,EAAA,CAAAc,cAAA,kBAA8C;UAAnBd,EAAA,CAAAoB,UAAA,mBAAAC,qDAAA;YAAA,OAASR,GAAA,CAAAf,MAAA,EAAQ;UAAA,EAAC;UAC3CE,EAAA,CAAAe,cAAA,EAA4E;UAA5Ef,EAAA,CAAAc,cAAA,eAA4E;UAC1Ed,EAAA,CAAAgB,SAAA,gBAAqK;UACvKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAmB,MAAA,gBACF;UAAAnB,EAAA,CAAAiB,YAAA,EAAS;UAMfjB,EAAA,CAAAkB,eAAA,EAA6B;UAA7BlB,EAAA,CAAAc,cAAA,gBAA6B;UAMnBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAkK;UACpKhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,qBAAa;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG5BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAqO;UACvOhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,eAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA4O;UAC9OhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,gBAAQ;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGvBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAqN;UACvNhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,eAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgM;UAClMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,kBAAU;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAI3BjB,EAAA,CAAAc,cAAA,eAA6B;UAEzBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAAgM;UAClMhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,mBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,gBAA+I;UACjJhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAmB,MAAA,mBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,eAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,eAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAgR;UAClRhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,gBAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAGtBjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAqI;UACvIhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,oBAAW;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG1BjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAAkV;UACpVhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,uBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAG7BjB,EAAA,CAAAc,cAAA,gBAA0B;UACxBd,EAAA,CAAAe,cAAA,EAAgF;UAAhFf,EAAA,CAAAc,cAAA,gBAAgF;UAC9Ed,EAAA,CAAAgB,SAAA,iBAA0M;UAC5MhB,EAAA,CAAAiB,YAAA,EAAM;UACNjB,EAAA,CAAAkB,eAAA,EAAM;UAANlB,EAAA,CAAAc,cAAA,aAAM;UAAAd,EAAA,CAAAmB,MAAA,uBAAc;UAAAnB,EAAA,CAAAiB,YAAA,EAAO;UAMjCjB,EAAA,CAAAc,cAAA,gBAA0B;UAKlBd,EAAA,CAAAe,cAAA,EAA6C;UAA7Cf,EAAA,CAAAc,cAAA,gBAA6C;UAC3Cd,EAAA,CAAAgB,SAAA,iBAAkI;UACpIhB,EAAA,CAAAiB,YAAA,EAAM;UAERjB,EAAA,CAAAkB,eAAA,EAAI;UAAJlB,EAAA,CAAAc,cAAA,WAAI;UAAAd,EAAA,CAAAmB,MAAA,gBAAO;UAAAnB,EAAA,CAAAiB,YAAA,EAAK;UAChBjB,EAAA,CAAAc,cAAA,cAA4B;UAAAd,EAAA,CAAAmB,MAAA,gCAAuB;UAAAnB,EAAA,CAAAiB,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}