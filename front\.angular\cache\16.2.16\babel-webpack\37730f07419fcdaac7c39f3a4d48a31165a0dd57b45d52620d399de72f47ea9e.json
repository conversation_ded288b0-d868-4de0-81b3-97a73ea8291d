{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { FlightSearchType, FlightClass } from '../../models/flight-search-request.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/flight-search.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nfunction FlightSearchComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"span\", 71);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 72);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.userInfo.agency == null ? null : ctx_r0.userInfo.agency.code, \" - \", ctx_r0.userInfo.agency == null ? null : ctx_r0.userInfo.agency.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.userInfo.code, \" - \", ctx_r0.userInfo.name, \"\");\n  }\n}\nfunction FlightSearchComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Departure location is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Please enter at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Destination is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Please enter at least 3 characters \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Departure date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_76_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1, \" Return date is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"label\", 41);\n    i0.ɵɵtext(2, \"To\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 74);\n    i0.ɵɵtemplate(4, FlightSearchComponent_div_76_div_4_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_1_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"error\", ctx_r6.hasError(\"returnDate\", \"required\"));\n    i0.ɵɵproperty(\"min\", (tmp_1_0 = ctx_r6.searchForm.get(\"departureDate\")) == null ? null : tmp_1_0.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.hasError(\"returnDate\", \"required\"));\n  }\n}\nfunction FlightSearchComponent_option_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flightClass_r13 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", flightClass_r13.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flightClass_r13.label, \" \");\n  }\n}\nfunction FlightSearchComponent_div_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 77);\n    i0.ɵɵelement(2, \"path\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.errorMessage, \" \");\n  }\n}\nfunction FlightSearchComponent_span_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 78);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 79);\n    i0.ɵɵelement(2, \"path\", 80);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" SEARCH NOW \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_span_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 82);\n    i0.ɵɵelement(2, \"circle\", 83)(3, \"path\", 84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Searching... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightSearchComponent_div_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"h3\");\n    i0.ɵɵtext(3, \"Latest Searches\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"We're bringing you a new level of comfort\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 87)(7, \"div\", 88)(8, \"span\", 89);\n    i0.ɵɵtext(9, \"Coming From IST - TUN on Jun 18, 2025\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 88)(11, \"span\", 89);\n    i0.ɵɵtext(12, \"Coming From IST - TUN on Jun 18, 2025\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 88)(14, \"span\", 89);\n    i0.ɵɵtext(15, \"Coming From TUN - IST on Jun 11, 2025\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class FlightSearchComponent {\n  constructor(formBuilder, flightSearchService, authService, router) {\n    this.formBuilder = formBuilder;\n    this.flightSearchService = flightSearchService;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.searchResults = null;\n    // Énumérations pour le template\n    this.FlightSearchType = FlightSearchType;\n    this.FlightClass = FlightClass;\n    // Options pour les sélecteurs\n    this.flightClasses = [{\n      value: FlightClass.ECONOMY,\n      label: 'Economy'\n    }, {\n      value: FlightClass.PREMIUM_ECONOMY,\n      label: 'Premium Economy'\n    }, {\n      value: FlightClass.BUSINESS,\n      label: 'Business'\n    }, {\n      value: FlightClass.FIRST,\n      label: 'First Class'\n    }];\n  }\n  ngOnInit() {\n    this.initializeForm();\n    this.loadUserInfo();\n    // Vérifier l'authentification\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/login']);\n    }\n  }\n  /**\n   * Initialise le formulaire de recherche\n   */\n  initializeForm() {\n    this.searchForm = this.formBuilder.group({\n      searchType: [FlightSearchType.ONE_WAY, Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      children: [0, [Validators.min(0), Validators.max(9)]],\n      infants: [0, [Validators.min(0), Validators.max(9)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n    // Surveiller les changements du type de recherche\n    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {\n      this.onSearchTypeChange(value);\n    });\n  }\n  /**\n   * Charge les informations utilisateur\n   */\n  loadUserInfo() {\n    this.userInfo = this.authService.getUserInfo();\n  }\n  /**\n   * Gère le changement de type de recherche\n   */\n  onSearchTypeChange(searchType) {\n    const returnDateControl = this.searchForm.get('returnDate');\n    if (searchType === FlightSearchType.ROUND_TRIP) {\n      returnDateControl?.setValidators([Validators.required]);\n    } else {\n      returnDateControl?.clearValidators();\n      returnDateControl?.setValue('');\n    }\n    returnDateControl?.updateValueAndValidity();\n  }\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports() {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n  /**\n   * Incrémente le nombre de passagers\n   */\n  incrementPassenger(type) {\n    const control = this.searchForm.get(type);\n    const currentValue = control?.value || 0;\n    const maxValue = type === 'infants' ? this.searchForm.get('adults')?.value : 9;\n    if (currentValue < maxValue) {\n      control?.setValue(currentValue + 1);\n    }\n  }\n  /**\n   * Décrémente le nombre de passagers\n   */\n  decrementPassenger(type) {\n    const control = this.searchForm.get(type);\n    const currentValue = control?.value || 0;\n    const minValue = type === 'adults' ? 1 : 0;\n    if (currentValue > minValue) {\n      control?.setValue(currentValue - 1);\n    }\n  }\n  /**\n   * Calcule le nombre total de passagers\n   */\n  getTotalPassengers() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n  /**\n   * Obtient le texte descriptif des passagers\n   */\n  getPassengerText() {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    return text;\n  }\n  /**\n   * Vérifie si un champ a une erreur spécifique\n   */\n  hasError(fieldName, errorType) {\n    const field = this.searchForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && (field.dirty || field.touched));\n  }\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  markFormGroupTouched() {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit() {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.searchResults = null;\n      const formData = {\n        searchType: this.searchForm.value.searchType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate || undefined,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline || undefined,\n        culture: 'fr-FR',\n        currency: 'EUR' // Peut être configuré selon les préférences utilisateur\n      };\n\n      this.flightSearchService.searchFlights(formData).subscribe({\n        next: response => {\n          this.isLoading = false;\n          if (response.header.success) {\n            this.searchResults = response.body;\n            console.log('Résultats de recherche:', this.searchResults);\n            // Ici vous pouvez naviguer vers une page de résultats ou afficher les résultats\n          } else {\n            this.handleApiError(response);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n          console.error('Erreur de recherche:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  handleApiError(response) {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères de recherche';\n    }\n  }\n  /**\n   * Réinitialise le formulaire\n   */\n  resetForm() {\n    this.searchForm.reset();\n    this.initializeForm();\n    this.errorMessage = '';\n    this.searchResults = null;\n  }\n  /**\n   * Obtient la date d'aujourd'hui au format YYYY-MM-DD\n   */\n  getTodayDate() {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  }\n  static {\n    this.ɵfac = function FlightSearchComponent_Factory(t) {\n      return new (t || FlightSearchComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.FlightSearchService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightSearchComponent,\n      selectors: [[\"app-flight-search\"]],\n      decls: 132,\n      vars: 42,\n      consts: [[1, \"flight-search-container\"], [1, \"search-header\"], [1, \"header-content\"], [1, \"logo-section\"], [1, \"logo\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"logo-icon\"], [\"d\", \"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"], [1, \"logo-text\"], [\"class\", \"agency-info\", 4, \"ngIf\"], [1, \"header-actions\"], [\"routerLink\", \"/dashboard\", 1, \"header-btn\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"d\", \"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"], [1, \"header-btn\"], [\"d\", \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"], [1, \"header-btn\", \"logout-btn\", 3, \"click\"], [\"d\", \"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\"], [1, \"search-main\"], [1, \"search-content\"], [1, \"search-section\"], [1, \"search-card\"], [1, \"search-header-title\"], [1, \"title-icon\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"], [1, \"title-content\"], [\"novalidate\", \"\", 1, \"search-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"trip-type-tabs\"], [1, \"trip-tab\"], [\"type\", \"radio\", \"formControlName\", \"searchType\", 3, \"value\"], [1, \"destination-row\"], [1, \"destination-group\"], [1, \"destination-label\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"label-icon\"], [\"d\", \"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"], [\"type\", \"text\", \"formControlName\", \"departureLocation\", \"placeholder\", \"Enter departure location\", \"autocomplete\", \"off\", 1, \"destination-input\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Swap airports\", 1, \"swap-button\", 3, \"click\"], [\"d\", \"M7.5 21L3 16.5l4.5-4.5L9 13.5 6 16.5h7.5v-9L12 6l1.5 1.5v9H21l-3-3L19.5 12 24 16.5 19.5 21 18 19.5l3-3h-7.5v9L12 24l-1.5-1.5v-9H3l3 3L4.5 18z\"], [\"type\", \"text\", \"formControlName\", \"arrivalLocation\", \"placeholder\", \"Enter destination\", \"autocomplete\", \"off\", 1, \"destination-input\"], [1, \"date-passenger-row\"], [1, \"date-group\"], [1, \"date-label\"], [\"type\", \"date\", \"formControlName\", \"departureDate\", 1, \"date-input\", 3, \"min\"], [\"class\", \"date-group\", 4, \"ngIf\"], [1, \"passenger-class-group\"], [1, \"option-label\"], [1, \"passenger-class-container\"], [1, \"passengers-selector\"], [1, \"passenger-type\"], [1, \"passenger-icon\"], [1, \"passenger-count\"], [1, \"passenger-controls\"], [\"type\", \"button\", 1, \"passenger-btn\", \"minus\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"passenger-btn\", \"plus\", 3, \"disabled\", \"click\"], [1, \"passenger-summary\"], [1, \"flight-class-group\"], [\"formControlName\", \"flightClass\", 1, \"option-select\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"additional-options\"], [1, \"checkbox-option\"], [\"type\", \"checkbox\", \"formControlName\", \"directFlightsOnly\"], [1, \"checkbox-custom\"], [1, \"checkbox-label\"], [1, \"airline-preference\"], [\"type\", \"text\", \"formControlName\", \"preferredAirline\", \"placeholder\", \"Preferred Airline (Optional)\", 1, \"airline-input\"], [\"class\", \"error-alert\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"button-content\", 4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [\"class\", \"latest-searches\", 4, \"ngIf\"], [1, \"agency-info\"], [1, \"agency-code\"], [1, \"user-info\"], [1, \"error-message\"], [\"type\", \"date\", \"formControlName\", \"returnDate\", 1, \"date-input\", 3, \"min\"], [3, \"value\"], [1, \"error-alert\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"error-icon\"], [1, \"button-content\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 1, \"button-icon\"], [\"d\", \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"], [1, \"loading-content\"], [\"viewBox\", \"0 0 24 24\", 1, \"loading-spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"4\", \"fill\", \"none\", \"opacity\", \"0.25\"], [\"fill\", \"currentColor\", \"d\", \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\", \"opacity\", \"0.75\"], [1, \"latest-searches\"], [1, \"searches-card\"], [1, \"search-history\"], [1, \"search-item\"], [1, \"search-route\"]],\n      template: function FlightSearchComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(5, \"svg\", 5);\n          i0.ɵɵelement(6, \"path\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(7, \"span\", 7);\n          i0.ɵɵtext(8, \"BLOCK TO BOOK\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, FlightSearchComponent_div_9_Template, 5, 4, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"button\", 10);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(12, \"svg\", 11);\n          i0.ɵɵelement(13, \"path\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" Home \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(15, \"button\", 13);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(16, \"svg\", 11);\n          i0.ɵɵelement(17, \"path\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(18, \" Tools \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(19, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_19_listener() {\n            ctx.authService.logout();\n            return ctx.router.navigate([\"/login\"]);\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(20, \"svg\", 11);\n          i0.ɵɵelement(21, \"path\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(22, \" Logout \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(23, \"main\", 17)(24, \"div\", 18)(25, \"div\", 19)(26, \"div\", 20)(27, \"div\", 21)(28, \"div\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 11);\n          i0.ɵɵelement(30, \"path\", 23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(31, \"div\", 24)(32, \"h1\");\n          i0.ɵɵtext(33, \"Search and Book Flights\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\");\n          i0.ɵɵtext(35, \"We're bringing you a new level of comfort\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(36, \"form\", 25);\n          i0.ɵɵlistener(\"ngSubmit\", function FlightSearchComponent_Template_form_ngSubmit_36_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(37, \"div\", 26)(38, \"label\", 27);\n          i0.ɵɵelement(39, \"input\", 28);\n          i0.ɵɵelementStart(40, \"span\");\n          i0.ɵɵtext(41, \"One way\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"label\", 27);\n          i0.ɵɵelement(43, \"input\", 28);\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"Round Trip\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"label\", 27);\n          i0.ɵɵelement(47, \"input\", 28);\n          i0.ɵɵelementStart(48, \"span\");\n          i0.ɵɵtext(49, \"Multi-City/Stop-Overs\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 29)(51, \"div\", 30)(52, \"label\", 31);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(53, \"svg\", 32);\n          i0.ɵɵelement(54, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(55, \" Leaving From (City, Country Or Specific Airport) \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(56, \"input\", 34);\n          i0.ɵɵtemplate(57, FlightSearchComponent_div_57_Template, 2, 0, \"div\", 35);\n          i0.ɵɵtemplate(58, FlightSearchComponent_div_58_Template, 2, 0, \"div\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_59_listener() {\n            return ctx.swapAirports();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(60, \"svg\", 11);\n          i0.ɵɵelement(61, \"path\", 37);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(62, \"div\", 30)(63, \"label\", 31);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(64, \"svg\", 32);\n          i0.ɵɵelement(65, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(66, \" Going To (City, Country Or Specific Airport) \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(67, \"input\", 38);\n          i0.ɵɵtemplate(68, FlightSearchComponent_div_68_Template, 2, 0, \"div\", 35);\n          i0.ɵɵtemplate(69, FlightSearchComponent_div_69_Template, 2, 0, \"div\", 35);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"div\", 39)(71, \"div\", 40)(72, \"label\", 41);\n          i0.ɵɵtext(73, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(74, \"input\", 42);\n          i0.ɵɵtemplate(75, FlightSearchComponent_div_75_Template, 2, 0, \"div\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(76, FlightSearchComponent_div_76_Template, 5, 4, \"div\", 43);\n          i0.ɵɵelementStart(77, \"div\", 44)(78, \"label\", 45);\n          i0.ɵɵtext(79, \"Passenger & Class of travel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 46)(81, \"div\", 47)(82, \"div\", 48)(83, \"span\", 49);\n          i0.ɵɵtext(84, \"\\uD83D\\uDC64\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"span\", 50);\n          i0.ɵɵtext(86);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"div\", 51)(88, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_88_listener() {\n            return ctx.decrementPassenger(\"adults\");\n          });\n          i0.ɵɵtext(89, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_90_listener() {\n            return ctx.incrementPassenger(\"adults\");\n          });\n          i0.ɵɵtext(91, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(92, \"div\", 48)(93, \"span\", 49);\n          i0.ɵɵtext(94, \"\\uD83D\\uDC76\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"span\", 50);\n          i0.ɵɵtext(96);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"div\", 51)(98, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_98_listener() {\n            return ctx.decrementPassenger(\"children\");\n          });\n          i0.ɵɵtext(99, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_100_listener() {\n            return ctx.incrementPassenger(\"children\");\n          });\n          i0.ɵɵtext(101, \"+\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(102, \"div\", 48)(103, \"span\", 49);\n          i0.ɵɵtext(104, \"\\uD83C\\uDF7C\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"span\", 50);\n          i0.ɵɵtext(106);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"div\", 51)(108, \"button\", 52);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_108_listener() {\n            return ctx.decrementPassenger(\"infants\");\n          });\n          i0.ɵɵtext(109, \"-\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(110, \"button\", 53);\n          i0.ɵɵlistener(\"click\", function FlightSearchComponent_Template_button_click_110_listener() {\n            return ctx.incrementPassenger(\"infants\");\n          });\n          i0.ɵɵtext(111, \"+\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(112, \"div\", 54);\n          i0.ɵɵtext(113);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 55)(115, \"label\", 45);\n          i0.ɵɵtext(116, \"Preferred Airline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"select\", 56);\n          i0.ɵɵtemplate(118, FlightSearchComponent_option_118_Template, 2, 2, \"option\", 57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(119, \"div\", 58)(120, \"label\", 59);\n          i0.ɵɵelement(121, \"input\", 60)(122, \"span\", 61);\n          i0.ɵɵelementStart(123, \"span\", 62);\n          i0.ɵɵtext(124, \"Direct flights only\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(125, \"div\", 63);\n          i0.ɵɵelement(126, \"input\", 64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(127, FlightSearchComponent_div_127_Template, 4, 1, \"div\", 65);\n          i0.ɵɵelementStart(128, \"button\", 66);\n          i0.ɵɵtemplate(129, FlightSearchComponent_span_129_Template, 4, 0, \"span\", 67);\n          i0.ɵɵtemplate(130, FlightSearchComponent_span_130_Template, 5, 0, \"span\", 68);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(131, FlightSearchComponent_div_131_Template, 16, 0, \"div\", 69);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_4_0;\n          let tmp_6_0;\n          let tmp_17_0;\n          let tmp_18_0;\n          let tmp_19_0;\n          let tmp_20_0;\n          let tmp_21_0;\n          let tmp_22_0;\n          let tmp_23_0;\n          let tmp_24_0;\n          let tmp_25_0;\n          let tmp_26_0;\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.userInfo);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ((tmp_2_0 = ctx.searchForm.get(\"searchType\")) == null ? null : tmp_2_0.value) === ctx.FlightSearchType.ONE_WAY);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.FlightSearchType.ONE_WAY);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ((tmp_4_0 = ctx.searchForm.get(\"searchType\")) == null ? null : tmp_4_0.value) === ctx.FlightSearchType.ROUND_TRIP);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.FlightSearchType.ROUND_TRIP);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ((tmp_6_0 = ctx.searchForm.get(\"searchType\")) == null ? null : tmp_6_0.value) === ctx.FlightSearchType.MULTI_CITY);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"value\", ctx.FlightSearchType.MULTI_CITY);\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureLocation\", \"required\") || ctx.hasError(\"departureLocation\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"departureLocation\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"departureLocation\", \"minlength\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"arrivalLocation\", \"required\") || ctx.hasError(\"arrivalLocation\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"arrivalLocation\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"arrivalLocation\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"departureDate\", \"required\"));\n          i0.ɵɵproperty(\"min\", ctx.getTodayDate());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"departureDate\", \"required\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.searchForm.get(\"searchType\")) == null ? null : tmp_17_0.value) === ctx.FlightSearchType.ROUND_TRIP);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate((tmp_18_0 = ctx.searchForm.get(\"adults\")) == null ? null : tmp_18_0.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_19_0 = ctx.searchForm.get(\"adults\")) == null ? null : tmp_19_0.value) <= 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_20_0 = ctx.searchForm.get(\"adults\")) == null ? null : tmp_20_0.value) >= 9);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((tmp_21_0 = ctx.searchForm.get(\"children\")) == null ? null : tmp_21_0.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_22_0 = ctx.searchForm.get(\"children\")) == null ? null : tmp_22_0.value) <= 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_23_0 = ctx.searchForm.get(\"children\")) == null ? null : tmp_23_0.value) >= 9);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate((tmp_24_0 = ctx.searchForm.get(\"infants\")) == null ? null : tmp_24_0.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_25_0 = ctx.searchForm.get(\"infants\")) == null ? null : tmp_25_0.value) <= 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ((tmp_26_0 = ctx.searchForm.get(\"infants\")) == null ? null : tmp_26_0.value) >= ((tmp_26_0 = ctx.searchForm.get(\"adults\")) == null ? null : tmp_26_0.value));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getPassengerText());\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.flightClasses);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.searchForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.searchResults);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i4.RouterLink, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.RadioControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"\\n\\n[_ngcontent-%COMP%]:root {\\n  --primary-blue: #4a90e2;\\n  --secondary-blue: #7bb3f0;\\n  --light-blue: #e8f4fd;\\n  --dark-blue: #2c5aa0;\\n  --success-green: #4caf50;\\n  --error-red: #f44336;\\n  --warning-orange: #ff9800;\\n  --text-dark: #333333;\\n  --text-light: #666666;\\n  --text-muted: #999999;\\n  --background-light: #f8f9fa;\\n  --white: #ffffff;\\n  --border-color: #e0e0e0;\\n  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);\\n  --border-radius: 8px;\\n  --transition: all 0.3s ease;\\n}\\n\\n\\n\\n.flight-search-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\\n}\\n\\n\\n\\n.search-header[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  border-bottom: 1px solid var(--border-color);\\n  padding: 1rem 0;\\n  box-shadow: var(--shadow-light);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.logo-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2rem;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.logo-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  color: var(--primary-blue);\\n}\\n\\n.logo-text[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: var(--text-dark);\\n  letter-spacing: -0.5px;\\n}\\n\\n.agency-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.agency-code[_ngcontent-%COMP%], .user-info[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-light);\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  align-items: center;\\n}\\n\\n.header-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.5rem 1rem;\\n  background: transparent;\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  color: var(--text-dark);\\n  font-size: 0.875rem;\\n  cursor: pointer;\\n  transition: var(--transition);\\n}\\n\\n.header-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--light-blue);\\n  border-color: var(--primary-blue);\\n  color: var(--primary-blue);\\n}\\n\\n.header-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.logout-btn[_ngcontent-%COMP%]:hover {\\n  background: #ffebee;\\n  border-color: var(--error-red);\\n  color: var(--error-red);\\n}\\n\\n\\n\\n.search-main[_ngcontent-%COMP%] {\\n  padding: 3rem 0;\\n}\\n\\n.search-content[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 3rem;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 16px;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n\\n.search-card[_ngcontent-%COMP%] {\\n  padding: 2.5rem;\\n}\\n\\n.search-header-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 2.5rem;\\n  padding-bottom: 1.5rem;\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.title-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.title-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  color: var(--white);\\n}\\n\\n.title-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  color: var(--white);\\n  margin: 0 0 0.25rem 0;\\n}\\n\\n.title-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: rgba(255, 255, 255, 0.8);\\n  margin: 0;\\n}\\n\\n\\n\\n.search-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n\\n\\n.trip-type-tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  padding: 0.5rem;\\n  gap: 0.5rem;\\n  margin-bottom: 2rem;\\n}\\n\\n.trip-tab[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n  padding: 1rem 1.5rem;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: rgba(255, 255, 255, 0.7);\\n  border: 1px solid transparent;\\n}\\n\\n.trip-tab[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.trip-tab.active[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: var(--white);\\n  border-color: rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n\\n\\n.destination-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr auto 1fr;\\n  gap: 1rem;\\n  align-items: end;\\n}\\n\\n.destination-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.destination-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.label-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n  color: rgba(255, 255, 255, 0.8);\\n}\\n\\n.destination-input[_ngcontent-%COMP%] {\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  transition: var(--transition);\\n  background: rgba(255, 255, 255, 0.1);\\n  color: var(--white);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.destination-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.destination-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);\\n}\\n\\n.destination-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-red);\\n}\\n\\n.swap-button[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 2px solid var(--border-color);\\n  border-radius: 50%;\\n  background: var(--white);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.swap-button[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-blue);\\n  background: var(--light-blue);\\n}\\n\\n.swap-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: var(--primary-blue);\\n}\\n\\n\\n\\n.date-passenger-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: auto auto 1fr auto;\\n  gap: 1.5rem;\\n  align-items: start;\\n}\\n\\n.date-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.date-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: rgba(255, 255, 255, 0.9);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.date-input[_ngcontent-%COMP%] {\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  transition: var(--transition);\\n  background: rgba(255, 255, 255, 0.1);\\n  color: var(--white);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  min-width: 160px;\\n}\\n\\n.date-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);\\n}\\n\\n.date-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-red);\\n}\\n\\n\\n\\n.passenger-class-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-class-container[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  padding: 1rem;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.passengers-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1.5rem;\\n  margin-bottom: 0.75rem;\\n  justify-content: center;\\n}\\n\\n.passenger-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n\\n.passenger-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--white);\\n  font-size: 1.125rem;\\n  min-width: 24px;\\n  text-align: center;\\n}\\n\\n.passenger-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 8px;\\n  background: rgba(255, 255, 255, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: var(--white);\\n  transition: var(--transition);\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: rgba(255, 255, 255, 0.2);\\n  border-color: rgba(255, 255, 255, 0.5);\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n}\\n\\n.passenger-btn.plus[_ngcontent-%COMP%] {\\n  background: rgba(74, 144, 226, 0.3);\\n  border-color: rgba(74, 144, 226, 0.5);\\n}\\n\\n.passenger-btn.minus[_ngcontent-%COMP%] {\\n  background: rgba(244, 67, 54, 0.3);\\n  border-color: rgba(244, 67, 54, 0.5);\\n}\\n\\n.passenger-summary[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: rgba(255, 255, 255, 0.7);\\n  text-align: center;\\n  font-style: italic;\\n}\\n\\n\\n\\n.flight-class-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  min-width: 180px;\\n}\\n\\n\\n\\n.options-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr;\\n  gap: 1rem;\\n}\\n\\n.option-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.option-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n.passengers-group[_ngcontent-%COMP%] {\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--border-radius);\\n  padding: 1rem;\\n  background: var(--white);\\n}\\n\\n.passengers-selector[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.passenger-type[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.passenger-icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n}\\n\\n.passenger-count[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--text-dark);\\n  min-width: 20px;\\n  text-align: center;\\n}\\n\\n.passenger-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 1px solid var(--border-color);\\n  border-radius: 4px;\\n  background: var(--white);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  cursor: pointer;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  transition: var(--transition);\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: var(--primary-blue);\\n  color: var(--white);\\n  border-color: var(--primary-blue);\\n}\\n\\n.passenger-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.passenger-summary[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--text-light);\\n  font-style: italic;\\n}\\n\\n.option-select[_ngcontent-%COMP%] {\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 12px;\\n  font-size: 1rem;\\n  background: rgba(255, 255, 255, 0.1);\\n  color: var(--white);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: var(--transition);\\n}\\n\\n.option-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n  background: rgba(255, 255, 255, 0.15);\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);\\n}\\n\\n.option-select[_ngcontent-%COMP%]   option[_ngcontent-%COMP%] {\\n  background: #333;\\n  color: var(--white);\\n}\\n\\n\\n\\n.additional-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n  background: rgba(255, 255, 255, 0.05);\\n  border-radius: 12px;\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.checkbox-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  cursor: pointer;\\n}\\n\\n.checkbox-option[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.checkbox-custom[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  border-radius: 4px;\\n  position: relative;\\n  transition: var(--transition);\\n  background: rgba(255, 255, 255, 0.1);\\n}\\n\\n.checkbox-option[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .checkbox-custom[_ngcontent-%COMP%] {\\n  background: rgba(74, 144, 226, 0.8);\\n  border-color: rgba(74, 144, 226, 1);\\n}\\n\\n.checkbox-option[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:checked    + .checkbox-custom[_ngcontent-%COMP%]::after {\\n  content: '\\u2713';\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  color: var(--white);\\n  font-size: 12px;\\n  font-weight: bold;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n.airline-input[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  border-radius: 8px;\\n  font-size: 0.875rem;\\n  background: rgba(255, 255, 255, 0.1);\\n  color: var(--white);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  transition: var(--transition);\\n  min-width: 200px;\\n}\\n\\n.airline-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.6);\\n}\\n\\n.airline-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: rgba(255, 255, 255, 0.4);\\n  background: rgba(255, 255, 255, 0.15);\\n}\\n\\n\\n\\n.error-message[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--error-red);\\n  margin-top: 0.25rem;\\n}\\n\\n.error-alert[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  background: #ffebee;\\n  border: 1px solid #ffcdd2;\\n  border-radius: var(--border-radius);\\n  color: var(--error-red);\\n  font-size: 0.875rem;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.search-button[_ngcontent-%COMP%] {\\n  padding: 1rem 2rem;\\n  background: linear-gradient(135deg, var(--success-green), #66bb6a);\\n  border: none;\\n  border-radius: var(--border-radius);\\n  color: var(--white);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  min-height: 48px;\\n}\\n\\n.search-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #43a047, var(--success-green));\\n  transform: translateY(-1px);\\n  box-shadow: var(--shadow-medium);\\n}\\n\\n.search-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n.button-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n\\n\\n.latest-searches[_ngcontent-%COMP%] {\\n  background: var(--white);\\n  border-radius: 12px;\\n  box-shadow: var(--shadow-medium);\\n  overflow: hidden;\\n}\\n\\n.searches-card[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n\\n.searches-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--text-dark);\\n  margin: 0 0 0.5rem 0;\\n}\\n\\n.searches-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-light);\\n  margin: 0 0 1.5rem 0;\\n}\\n\\n.search-history[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n\\n.search-item[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  background: var(--background-light);\\n  border-radius: var(--border-radius);\\n  border-left: 4px solid var(--primary-blue);\\n}\\n\\n.search-route[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-dark);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .search-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    padding: 0 1rem;\\n  }\\n  \\n  .header-content[_ngcontent-%COMP%] {\\n    padding: 0 1rem;\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .destination-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  \\n  .swap-button[_ngcontent-%COMP%] {\\n    order: 3;\\n    margin: 0;\\n    align-self: center;\\n  }\\n  \\n  .date-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .options-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .additional-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n    gap: 1rem;\\n  }\\n  \\n  .passengers-selector[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n  \\n  .passenger-type[_ngcontent-%COMP%] {\\n    justify-content: space-between;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "FlightSearchType", "FlightClass", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "userInfo", "agency", "code", "name", "ɵɵelement", "ɵɵtemplate", "FlightSearchComponent_div_76_div_4_Template", "ɵɵclassProp", "ctx_r6", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵproperty", "tmp_1_0", "searchForm", "get", "value", "flightClass_r13", "ɵɵtextInterpolate1", "label", "ɵɵnamespaceSVG", "ctx_r8", "errorMessage", "FlightSearchComponent", "constructor", "formBuilder", "flightSearchService", "authService", "router", "isLoading", "searchResults", "flightClasses", "ECONOMY", "PREMIUM_ECONOMY", "BUSINESS", "FIRST", "ngOnInit", "initializeForm", "loadUserInfo", "isAuthenticated", "navigate", "group", "searchType", "ONE_WAY", "required", "departureLocation", "<PERSON><PERSON><PERSON><PERSON>", "arrivalLocation", "departureDate", "returnDate", "adults", "min", "max", "children", "infants", "flightClass", "directFlightsOnly", "preferredAirline", "valueChanges", "subscribe", "onSearchTypeChange", "getUserInfo", "returnDateControl", "ROUND_TRIP", "setValidators", "clearValidators", "setValue", "updateValueAndValidity", "swapAirports", "departure", "arrival", "patchValue", "increment<PERSON><PERSON><PERSON><PERSON>", "type", "control", "currentValue", "maxValue", "decre<PERSON><PERSON><PERSON><PERSON><PERSON>", "minValue", "getTotalPassengers", "getPassengerText", "text", "fieldName", "errorType", "field", "dirty", "touched", "markFormGroupTouched", "Object", "keys", "controls", "for<PERSON>ach", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "onSubmit", "valid", "formData", "trim", "undefined", "passengers", "culture", "currency", "searchFlights", "next", "response", "header", "success", "body", "console", "log", "handleApiError", "error", "message", "messages", "length", "resetForm", "reset", "getTodayDate", "today", "Date", "toISOString", "split", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "FlightSearchService", "i3", "AuthService", "i4", "Router", "selectors", "decls", "vars", "consts", "template", "FlightSearchComponent_Template", "rf", "ctx", "ɵɵnamespaceHTML", "FlightSearchComponent_div_9_Template", "ɵɵlistener", "FlightSearchComponent_Template_button_click_19_listener", "logout", "FlightSearchComponent_Template_form_ngSubmit_36_listener", "FlightSearchComponent_div_57_Template", "FlightSearchComponent_div_58_Template", "FlightSearchComponent_Template_button_click_59_listener", "FlightSearchComponent_div_68_Template", "FlightSearchComponent_div_69_Template", "FlightSearchComponent_div_75_Template", "FlightSearchComponent_div_76_Template", "FlightSearchComponent_Template_button_click_88_listener", "FlightSearchComponent_Template_button_click_90_listener", "FlightSearchComponent_Template_button_click_98_listener", "FlightSearchComponent_Template_button_click_100_listener", "FlightSearchComponent_Template_button_click_108_listener", "FlightSearchComponent_Template_button_click_110_listener", "FlightSearchComponent_option_118_Template", "FlightSearchComponent_div_127_Template", "FlightSearchComponent_span_129_Template", "FlightSearchComponent_span_130_Template", "FlightSearchComponent_div_131_Template", "tmp_2_0", "tmp_4_0", "tmp_6_0", "MULTI_CITY", "tmp_17_0", "ɵɵtextInterpolate", "tmp_18_0", "tmp_19_0", "tmp_20_0", "tmp_21_0", "tmp_22_0", "tmp_23_0", "tmp_24_0", "tmp_25_0", "tmp_26_0", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-search\\flight-search.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-search\\flight-search.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\n\nimport { FlightSearchService } from '../../services/flight-search.service';\nimport { AuthService } from '../../services/auth.service';\nimport { \n  FlightSearchForm, \n  FlightSearchType, \n  FlightClass,\n  FlightSearchResponse \n} from '../../models/flight-search-request.interface';\n\n@Component({\n  selector: 'app-flight-search',\n  templateUrl: './flight-search.component.html',\n  styleUrls: ['./flight-search.component.css']\n})\nexport class FlightSearchComponent implements OnInit {\n  searchForm!: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  searchResults: any = null;\n  \n  // Énumérations pour le template\n  FlightSearchType = FlightSearchType;\n  FlightClass = FlightClass;\n  \n  // Options pour les sélecteurs\n  flightClasses = [\n    { value: FlightClass.ECONOMY, label: 'Economy' },\n    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },\n    { value: FlightClass.BUSINESS, label: 'Business' },\n    { value: FlightClass.FIRST, label: 'First Class' }\n  ];\n\n  // Données utilisateur\n  userInfo: any;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private flightSearchService: FlightSearchService,\n    public authService: AuthService,\n    public router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    this.loadUserInfo();\n    \n    // Vérifier l'authentification\n    if (!this.authService.isAuthenticated()) {\n      this.router.navigate(['/login']);\n    }\n  }\n\n  /**\n   * Initialise le formulaire de recherche\n   */\n  private initializeForm(): void {\n    this.searchForm = this.formBuilder.group({\n      searchType: [FlightSearchType.ONE_WAY, Validators.required],\n      departureLocation: ['', [Validators.required, Validators.minLength(3)]],\n      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],\n      departureDate: ['', Validators.required],\n      returnDate: [''],\n      adults: [1, [Validators.required, Validators.min(1), Validators.max(9)]],\n      children: [0, [Validators.min(0), Validators.max(9)]],\n      infants: [0, [Validators.min(0), Validators.max(9)]],\n      flightClass: [FlightClass.ECONOMY, Validators.required],\n      directFlightsOnly: [false],\n      preferredAirline: ['']\n    });\n\n    // Surveiller les changements du type de recherche\n    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {\n      this.onSearchTypeChange(value);\n    });\n  }\n\n  /**\n   * Charge les informations utilisateur\n   */\n  private loadUserInfo(): void {\n    this.userInfo = this.authService.getUserInfo();\n  }\n\n  /**\n   * Gère le changement de type de recherche\n   */\n  onSearchTypeChange(searchType: FlightSearchType): void {\n    const returnDateControl = this.searchForm.get('returnDate');\n    \n    if (searchType === FlightSearchType.ROUND_TRIP) {\n      returnDateControl?.setValidators([Validators.required]);\n    } else {\n      returnDateControl?.clearValidators();\n      returnDateControl?.setValue('');\n    }\n    \n    returnDateControl?.updateValueAndValidity();\n  }\n\n  /**\n   * Échange les aéroports de départ et d'arrivée\n   */\n  swapAirports(): void {\n    const departure = this.searchForm.get('departureLocation')?.value;\n    const arrival = this.searchForm.get('arrivalLocation')?.value;\n    \n    this.searchForm.patchValue({\n      departureLocation: arrival,\n      arrivalLocation: departure\n    });\n  }\n\n  /**\n   * Incrémente le nombre de passagers\n   */\n  incrementPassenger(type: string): void {\n    const control = this.searchForm.get(type);\n    const currentValue = control?.value || 0;\n    const maxValue = type === 'infants' ? this.searchForm.get('adults')?.value : 9;\n    \n    if (currentValue < maxValue) {\n      control?.setValue(currentValue + 1);\n    }\n  }\n\n  /**\n   * Décrémente le nombre de passagers\n   */\n  decrementPassenger(type: string): void {\n    const control = this.searchForm.get(type);\n    const currentValue = control?.value || 0;\n    const minValue = type === 'adults' ? 1 : 0;\n    \n    if (currentValue > minValue) {\n      control?.setValue(currentValue - 1);\n    }\n  }\n\n  /**\n   * Calcule le nombre total de passagers\n   */\n  getTotalPassengers(): number {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    return adults + children + infants;\n  }\n\n  /**\n   * Obtient le texte descriptif des passagers\n   */\n  getPassengerText(): string {\n    const adults = this.searchForm.get('adults')?.value || 0;\n    const children = this.searchForm.get('children')?.value || 0;\n    const infants = this.searchForm.get('infants')?.value || 0;\n    \n    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;\n    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;\n    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;\n    \n    return text;\n  }\n\n  /**\n   * Vérifie si un champ a une erreur spécifique\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.searchForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && (field.dirty || field.touched));\n  }\n\n  /**\n   * Marque tous les champs du formulaire comme touchés\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.searchForm.controls).forEach(key => {\n      const control = this.searchForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Soumet le formulaire de recherche\n   */\n  onSubmit(): void {\n    if (this.searchForm.valid && !this.isLoading) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      this.searchResults = null;\n\n      const formData: FlightSearchForm = {\n        searchType: this.searchForm.value.searchType,\n        departureLocation: this.searchForm.value.departureLocation.trim(),\n        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),\n        departureDate: this.searchForm.value.departureDate,\n        returnDate: this.searchForm.value.returnDate || undefined,\n        passengers: {\n          adults: this.searchForm.value.adults,\n          children: this.searchForm.value.children,\n          infants: this.searchForm.value.infants\n        },\n        flightClass: this.searchForm.value.flightClass,\n        directFlightsOnly: this.searchForm.value.directFlightsOnly,\n        preferredAirline: this.searchForm.value.preferredAirline || undefined,\n        culture: 'fr-FR', // Peut être configuré selon les préférences utilisateur\n        currency: 'EUR'   // Peut être configuré selon les préférences utilisateur\n      };\n\n      this.flightSearchService.searchFlights(formData).subscribe({\n        next: (response: FlightSearchResponse) => {\n          this.isLoading = false;\n          \n          if (response.header.success) {\n            this.searchResults = response.body;\n            console.log('Résultats de recherche:', this.searchResults);\n            // Ici vous pouvez naviguer vers une page de résultats ou afficher les résultats\n          } else {\n            this.handleApiError(response);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';\n          console.error('Erreur de recherche:', error);\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Gère les erreurs retournées par l'API\n   */\n  private handleApiError(response: FlightSearchResponse): void {\n    if (response.header.messages && response.header.messages.length > 0) {\n      this.errorMessage = response.header.messages[0].message;\n    } else {\n      this.errorMessage = 'Aucun vol trouvé pour ces critères de recherche';\n    }\n  }\n\n  /**\n   * Réinitialise le formulaire\n   */\n  resetForm(): void {\n    this.searchForm.reset();\n    this.initializeForm();\n    this.errorMessage = '';\n    this.searchResults = null;\n  }\n\n  /**\n   * Obtient la date d'aujourd'hui au format YYYY-MM-DD\n   */\n  getTodayDate(): string {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  }\n}\n", "<div class=\"flight-search-container\">\n  <!-- Header avec informations de l'agence -->\n  <header class=\"search-header\">\n    <div class=\"header-content\">\n      <div class=\"logo-section\">\n        <div class=\"logo\">\n          <svg class=\"logo-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n          </svg>\n          <span class=\"logo-text\">BLOCK TO BOOK</span>\n        </div>\n        <div class=\"agency-info\" *ngIf=\"userInfo\">\n          <span class=\"agency-code\">{{ userInfo.agency?.code }} - {{ userInfo.agency?.name }}</span>\n          <span class=\"user-info\">{{ userInfo.code }} - {{ userInfo.name }}</span>\n        </div>\n      </div>\n      \n      <div class=\"header-actions\">\n        <button class=\"header-btn\" routerLink=\"/dashboard\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\"/>\n          </svg>\n          Home\n        </button>\n        <button class=\"header-btn\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n          </svg>\n          Tools\n        </button>\n        <button class=\"header-btn logout-btn\" (click)=\"authService.logout(); router.navigate(['/login'])\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z\"/>\n          </svg>\n          Logout\n        </button>\n      </div>\n    </div>\n  </header>\n\n  <!-- Zone principale de recherche -->\n  <main class=\"search-main\">\n    <div class=\"search-content\">\n      <!-- Section de recherche -->\n      <div class=\"search-section\">\n        <div class=\"search-card\">\n          <div class=\"search-header-title\">\n            <div class=\"title-icon\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n              </svg>\n            </div>\n            <div class=\"title-content\">\n              <h1>Search and Book Flights</h1>\n              <p>We're bringing you a new level of comfort</p>\n            </div>\n          </div>\n\n          <!-- Formulaire de recherche -->\n          <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSubmit()\" class=\"search-form\" novalidate>\n            \n            <!-- Types de voyage -->\n            <div class=\"trip-type-tabs\">\n              <label class=\"trip-tab\" [class.active]=\"searchForm.get('searchType')?.value === FlightSearchType.ONE_WAY\">\n                <input type=\"radio\" [value]=\"FlightSearchType.ONE_WAY\" formControlName=\"searchType\">\n                <span>One way</span>\n              </label>\n              <label class=\"trip-tab\" [class.active]=\"searchForm.get('searchType')?.value === FlightSearchType.ROUND_TRIP\">\n                <input type=\"radio\" [value]=\"FlightSearchType.ROUND_TRIP\" formControlName=\"searchType\">\n                <span>Round Trip</span>\n              </label>\n              <label class=\"trip-tab\" [class.active]=\"searchForm.get('searchType')?.value === FlightSearchType.MULTI_CITY\">\n                <input type=\"radio\" [value]=\"FlightSearchType.MULTI_CITY\" formControlName=\"searchType\">\n                <span>Multi-City/Stop-Overs</span>\n              </label>\n            </div>\n\n            <!-- Champs de destination -->\n            <div class=\"destination-row\">\n              <div class=\"destination-group\">\n                <label class=\"destination-label\">\n                  <svg class=\"label-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n                  </svg>\n                  Leaving From (City, Country Or Specific Airport)\n                </label>\n                <input\n                  type=\"text\"\n                  formControlName=\"departureLocation\"\n                  class=\"destination-input\"\n                  [class.error]=\"hasError('departureLocation', 'required') || hasError('departureLocation', 'minlength')\"\n                  placeholder=\"Enter departure location\"\n                  autocomplete=\"off\"\n                >\n                <div class=\"error-message\" *ngIf=\"hasError('departureLocation', 'required')\">\n                  Departure location is required\n                </div>\n                <div class=\"error-message\" *ngIf=\"hasError('departureLocation', 'minlength')\">\n                  Please enter at least 3 characters\n                </div>\n              </div>\n\n              <button type=\"button\" class=\"swap-button\" (click)=\"swapAirports()\" title=\"Swap airports\">\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M7.5 21L3 16.5l4.5-4.5L9 13.5 6 16.5h7.5v-9L12 6l1.5 1.5v9H21l-3-3L19.5 12 24 16.5 19.5 21 18 19.5l3-3h-7.5v9L12 24l-1.5-1.5v-9H3l3 3L4.5 18z\"/>\n                </svg>\n              </button>\n\n              <div class=\"destination-group\">\n                <label class=\"destination-label\">\n                  <svg class=\"label-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n                  </svg>\n                  Going To (City, Country Or Specific Airport)\n                </label>\n                <input\n                  type=\"text\"\n                  formControlName=\"arrivalLocation\"\n                  class=\"destination-input\"\n                  [class.error]=\"hasError('arrivalLocation', 'required') || hasError('arrivalLocation', 'minlength')\"\n                  placeholder=\"Enter destination\"\n                  autocomplete=\"off\"\n                >\n                <div class=\"error-message\" *ngIf=\"hasError('arrivalLocation', 'required')\">\n                  Destination is required\n                </div>\n                <div class=\"error-message\" *ngIf=\"hasError('arrivalLocation', 'minlength')\">\n                  Please enter at least 3 characters\n                </div>\n              </div>\n            </div>\n\n            <!-- Dates et Passagers en une ligne -->\n            <div class=\"date-passenger-row\">\n              <div class=\"date-group\">\n                <label class=\"date-label\">From</label>\n                <input\n                  type=\"date\"\n                  formControlName=\"departureDate\"\n                  class=\"date-input\"\n                  [class.error]=\"hasError('departureDate', 'required')\"\n                  [min]=\"getTodayDate()\"\n                >\n                <div class=\"error-message\" *ngIf=\"hasError('departureDate', 'required')\">\n                  Departure date is required\n                </div>\n              </div>\n\n              <div class=\"date-group\" *ngIf=\"searchForm.get('searchType')?.value === FlightSearchType.ROUND_TRIP\">\n                <label class=\"date-label\">To</label>\n                <input\n                  type=\"date\"\n                  formControlName=\"returnDate\"\n                  class=\"date-input\"\n                  [class.error]=\"hasError('returnDate', 'required')\"\n                  [min]=\"searchForm.get('departureDate')?.value\"\n                >\n                <div class=\"error-message\" *ngIf=\"hasError('returnDate', 'required')\">\n                  Return date is required\n                </div>\n              </div>\n\n              <!-- Passagers et Classe -->\n              <div class=\"passenger-class-group\">\n                <label class=\"option-label\">Passenger & Class of travel</label>\n                <div class=\"passenger-class-container\">\n                  <div class=\"passengers-selector\">\n                    <div class=\"passenger-type\">\n                      <span class=\"passenger-icon\">👤</span>\n                      <span class=\"passenger-count\">{{ searchForm.get('adults')?.value }}</span>\n                      <div class=\"passenger-controls\">\n                        <button type=\"button\" class=\"passenger-btn minus\" (click)=\"decrementPassenger('adults')\" [disabled]=\"searchForm.get('adults')?.value <= 1\">-</button>\n                        <button type=\"button\" class=\"passenger-btn plus\" (click)=\"incrementPassenger('adults')\" [disabled]=\"searchForm.get('adults')?.value >= 9\">+</button>\n                      </div>\n                    </div>\n\n                    <div class=\"passenger-type\">\n                      <span class=\"passenger-icon\">👶</span>\n                      <span class=\"passenger-count\">{{ searchForm.get('children')?.value }}</span>\n                      <div class=\"passenger-controls\">\n                        <button type=\"button\" class=\"passenger-btn minus\" (click)=\"decrementPassenger('children')\" [disabled]=\"searchForm.get('children')?.value <= 0\">-</button>\n                        <button type=\"button\" class=\"passenger-btn plus\" (click)=\"incrementPassenger('children')\" [disabled]=\"searchForm.get('children')?.value >= 9\">+</button>\n                      </div>\n                    </div>\n\n                    <div class=\"passenger-type\">\n                      <span class=\"passenger-icon\">🍼</span>\n                      <span class=\"passenger-count\">{{ searchForm.get('infants')?.value }}</span>\n                      <div class=\"passenger-controls\">\n                        <button type=\"button\" class=\"passenger-btn minus\" (click)=\"decrementPassenger('infants')\" [disabled]=\"searchForm.get('infants')?.value <= 0\">-</button>\n                        <button type=\"button\" class=\"passenger-btn plus\" (click)=\"incrementPassenger('infants')\" [disabled]=\"searchForm.get('infants')?.value >= searchForm.get('adults')?.value\">+</button>\n                      </div>\n                    </div>\n                  </div>\n                  <div class=\"passenger-summary\">{{ getPassengerText() }}</div>\n                </div>\n              </div>\n\n              <!-- Classe de vol -->\n              <div class=\"flight-class-group\">\n                <label class=\"option-label\">Preferred Airline</label>\n                <select formControlName=\"flightClass\" class=\"option-select\">\n                  <option *ngFor=\"let flightClass of flightClasses\" [value]=\"flightClass.value\">\n                    {{ flightClass.label }}\n                  </option>\n                </select>\n              </div>\n            </div>\n\n\n\n            <!-- Options supplémentaires -->\n            <div class=\"additional-options\">\n              <label class=\"checkbox-option\">\n                <input type=\"checkbox\" formControlName=\"directFlightsOnly\">\n                <span class=\"checkbox-custom\"></span>\n                <span class=\"checkbox-label\">Direct flights only</span>\n              </label>\n              \n              <div class=\"airline-preference\">\n                <input \n                  type=\"text\" \n                  formControlName=\"preferredAirline\"\n                  class=\"airline-input\"\n                  placeholder=\"Preferred Airline (Optional)\"\n                >\n              </div>\n            </div>\n\n            <!-- Message d'erreur global -->\n            <div class=\"error-alert\" *ngIf=\"errorMessage\">\n              <svg class=\"error-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n              </svg>\n              {{ errorMessage }}\n            </div>\n\n            <!-- Bouton de recherche -->\n            <button \n              type=\"submit\" \n              class=\"search-button\"\n              [disabled]=\"isLoading || searchForm.invalid\"\n              [class.loading]=\"isLoading\"\n            >\n              <span *ngIf=\"!isLoading\" class=\"button-content\">\n                <svg class=\"button-icon\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n                </svg>\n                SEARCH NOW\n              </span>\n              <span *ngIf=\"isLoading\" class=\"loading-content\">\n                <svg class=\"loading-spinner\" viewBox=\"0 0 24 24\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\" fill=\"none\" opacity=\"0.25\"></circle>\n                  <path fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" opacity=\"0.75\"></path>\n                </svg>\n                Searching...\n              </span>\n            </button>\n          </form>\n        </div>\n      </div>\n\n      <!-- Section des dernières recherches (optionnel) -->\n      <div class=\"latest-searches\" *ngIf=\"!isLoading && !searchResults\">\n        <div class=\"searches-card\">\n          <h3>Latest Searches</h3>\n          <p>We're bringing you a new level of comfort</p>\n          <div class=\"search-history\">\n            <div class=\"search-item\">\n              <span class=\"search-route\">Coming From IST - TUN on Jun 18, 2025</span>\n            </div>\n            <div class=\"search-item\">\n              <span class=\"search-route\">Coming From IST - TUN on Jun 18, 2025</span>\n            </div>\n            <div class=\"search-item\">\n              <span class=\"search-route\">Coming From TUN - IST on Jun 11, 2025</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </main>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAEEC,gBAAgB,EAChBC,WAAW,QAEN,8CAA8C;;;;;;;;;ICA7CC,EAAA,CAAAC,cAAA,cAA0C;IACdD,EAAA,CAAAE,MAAA,GAAyD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1FH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD9CH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAC,QAAA,CAAAC,MAAA,kBAAAF,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAAE,IAAA,KAAyD;IAC3DV,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,QAAA,CAAAE,IAAA,SAAAH,MAAA,CAAAC,QAAA,CAAAG,IAAA,KAAyC;;;;;IAiF3DV,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwBNH,EAAA,CAAAC,cAAA,cAA2E;IACzED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAA4E;IAC1ED,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAeNH,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAXRH,EAAA,CAAAC,cAAA,cAAoG;IACxED,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpCH,EAAA,CAAAW,SAAA,gBAMC;IACDX,EAAA,CAAAY,UAAA,IAAAC,2CAAA,kBAEM;IACRb,EAAA,CAAAG,YAAA,EAAM;;;;;IANFH,EAAA,CAAAI,SAAA,GAAkD;IAAlDJ,EAAA,CAAAc,WAAA,UAAAC,MAAA,CAAAC,QAAA,2BAAkD;IAClDhB,EAAA,CAAAiB,UAAA,SAAAC,OAAA,GAAAH,MAAA,CAAAI,UAAA,CAAAC,GAAA,oCAAAF,OAAA,CAAAG,KAAA,CAA8C;IAEpBrB,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAiB,UAAA,SAAAF,MAAA,CAAAC,QAAA,2BAAwC;;;;;IA6ClEhB,EAAA,CAAAC,cAAA,iBAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFyCH,EAAA,CAAAiB,UAAA,UAAAK,eAAA,CAAAD,KAAA,CAA2B;IAC3ErB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuB,kBAAA,MAAAD,eAAA,CAAAE,KAAA,MACF;;;;;IA0BNxB,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAyB,cAAA,EAAgE;IAAhEzB,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAW,SAAA,eAAiI;IACnIX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAuB,kBAAA,MAAAG,MAAA,CAAAC,YAAA,MACF;;;;;IASE3B,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAyB,cAAA,EAAiE;IAAjEzB,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAW,SAAA,eAAsP;IACxPX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAyB,cAAA,EAAiD;IAAjDzB,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAW,SAAA,iBAA0G;IAE5GX,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOfH,EAAA,CAAAC,cAAA,cAAkE;IAE1DD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,cAA4B;IAEGD,EAAA,CAAAE,MAAA,4CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzEH,EAAA,CAAAC,cAAA,eAAyB;IACID,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzEH,EAAA,CAAAC,cAAA,eAAyB;IACID,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADjQrF,OAAM,MAAOyB,qBAAqB;EAqBhCC,YACUC,WAAwB,EACxBC,mBAAwC,EACzCC,WAAwB,EACxBC,MAAc;IAHb,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACpB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvBf,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAP,YAAY,GAAG,EAAE;IACjB,KAAAQ,aAAa,GAAQ,IAAI;IAEzB;IACA,KAAArC,gBAAgB,GAAGA,gBAAgB;IACnC,KAAAC,WAAW,GAAGA,WAAW;IAEzB;IACA,KAAAqC,aAAa,GAAG,CACd;MAAEf,KAAK,EAAEtB,WAAW,CAACsC,OAAO;MAAEb,KAAK,EAAE;IAAS,CAAE,EAChD;MAAEH,KAAK,EAAEtB,WAAW,CAACuC,eAAe;MAAEd,KAAK,EAAE;IAAiB,CAAE,EAChE;MAAEH,KAAK,EAAEtB,WAAW,CAACwC,QAAQ;MAAEf,KAAK,EAAE;IAAU,CAAE,EAClD;MAAEH,KAAK,EAAEtB,WAAW,CAACyC,KAAK;MAAEhB,KAAK,EAAE;IAAa,CAAE,CACnD;EAUE;EAEHiB,QAAQA,CAAA;IACN,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,YAAY,EAAE;IAEnB;IACA,IAAI,CAAC,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE,EAAE;MACvC,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;EAEA;;;EAGQH,cAAcA,CAAA;IACpB,IAAI,CAACvB,UAAU,GAAG,IAAI,CAACW,WAAW,CAACgB,KAAK,CAAC;MACvCC,UAAU,EAAE,CAACjD,gBAAgB,CAACkD,OAAO,EAAEnD,UAAU,CAACoD,QAAQ,CAAC;MAC3DC,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAACoD,QAAQ,EAAEpD,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,eAAe,EAAE,CAAC,EAAE,EAAE,CAACvD,UAAU,CAACoD,QAAQ,EAAEpD,UAAU,CAACsD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACrEE,aAAa,EAAE,CAAC,EAAE,EAAExD,UAAU,CAACoD,QAAQ,CAAC;MACxCK,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC1D,UAAU,CAACoD,QAAQ,EAAEpD,UAAU,CAAC2D,GAAG,CAAC,CAAC,CAAC,EAAE3D,UAAU,CAAC4D,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxEC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC7D,UAAU,CAAC2D,GAAG,CAAC,CAAC,CAAC,EAAE3D,UAAU,CAAC4D,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrDE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC9D,UAAU,CAAC2D,GAAG,CAAC,CAAC,CAAC,EAAE3D,UAAU,CAAC4D,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpDG,WAAW,EAAE,CAAC7D,WAAW,CAACsC,OAAO,EAAExC,UAAU,CAACoD,QAAQ,CAAC;MACvDY,iBAAiB,EAAE,CAAC,KAAK,CAAC;MAC1BC,gBAAgB,EAAE,CAAC,EAAE;KACtB,CAAC;IAEF;IACA,IAAI,CAAC3C,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC,EAAE2C,YAAY,CAACC,SAAS,CAAC3C,KAAK,IAAG;MAChE,IAAI,CAAC4C,kBAAkB,CAAC5C,KAAK,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA;;;EAGQsB,YAAYA,CAAA;IAClB,IAAI,CAACpC,QAAQ,GAAG,IAAI,CAACyB,WAAW,CAACkC,WAAW,EAAE;EAChD;EAEA;;;EAGAD,kBAAkBA,CAAClB,UAA4B;IAC7C,MAAMoB,iBAAiB,GAAG,IAAI,CAAChD,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;IAE3D,IAAI2B,UAAU,KAAKjD,gBAAgB,CAACsE,UAAU,EAAE;MAC9CD,iBAAiB,EAAEE,aAAa,CAAC,CAACxE,UAAU,CAACoD,QAAQ,CAAC,CAAC;KACxD,MAAM;MACLkB,iBAAiB,EAAEG,eAAe,EAAE;MACpCH,iBAAiB,EAAEI,QAAQ,CAAC,EAAE,CAAC;;IAGjCJ,iBAAiB,EAAEK,sBAAsB,EAAE;EAC7C;EAEA;;;EAGAC,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAG,IAAI,CAACvD,UAAU,CAACC,GAAG,CAAC,mBAAmB,CAAC,EAAEC,KAAK;IACjE,MAAMsD,OAAO,GAAG,IAAI,CAACxD,UAAU,CAACC,GAAG,CAAC,iBAAiB,CAAC,EAAEC,KAAK;IAE7D,IAAI,CAACF,UAAU,CAACyD,UAAU,CAAC;MACzB1B,iBAAiB,EAAEyB,OAAO;MAC1BvB,eAAe,EAAEsB;KAClB,CAAC;EACJ;EAEA;;;EAGAG,kBAAkBA,CAACC,IAAY;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAAC5D,UAAU,CAACC,GAAG,CAAC0D,IAAI,CAAC;IACzC,MAAME,YAAY,GAAGD,OAAO,EAAE1D,KAAK,IAAI,CAAC;IACxC,MAAM4D,QAAQ,GAAGH,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC3D,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,GAAG,CAAC;IAE9E,IAAI2D,YAAY,GAAGC,QAAQ,EAAE;MAC3BF,OAAO,EAAER,QAAQ,CAACS,YAAY,GAAG,CAAC,CAAC;;EAEvC;EAEA;;;EAGAE,kBAAkBA,CAACJ,IAAY;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAAC5D,UAAU,CAACC,GAAG,CAAC0D,IAAI,CAAC;IACzC,MAAME,YAAY,GAAGD,OAAO,EAAE1D,KAAK,IAAI,CAAC;IACxC,MAAM8D,QAAQ,GAAGL,IAAI,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC;IAE1C,IAAIE,YAAY,GAAGG,QAAQ,EAAE;MAC3BJ,OAAO,EAAER,QAAQ,CAACS,YAAY,GAAG,CAAC,CAAC;;EAEvC;EAEA;;;EAGAI,kBAAkBA,CAAA;IAChB,MAAM7B,MAAM,GAAG,IAAI,CAACpC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,IAAI,CAAC;IACxD,MAAMqC,QAAQ,GAAG,IAAI,CAACvC,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC5D,MAAMsC,OAAO,GAAG,IAAI,CAACxC,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC1D,OAAOkC,MAAM,GAAGG,QAAQ,GAAGC,OAAO;EACpC;EAEA;;;EAGA0B,gBAAgBA,CAAA;IACd,MAAM9B,MAAM,GAAG,IAAI,CAACpC,UAAU,CAACC,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK,IAAI,CAAC;IACxD,MAAMqC,QAAQ,GAAG,IAAI,CAACvC,UAAU,CAACC,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK,IAAI,CAAC;IAC5D,MAAMsC,OAAO,GAAG,IAAI,CAACxC,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC,EAAEC,KAAK,IAAI,CAAC;IAE1D,IAAIiE,IAAI,GAAG,GAAG/B,MAAM,SAASA,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IACpD,IAAIG,QAAQ,GAAG,CAAC,EAAE4B,IAAI,IAAI,KAAK5B,QAAQ,SAASA,QAAQ,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE;IAC3E,IAAIC,OAAO,GAAG,CAAC,EAAE2B,IAAI,IAAI,KAAK3B,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;IAEvE,OAAO2B,IAAI;EACb;EAEA;;;EAGAtE,QAAQA,CAACuE,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAACtE,UAAU,CAACC,GAAG,CAACmE,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAEE,KAAK,IAAIA,KAAK,CAACzE,QAAQ,CAACwE,SAAS,CAAC,KAAKC,KAAK,CAACC,KAAK,IAAID,KAAK,CAACE,OAAO,CAAC,CAAC;EACjF;EAEA;;;EAGQC,oBAAoBA,CAAA;IAC1BC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3E,UAAU,CAAC4E,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMlB,OAAO,GAAG,IAAI,CAAC5D,UAAU,CAACC,GAAG,CAAC6E,GAAG,CAAC;MACxClB,OAAO,EAAEmB,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAChF,UAAU,CAACiF,KAAK,IAAI,CAAC,IAAI,CAAClE,SAAS,EAAE;MAC5C,IAAI,CAACA,SAAS,GAAG,IAAI;MACrB,IAAI,CAACP,YAAY,GAAG,EAAE;MACtB,IAAI,CAACQ,aAAa,GAAG,IAAI;MAEzB,MAAMkE,QAAQ,GAAqB;QACjCtD,UAAU,EAAE,IAAI,CAAC5B,UAAU,CAACE,KAAK,CAAC0B,UAAU;QAC5CG,iBAAiB,EAAE,IAAI,CAAC/B,UAAU,CAACE,KAAK,CAAC6B,iBAAiB,CAACoD,IAAI,EAAE;QACjElD,eAAe,EAAE,IAAI,CAACjC,UAAU,CAACE,KAAK,CAAC+B,eAAe,CAACkD,IAAI,EAAE;QAC7DjD,aAAa,EAAE,IAAI,CAAClC,UAAU,CAACE,KAAK,CAACgC,aAAa;QAClDC,UAAU,EAAE,IAAI,CAACnC,UAAU,CAACE,KAAK,CAACiC,UAAU,IAAIiD,SAAS;QACzDC,UAAU,EAAE;UACVjD,MAAM,EAAE,IAAI,CAACpC,UAAU,CAACE,KAAK,CAACkC,MAAM;UACpCG,QAAQ,EAAE,IAAI,CAACvC,UAAU,CAACE,KAAK,CAACqC,QAAQ;UACxCC,OAAO,EAAE,IAAI,CAACxC,UAAU,CAACE,KAAK,CAACsC;SAChC;QACDC,WAAW,EAAE,IAAI,CAACzC,UAAU,CAACE,KAAK,CAACuC,WAAW;QAC9CC,iBAAiB,EAAE,IAAI,CAAC1C,UAAU,CAACE,KAAK,CAACwC,iBAAiB;QAC1DC,gBAAgB,EAAE,IAAI,CAAC3C,UAAU,CAACE,KAAK,CAACyC,gBAAgB,IAAIyC,SAAS;QACrEE,OAAO,EAAE,OAAO;QAChBC,QAAQ,EAAE,KAAK,CAAG;OACnB;;MAED,IAAI,CAAC3E,mBAAmB,CAAC4E,aAAa,CAACN,QAAQ,CAAC,CAACrC,SAAS,CAAC;QACzD4C,IAAI,EAAGC,QAA8B,IAAI;UACvC,IAAI,CAAC3E,SAAS,GAAG,KAAK;UAEtB,IAAI2E,QAAQ,CAACC,MAAM,CAACC,OAAO,EAAE;YAC3B,IAAI,CAAC5E,aAAa,GAAG0E,QAAQ,CAACG,IAAI;YAClCC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC/E,aAAa,CAAC;YAC1D;WACD,MAAM;YACL,IAAI,CAACgF,cAAc,CAACN,QAAQ,CAAC;;QAEjC,CAAC;QACDO,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAAClF,SAAS,GAAG,KAAK;UACtB,IAAI,CAACP,YAAY,GAAGyF,KAAK,CAACC,OAAO,IAAI,8CAA8C;UACnFJ,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAACxB,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQuB,cAAcA,CAACN,QAA8B;IACnD,IAAIA,QAAQ,CAACC,MAAM,CAACQ,QAAQ,IAAIT,QAAQ,CAACC,MAAM,CAACQ,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;MACnE,IAAI,CAAC5F,YAAY,GAAGkF,QAAQ,CAACC,MAAM,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACD,OAAO;KACxD,MAAM;MACL,IAAI,CAAC1F,YAAY,GAAG,iDAAiD;;EAEzE;EAEA;;;EAGA6F,SAASA,CAAA;IACP,IAAI,CAACrG,UAAU,CAACsG,KAAK,EAAE;IACvB,IAAI,CAAC/E,cAAc,EAAE;IACrB,IAAI,CAACf,YAAY,GAAG,EAAE;IACtB,IAAI,CAACQ,aAAa,GAAG,IAAI;EAC3B;EAEA;;;EAGAuF,YAAYA,CAAA;IACV,MAAMC,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxB,OAAOD,KAAK,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1C;;;uBApPWlG,qBAAqB,EAAA5B,EAAA,CAAA+H,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjI,EAAA,CAAA+H,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAnI,EAAA,CAAA+H,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAA+H,iBAAA,CAAAO,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAArB3G,qBAAqB;MAAA4G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClBlC9I,EAAA,CAAAC,cAAA,aAAqC;UAM3BD,EAAA,CAAAyB,cAAA,EAA+D;UAA/DzB,EAAA,CAAAC,cAAA,aAA+D;UAC7DD,EAAA,CAAAW,SAAA,cAAmE;UACrEX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAgJ,eAAA,EAAwB;UAAxBhJ,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9CH,EAAA,CAAAY,UAAA,IAAAqI,oCAAA,iBAGM;UACRjJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAA4B;UAExBD,EAAA,CAAAyB,cAAA,EAA6C;UAA7CzB,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,SAAA,gBAA+C;UACjDX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,MAAA,cACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAgJ,eAAA,EAA2B;UAA3BhJ,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAyB,cAAA,EAA6C;UAA7CzB,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,SAAA,gBAAiI;UACnIX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAgJ,eAAA,EAAkG;UAAlGhJ,EAAA,CAAAC,cAAA,kBAAkG;UAA5DD,EAAA,CAAAkJ,UAAA,mBAAAC,wDAAA;YAASJ,GAAA,CAAA/G,WAAA,CAAAoH,MAAA,EAAoB;YAAA,OAAEL,GAAA,CAAA9G,MAAA,CAAAY,QAAA,EAAiB,QAAQ,EAAE;UAAA,EAAC;UAC/F7C,EAAA,CAAAyB,cAAA,EAA6C;UAA7CzB,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,SAAA,gBAAqE;UACvEX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAgJ,eAAA,EAA0B;UAA1BhJ,EAAA,CAAAC,cAAA,gBAA0B;UAOdD,EAAA,CAAAyB,cAAA,EAA6C;UAA7CzB,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,SAAA,gBAAkI;UACpIX,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAgJ,eAAA,EAA2B;UAA3BhJ,EAAA,CAAAC,cAAA,eAA2B;UACrBD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKpDH,EAAA,CAAAC,cAAA,gBAAsF;UAAvDD,EAAA,CAAAkJ,UAAA,sBAAAG,yDAAA;YAAA,OAAYN,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAGpDnG,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAW,SAAA,iBAAoF;UACpFX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtBH,EAAA,CAAAC,cAAA,iBAA6G;UAC3GD,EAAA,CAAAW,SAAA,iBAAuF;UACvFX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,iBAA6G;UAC3GD,EAAA,CAAAW,SAAA,iBAAuF;UACvFX,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKtCH,EAAA,CAAAC,cAAA,eAA6B;UAGvBD,EAAA,CAAAyB,cAAA,EAAgE;UAAhEzB,EAAA,CAAAC,cAAA,eAAgE;UAC9DD,EAAA,CAAAW,SAAA,gBAAsK;UACxKX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,MAAA,0DACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAgJ,eAAA,EAOC;UAPDhJ,EAAA,CAAAW,SAAA,iBAOC;UACDX,EAAA,CAAAY,UAAA,KAAA0I,qCAAA,kBAEM;UACNtJ,EAAA,CAAAY,UAAA,KAAA2I,qCAAA,kBAEM;UACRvJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,kBAAyF;UAA/CD,EAAA,CAAAkJ,UAAA,mBAAAM,wDAAA;YAAA,OAAST,GAAA,CAAAtE,YAAA,EAAc;UAAA,EAAC;UAChEzE,EAAA,CAAAyB,cAAA,EAA6C;UAA7CzB,EAAA,CAAAC,cAAA,eAA6C;UAC3CD,EAAA,CAAAW,SAAA,gBAAyJ;UAC3JX,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAgJ,eAAA,EAA+B;UAA/BhJ,EAAA,CAAAC,cAAA,eAA+B;UAE3BD,EAAA,CAAAyB,cAAA,EAAgE;UAAhEzB,EAAA,CAAAC,cAAA,eAAgE;UAC9DD,EAAA,CAAAW,SAAA,gBAAsK;UACxKX,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAE,MAAA,sDACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAgJ,eAAA,EAOC;UAPDhJ,EAAA,CAAAW,SAAA,iBAOC;UACDX,EAAA,CAAAY,UAAA,KAAA6I,qCAAA,kBAEM;UACNzJ,EAAA,CAAAY,UAAA,KAAA8I,qCAAA,kBAEM;UACR1J,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,eAAgC;UAEFD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAW,SAAA,iBAMC;UACDX,EAAA,CAAAY,UAAA,KAAA+I,qCAAA,kBAEM;UACR3J,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAY,UAAA,KAAAgJ,qCAAA,kBAYM;UAGN5J,EAAA,CAAAC,cAAA,eAAmC;UACLD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,eAAuC;UAGJD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1EH,EAAA,CAAAC,cAAA,eAAgC;UACoBD,EAAA,CAAAkJ,UAAA,mBAAAW,wDAAA;YAAA,OAASd,GAAA,CAAA7D,kBAAA,CAAmB,QAAQ,CAAC;UAAA,EAAC;UAAmDlF,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrJH,EAAA,CAAAC,cAAA,kBAA0I;UAAzFD,EAAA,CAAAkJ,UAAA,mBAAAY,wDAAA;YAAA,OAASf,GAAA,CAAAlE,kBAAA,CAAmB,QAAQ,CAAC;UAAA,EAAC;UAAmD7E,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIxJH,EAAA,CAAAC,cAAA,eAA4B;UACGD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,gBAA8B;UAAAD,EAAA,CAAAE,MAAA,IAAuC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5EH,EAAA,CAAAC,cAAA,eAAgC;UACoBD,EAAA,CAAAkJ,UAAA,mBAAAa,wDAAA;YAAA,OAAShB,GAAA,CAAA7D,kBAAA,CAAmB,UAAU,CAAC;UAAA,EAAC;UAAqDlF,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzJH,EAAA,CAAAC,cAAA,mBAA8I;UAA7FD,EAAA,CAAAkJ,UAAA,mBAAAc,yDAAA;YAAA,OAASjB,GAAA,CAAAlE,kBAAA,CAAmB,UAAU,CAAC;UAAA,EAAC;UAAqD7E,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI5JH,EAAA,CAAAC,cAAA,gBAA4B;UACGD,EAAA,CAAAE,MAAA,qBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,iBAA8B;UAAAD,EAAA,CAAAE,MAAA,KAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3EH,EAAA,CAAAC,cAAA,gBAAgC;UACoBD,EAAA,CAAAkJ,UAAA,mBAAAe,yDAAA;YAAA,OAASlB,GAAA,CAAA7D,kBAAA,CAAmB,SAAS,CAAC;UAAA,EAAC;UAAoDlF,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvJH,EAAA,CAAAC,cAAA,mBAA0K;UAAzHD,EAAA,CAAAkJ,UAAA,mBAAAgB,yDAAA;YAAA,OAASnB,GAAA,CAAAlE,kBAAA,CAAmB,SAAS,CAAC;UAAA,EAAC;UAAkF7E,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI1LH,EAAA,CAAAC,cAAA,gBAA+B;UAAAD,EAAA,CAAAE,MAAA,KAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKjEH,EAAA,CAAAC,cAAA,gBAAgC;UACFD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAC,cAAA,mBAA4D;UAC1DD,EAAA,CAAAY,UAAA,MAAAuJ,yCAAA,qBAES;UACXnK,EAAA,CAAAG,YAAA,EAAS;UAObH,EAAA,CAAAC,cAAA,gBAAgC;UAE5BD,EAAA,CAAAW,SAAA,kBAA2D;UAE3DX,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGzDH,EAAA,CAAAC,cAAA,gBAAgC;UAC9BD,EAAA,CAAAW,SAAA,kBAKC;UACHX,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAY,UAAA,MAAAwJ,sCAAA,kBAKM;UAGNpK,EAAA,CAAAC,cAAA,mBAKC;UACCD,EAAA,CAAAY,UAAA,MAAAyJ,uCAAA,mBAKO;UACPrK,EAAA,CAAAY,UAAA,MAAA0J,uCAAA,mBAMO;UACTtK,EAAA,CAAAG,YAAA,EAAS;UAMfH,EAAA,CAAAY,UAAA,MAAA2J,sCAAA,mBAgBM;UACRvK,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;;;;;;UA7QwBH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAAxI,QAAA,CAAc;UAgDhCP,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAiB,UAAA,cAAA8H,GAAA,CAAA5H,UAAA,CAAwB;UAIFnB,EAAA,CAAAI,SAAA,GAAiF;UAAjFJ,EAAA,CAAAc,WAAA,aAAA0J,OAAA,GAAAzB,GAAA,CAAA5H,UAAA,CAAAC,GAAA,iCAAAoJ,OAAA,CAAAnJ,KAAA,MAAA0H,GAAA,CAAAjJ,gBAAA,CAAAkD,OAAA,CAAiF;UACnFhD,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAiB,UAAA,UAAA8H,GAAA,CAAAjJ,gBAAA,CAAAkD,OAAA,CAAkC;UAGhChD,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAc,WAAA,aAAA2J,OAAA,GAAA1B,GAAA,CAAA5H,UAAA,CAAAC,GAAA,iCAAAqJ,OAAA,CAAApJ,KAAA,MAAA0H,GAAA,CAAAjJ,gBAAA,CAAAsE,UAAA,CAAoF;UACtFpE,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAiB,UAAA,UAAA8H,GAAA,CAAAjJ,gBAAA,CAAAsE,UAAA,CAAqC;UAGnCpE,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAc,WAAA,aAAA4J,OAAA,GAAA3B,GAAA,CAAA5H,UAAA,CAAAC,GAAA,iCAAAsJ,OAAA,CAAArJ,KAAA,MAAA0H,GAAA,CAAAjJ,gBAAA,CAAA6K,UAAA,CAAoF;UACtF3K,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAiB,UAAA,UAAA8H,GAAA,CAAAjJ,gBAAA,CAAA6K,UAAA,CAAqC;UAkBvD3K,EAAA,CAAAI,SAAA,GAAuG;UAAvGJ,EAAA,CAAAc,WAAA,UAAAiI,GAAA,CAAA/H,QAAA,qCAAA+H,GAAA,CAAA/H,QAAA,mCAAuG;UAI7EhB,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA/H,QAAA,kCAA+C;UAG/ChB,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA/H,QAAA,mCAAgD;UAsB1EhB,EAAA,CAAAI,SAAA,GAAmG;UAAnGJ,EAAA,CAAAc,WAAA,UAAAiI,GAAA,CAAA/H,QAAA,mCAAA+H,GAAA,CAAA/H,QAAA,iCAAmG;UAIzEhB,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA/H,QAAA,gCAA6C;UAG7ChB,EAAA,CAAAI,SAAA,GAA8C;UAA9CJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA/H,QAAA,iCAA8C;UAcxEhB,EAAA,CAAAI,SAAA,GAAqD;UAArDJ,EAAA,CAAAc,WAAA,UAAAiI,GAAA,CAAA/H,QAAA,8BAAqD;UACrDhB,EAAA,CAAAiB,UAAA,QAAA8H,GAAA,CAAArB,YAAA,GAAsB;UAEI1H,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA/H,QAAA,8BAA2C;UAKhDhB,EAAA,CAAAI,SAAA,GAAyE;UAAzEJ,EAAA,CAAAiB,UAAA,WAAA2J,QAAA,GAAA7B,GAAA,CAAA5H,UAAA,CAAAC,GAAA,iCAAAwJ,QAAA,CAAAvJ,KAAA,MAAA0H,GAAA,CAAAjJ,gBAAA,CAAAsE,UAAA,CAAyE;UAqB5DpE,EAAA,CAAAI,SAAA,IAAqC;UAArCJ,EAAA,CAAA6K,iBAAA,EAAAC,QAAA,GAAA/B,GAAA,CAAA5H,UAAA,CAAAC,GAAA,6BAAA0J,QAAA,CAAAzJ,KAAA,CAAqC;UAEwBrB,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAiB,UAAA,eAAA8J,QAAA,GAAAhC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,6BAAA2J,QAAA,CAAA1J,KAAA,OAAiD;UAClDrB,EAAA,CAAAI,SAAA,GAAiD;UAAjDJ,EAAA,CAAAiB,UAAA,eAAA+J,QAAA,GAAAjC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,6BAAA4J,QAAA,CAAA3J,KAAA,OAAiD;UAM7GrB,EAAA,CAAAI,SAAA,GAAuC;UAAvCJ,EAAA,CAAA6K,iBAAA,EAAAI,QAAA,GAAAlC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,+BAAA6J,QAAA,CAAA5J,KAAA,CAAuC;UAEwBrB,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAiB,UAAA,eAAAiK,QAAA,GAAAnC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,+BAAA8J,QAAA,CAAA7J,KAAA,OAAmD;UACpDrB,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAiB,UAAA,eAAAkK,QAAA,GAAApC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,+BAAA+J,QAAA,CAAA9J,KAAA,OAAmD;UAMjHrB,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAA6K,iBAAA,EAAAO,QAAA,GAAArC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,8BAAAgK,QAAA,CAAA/J,KAAA,CAAsC;UAEwBrB,EAAA,CAAAI,SAAA,GAAkD;UAAlDJ,EAAA,CAAAiB,UAAA,eAAAoK,QAAA,GAAAtC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,8BAAAiK,QAAA,CAAAhK,KAAA,OAAkD;UACnDrB,EAAA,CAAAI,SAAA,GAAgF;UAAhFJ,EAAA,CAAAiB,UAAA,eAAAqK,QAAA,GAAAvC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,8BAAAkK,QAAA,CAAAjK,KAAA,OAAAiK,QAAA,GAAAvC,GAAA,CAAA5H,UAAA,CAAAC,GAAA,6BAAAkK,QAAA,CAAAjK,KAAA,EAAgF;UAIhJrB,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA6K,iBAAA,CAAA9B,GAAA,CAAA1D,gBAAA,GAAwB;UAQvBrF,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAA8H,GAAA,CAAA3G,aAAA,CAAgB;UA4B5BpC,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAApH,YAAA,CAAkB;UAY1C3B,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAc,WAAA,YAAAiI,GAAA,CAAA7G,SAAA,CAA2B;UAD3BlC,EAAA,CAAAiB,UAAA,aAAA8H,GAAA,CAAA7G,SAAA,IAAA6G,GAAA,CAAA5H,UAAA,CAAAoK,OAAA,CAA4C;UAGrCvL,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,UAAA8H,GAAA,CAAA7G,SAAA,CAAgB;UAMhBlC,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAiB,UAAA,SAAA8H,GAAA,CAAA7G,SAAA,CAAe;UAaAlC,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAiB,UAAA,UAAA8H,GAAA,CAAA7G,SAAA,KAAA6G,GAAA,CAAA5G,aAAA,CAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}