{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightSearchComponent } from './components/flight-search/flight-search.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: HttpRequestInterceptor,\n        multi: true\n      }],\n      imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, LoginComponent, DashboardComponent, FlightSearchComponent],\n    imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "ReactiveFormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppRoutingModule", "AppComponent", "LoginComponent", "DashboardComponent", "FlightSearchComponent", "HttpRequestInterceptor", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightSearchComponent } from './components/flight-search/flight-search.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    DashboardComponent,\n    FlightSearchComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    HttpClientModule\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: HttpRequestInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,sBAAsB,QAAQ,iCAAiC;;AAwBxE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRN,YAAY;IAAA;EAAA;;;iBAPb,CACT;QACEO,OAAO,EAAET,iBAAiB;QAC1BU,QAAQ,EAAEJ,sBAAsB;QAChCK,KAAK,EAAE;OACR,CACF;MAAAC,OAAA,GAXCf,aAAa,EACbI,gBAAgB,EAChBH,mBAAmB,EACnBC,gBAAgB;IAAA;EAAA;;;2EAWPQ,SAAS;IAAAM,YAAA,GApBlBX,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,qBAAqB;IAAAO,OAAA,GAGrBf,aAAa,EACbI,gBAAgB,EAChBH,mBAAmB,EACnBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}