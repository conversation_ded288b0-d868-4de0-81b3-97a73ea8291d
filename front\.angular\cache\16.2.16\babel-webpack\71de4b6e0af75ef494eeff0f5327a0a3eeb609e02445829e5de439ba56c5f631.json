{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AirlineLogoService {\n  constructor() {\n    this.logoMap = {\n      'TK': 'https://content.airhex.com/content/logos/airlines_TK_200_200_s.png',\n      'TU': 'https://content.airhex.com/content/logos/airlines_TU_200_200_s.png',\n      'AF': 'https://content.airhex.com/content/logos/airlines_AF_200_200_s.png',\n      'LH': 'https://content.airhex.com/content/logos/airlines_LH_200_200_s.png',\n      'EK': 'https://content.airhex.com/content/logos/airlines_EK_200_200_s.png',\n      'QR': 'https://content.airhex.com/content/logos/airlines_QR_200_200_s.png',\n      'BA': 'https://content.airhex.com/content/logos/airlines_BA_200_200_s.png',\n      'KL': 'https://content.airhex.com/content/logos/airlines_KL_200_200_s.png',\n      'IB': 'https://content.airhex.com/content/logos/airlines_IB_200_200_s.png',\n      'AZ': 'https://content.airhex.com/content/logos/airlines_AZ_200_200_s.png',\n      'MS': 'https://content.airhex.com/content/logos/airlines_MS_200_200_s.png',\n      'SV': 'https://content.airhex.com/content/logos/airlines_SV_200_200_s.png',\n      'RJ': 'https://content.airhex.com/content/logos/airlines_RJ_200_200_s.png',\n      'UX': 'https://content.airhex.com/content/logos/airlines_UX_200_200_s.png'\n    };\n  }\n  /**\n   * Obtient le logo d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode) {\n    return this.logoMap[airlineCode] || null;\n  }\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode) {\n    return airlineCode in this.logoMap;\n  }\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines() {\n    return Object.keys(this.logoMap);\n  }\n  static {\n    this.ɵfac = function AirlineLogoService_Factory(t) {\n      return new (t || AirlineLogoService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AirlineLogoService,\n      factory: AirlineLogoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AirlineLogoService", "constructor", "logoMap", "getAirlineLogo", "airlineCode", "<PERSON><PERSON><PERSON>", "getAvailableAirlines", "Object", "keys", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\airline-logo.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AirlineLogoService {\n  private logoMap: { [key: string]: string } = {\n    'TK': 'https://content.airhex.com/content/logos/airlines_TK_200_200_s.png',\n    'TU': 'https://content.airhex.com/content/logos/airlines_TU_200_200_s.png',\n    'AF': 'https://content.airhex.com/content/logos/airlines_AF_200_200_s.png',\n    'LH': 'https://content.airhex.com/content/logos/airlines_LH_200_200_s.png',\n    'EK': 'https://content.airhex.com/content/logos/airlines_EK_200_200_s.png',\n    'QR': 'https://content.airhex.com/content/logos/airlines_QR_200_200_s.png',\n    'BA': 'https://content.airhex.com/content/logos/airlines_BA_200_200_s.png',\n    'KL': 'https://content.airhex.com/content/logos/airlines_KL_200_200_s.png',\n    'IB': 'https://content.airhex.com/content/logos/airlines_IB_200_200_s.png',\n    'AZ': 'https://content.airhex.com/content/logos/airlines_AZ_200_200_s.png',\n    'MS': 'https://content.airhex.com/content/logos/airlines_MS_200_200_s.png',\n    'SV': 'https://content.airhex.com/content/logos/airlines_SV_200_200_s.png',\n    'RJ': 'https://content.airhex.com/content/logos/airlines_RJ_200_200_s.png',\n    'UX': 'https://content.airhex.com/content/logos/airlines_UX_200_200_s.png'\n  };\n\n  constructor() { }\n\n  /**\n   * Obtient le logo d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode: string): string | null {\n    return this.logoMap[airlineCode] || null;\n  }\n\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode: string): boolean {\n    return airlineCode in this.logoMap;\n  }\n\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines(): string[] {\n    return Object.keys(this.logoMap);\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAkB7BC,YAAA;IAjBQ,KAAAC,OAAO,GAA8B;MAC3C,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE;KACP;EAEe;EAEhB;;;EAGAC,cAAcA,CAACC,WAAmB;IAChC,OAAO,IAAI,CAACF,OAAO,CAACE,WAAW,CAAC,IAAI,IAAI;EAC1C;EAEA;;;EAGAC,OAAOA,CAACD,WAAmB;IACzB,OAAOA,WAAW,IAAI,IAAI,CAACF,OAAO;EACpC;EAEA;;;EAGAI,oBAAoBA,CAAA;IAClB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO,CAAC;EAClC;;;uBAvCWF,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAS,OAAA,EAAlBT,kBAAkB,CAAAU,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}