{"ast": null, "code": "/**\n * Interfaces TypeScript correspondant aux modèles de requête de recherche de vols du backend Spring Boot\n */\n// ===== TYPES D'ÉNUMÉRATION =====\nexport var FlightSearchType;\n(function (FlightSearchType) {\n  FlightSearchType[\"ONE_WAY\"] = \"oneWay\";\n  FlightSearchType[\"ROUND_TRIP\"] = \"roundTrip\";\n  FlightSearchType[\"MULTI_CITY\"] = \"multiCity\";\n})(FlightSearchType || (FlightSearchType = {}));\nexport var PassengerType;\n(function (PassengerType) {\n  PassengerType[PassengerType[\"ADULT\"] = 1] = \"ADULT\";\n  PassengerType[PassengerType[\"CHILD\"] = 2] = \"CHILD\";\n  PassengerType[PassengerType[\"INFANT\"] = 3] = \"INFANT\";\n})(PassengerType || (PassengerType = {}));\nexport var LocationType;\n(function (LocationType) {\n  LocationType[LocationType[\"AIRPORT\"] = 1] = \"AIRPORT\";\n  LocationType[LocationType[\"CITY\"] = 2] = \"CITY\";\n  LocationType[LocationType[\"COUNTRY\"] = 3] = \"COUNTRY\";\n})(LocationType || (LocationType = {}));\nexport var FlightClass;\n(function (FlightClass) {\n  FlightClass[FlightClass[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClass[FlightClass[\"PREMIUM_ECONOMY\"] = 2] = \"PREMIUM_ECONOMY\";\n  FlightClass[FlightClass[\"BUSINESS\"] = 3] = \"BUSINESS\";\n  FlightClass[FlightClass[\"FIRST\"] = 4] = \"FIRST\";\n})(FlightClass || (FlightClass = {}));", "map": {"version": 3, "names": ["FlightSearchType", "PassengerType", "LocationType", "FlightClass"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\models\\flight-search-request.interface.ts"], "sourcesContent": ["/**\n * Interfaces TypeScript correspondant aux modèles de requête de recherche de vols du backend Spring Boot\n */\n\n// ===== INTERFACES COMMUNES =====\n\nexport interface Location {\n  id: string;\n  type: number;\n  provider?: number;\n}\n\nexport interface Passenger {\n  type: number;\n  count: number;\n}\n\nexport interface CorporateRule {\n  Airline: string;\n  Supplier: string;\n}\n\nexport interface CorporateCode {\n  Code: string;\n  Rule: CorporateRule;\n}\n\nexport interface GetOptionsParameters {\n  flightBaggageGetOption: number;\n}\n\nexport interface AdditionalParameters {\n  getOptionsParameters?: GetOptionsParameters;\n  CorporateCodes?: CorporateCode[];\n}\n\n// ===== ONE WAY REQUEST =====\n\nexport interface OneWayRequest {\n  ProductType: number;\n  ServiceTypes: string[];\n  CheckIn: string;\n  DepartureLocations: Location[];\n  ArrivalLocations: Location[];\n  Passengers: Passenger[];\n  showOnlyNonStopFlight: boolean;\n  additionalParameters?: AdditionalParameters;\n  acceptPendingProviders: boolean;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  calculateFlightFees: boolean;\n  flightClasses: number[];\n  Culture: string;\n  Currency: string;\n}\n\n// ===== ROUND TRIP REQUEST =====\n\nexport interface RoundTripRequest {\n  ProductType: number;\n  ServiceTypes: string[];\n  DepartureLocations: Location[];\n  ArrivalLocations: Location[];\n  CheckIn: string;\n  Night: number;\n  Passengers: Passenger[];\n  acceptPendingProviders: boolean;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  supportedFlightReponseListTypes: number[];\n  showOnlyNonStopFlight: boolean;\n  additionalParameters?: AdditionalParameters;\n  calculateFlightFees: boolean;\n  Culture: string;\n  Currency: string;\n}\n\n// ===== MULTI-CITY REQUEST =====\n\nexport interface MulticityRequest {\n  serviceTypes: string[];\n  productType: number;\n  arrivalLocations: Location[];\n  departureLocations: Location[];\n  passengers: Passenger[];\n  checkIns: string[];\n  calculateFlightFees: boolean;\n  acceptPendingProviders: boolean;\n  additionalParameters?: AdditionalParameters;\n  forceFlightBundlePackage: boolean;\n  disablePackageOfferTotalPrice: boolean;\n  showOnlyNonStopFlight: boolean;\n  supportedFlightReponseListTypes: number[];\n  culture: string;\n  currency: string;\n}\n\n// ===== TYPES D'ÉNUMÉRATION =====\n\nexport enum FlightSearchType {\n  ONE_WAY = 'oneWay',\n  ROUND_TRIP = 'roundTrip',\n  MULTI_CITY = 'multiCity'\n}\n\nexport enum PassengerType {\n  ADULT = 1,\n  CHILD = 2,\n  INFANT = 3\n}\n\nexport enum LocationType {\n  AIRPORT = 1,\n  CITY = 2,\n  COUNTRY = 3\n}\n\nexport enum FlightClass {\n  ECONOMY = 1,\n  PREMIUM_ECONOMY = 2,\n  BUSINESS = 3,\n  FIRST = 4\n}\n\n// ===== INTERFACE POUR LE FORMULAIRE =====\n\nexport interface FlightSearchForm {\n  searchType: FlightSearchType;\n  departureLocation: string;\n  arrivalLocation: string;\n  departureDate: string;\n  returnDate?: string;\n  passengers: {\n    adults: number;\n    children: number;\n    infants: number;\n  };\n  flightClass: FlightClass;\n  directFlightsOnly: boolean;\n  preferredAirline?: string;\n  culture: string;\n  currency: string;\n}\n\n// ===== INTERFACE POUR LES RÉSULTATS =====\n\nexport interface FlightSearchResponse {\n  header: {\n    requestId: string;\n    success: boolean;\n    messages: Array<{\n      id: number;\n      code: string;\n      messageType: number;\n      message: string;\n    }>;\n  };\n  body: any; // À définir selon la structure de réponse de votre API\n}\n"], "mappings": "AAAA;;;AAiGA;AAEA,WAAYA,gBAIX;AAJD,WAAYA,gBAAgB;EAC1BA,gBAAA,sBAAkB;EAClBA,gBAAA,4BAAwB;EACxBA,gBAAA,4BAAwB;AAC1B,CAAC,EAJWA,gBAAgB,KAAhBA,gBAAgB;AAM5B,WAAYC,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EAJWA,aAAa,KAAbA,aAAa;AAMzB,WAAYC,YAIX;AAJD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EAJWA,YAAY,KAAZA,YAAY;AAMxB,WAAYC,WAKX;AALD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,4CAAmB;EACnBA,WAAA,CAAAA,WAAA,8BAAY;EACZA,WAAA,CAAAA,WAAA,wBAAS;AACX,CAAC,EALWA,WAAW,KAAXA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}