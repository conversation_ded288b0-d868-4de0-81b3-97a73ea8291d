{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Subject, of } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"inputElement\"];\nfunction AutocompleteComponent_div_2__svg_path_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 14);\n  }\n}\nfunction AutocompleteComponent_div_2__svg_path_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 15);\n  }\n}\nfunction AutocompleteComponent_div_2__svg_path_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 16);\n  }\n}\nfunction AutocompleteComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 9);\n    i0.ɵɵelementContainerStart(2, 10);\n    i0.ɵɵtemplate(3, AutocompleteComponent_div_2__svg_path_3_Template, 1, 0, \"path\", 11);\n    i0.ɵɵtemplate(4, AutocompleteComponent_div_2__svg_path_4_Template, 1, 0, \"path\", 12);\n    i0.ɵɵtemplate(5, AutocompleteComponent_div_2__svg_path_5_Template, 1, 0, \"path\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r0.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"location\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngSwitchCase\", \"plane\");\n  }\n}\nfunction AutocompleteComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function AutocompleteComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.clearInput());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 9);\n    i0.ɵɵelement(2, \"path\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutocompleteComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 20);\n    i0.ɵɵelement(2, \"circle\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutocompleteComponent_div_7_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function AutocompleteComponent_div_7_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const suggestion_r12 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.selectItem(suggestion_r12));\n    })(\"mouseenter\", function AutocompleteComponent_div_7_div_2_Template_div_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const i_r13 = restoredCtx.index;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.selectedIndex = i_r13);\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 28)(3, \"span\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const suggestion_r12 = ctx.$implicit;\n    const i_r13 = ctx.index;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", i_r13 === ctx_r10.selectedIndex);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(suggestion_r12.code);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r10.highlightMatch(suggestion_r12.name, ctx_r10.value), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r10.highlightMatch(suggestion_r12.displayName, ctx_r10.value), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction AutocompleteComponent_div_7_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 9);\n    i0.ɵɵelement(3, \"path\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Aucun r\\u00E9sultat trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AutocompleteComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵtemplate(2, AutocompleteComponent_div_7_div_2_Template, 10, 5, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AutocompleteComponent_div_7_div_3_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.suggestions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.suggestions.length === 0 && !ctx_r4.loading && ctx_r4.value.length >= 2);\n  }\n}\nexport class AutocompleteComponent {\n  constructor() {\n    this.placeholder = '';\n    this.value = '';\n    this.disabled = false;\n    this.required = false;\n    this.icon = '';\n    this.valueChange = new EventEmitter();\n    this.itemSelected = new EventEmitter();\n    this.searchTerm$ = new Subject();\n    this.suggestions = [];\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.loading = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.setupSearch();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setupSearch() {\n    this.searchTerm$.pipe(debounceTime(300), distinctUntilChanged(), switchMap(term => {\n      if (!term || term.length < 2) {\n        return of([]);\n      }\n      this.loading = true;\n      return this.searchFunction(term);\n    }), takeUntil(this.destroy$)).subscribe({\n      next: results => {\n        this.suggestions = results;\n        this.showSuggestions = results.length > 0;\n        this.selectedIndex = -1;\n        this.loading = false;\n      },\n      error: () => {\n        this.loading = false;\n        this.suggestions = [];\n        this.showSuggestions = false;\n      }\n    });\n  }\n  onInput(event) {\n    const target = event.target;\n    const value = target.value;\n    this.value = value;\n    this.valueChange.emit(value);\n    this.searchTerm$.next(value);\n  }\n  onFocus() {\n    if (this.suggestions.length > 0) {\n      this.showSuggestions = true;\n    }\n  }\n  onBlur() {\n    // Délai pour permettre le clic sur une suggestion\n    setTimeout(() => {\n      this.showSuggestions = false;\n      this.selectedIndex = -1;\n    }, 200);\n  }\n  onKeyDown(event) {\n    if (!this.showSuggestions) return;\n    switch (event.key) {\n      case 'ArrowDown':\n        event.preventDefault();\n        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);\n        break;\n      case 'Enter':\n        event.preventDefault();\n        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {\n          this.selectItem(this.suggestions[this.selectedIndex]);\n        }\n        break;\n      case 'Escape':\n        this.showSuggestions = false;\n        this.selectedIndex = -1;\n        break;\n    }\n  }\n  selectItem(item) {\n    this.value = item.displayName;\n    this.valueChange.emit(this.value);\n    this.itemSelected.emit(item);\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.inputElement.nativeElement.value = item.displayName;\n  }\n  clearInput() {\n    this.value = '';\n    this.valueChange.emit('');\n    this.suggestions = [];\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.inputElement.nativeElement.value = '';\n    this.inputElement.nativeElement.focus();\n  }\n  highlightMatch(text, query) {\n    if (!query) return text;\n    const regex = new RegExp(`(${query})`, 'gi');\n    return text.replace(regex, '<mark>$1</mark>');\n  }\n  static {\n    this.ɵfac = function AutocompleteComponent_Factory(t) {\n      return new (t || AutocompleteComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AutocompleteComponent,\n      selectors: [[\"app-autocomplete\"]],\n      viewQuery: function AutocompleteComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      inputs: {\n        placeholder: \"placeholder\",\n        value: \"value\",\n        searchFunction: \"searchFunction\",\n        disabled: \"disabled\",\n        required: \"required\",\n        icon: \"icon\"\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        itemSelected: \"itemSelected\"\n      },\n      decls: 8,\n      vars: 8,\n      consts: [[1, \"autocomplete-container\"], [1, \"input-wrapper\"], [\"class\", \"input-icon\", 4, \"ngIf\"], [\"type\", \"text\", \"autocomplete\", \"off\", \"spellcheck\", \"false\", 1, \"autocomplete-input\", 3, \"placeholder\", \"value\", \"disabled\", \"required\", \"input\", \"focus\", \"blur\", \"keydown\"], [\"inputElement\", \"\"], [\"type\", \"button\", \"class\", \"clear-button\", \"tabindex\", \"-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"loading-indicator\", 4, \"ngIf\"], [\"class\", \"suggestions-container\", 4, \"ngIf\"], [1, \"input-icon\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [3, \"ngSwitch\"], [\"d\", \"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\", 4, \"ngSwitchCase\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\", 4, \"ngSwitchCase\"], [\"d\", \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\", 4, \"ngSwitchDefault\"], [\"d\", \"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"], [\"d\", \"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"], [\"type\", \"button\", \"tabindex\", \"-1\", 1, \"clear-button\", 3, \"click\"], [\"d\", \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"], [1, \"loading-indicator\"], [\"viewBox\", \"0 0 24 24\", 1, \"spinner\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"fill\", \"none\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [1, \"suggestions-container\"], [1, \"suggestions-list\"], [\"class\", \"suggestion-item\", 3, \"selected\", \"click\", \"mouseenter\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"suggestion-item\", 3, \"click\", \"mouseenter\"], [1, \"suggestion-content\"], [1, \"suggestion-main\"], [1, \"suggestion-code\"], [1, \"suggestion-name\", 3, \"innerHTML\"], [1, \"suggestion-display\", 3, \"innerHTML\"], [1, \"suggestion-icon\"], [\"d\", \"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z\"], [1, \"no-results\"], [1, \"no-results-icon\"]],\n      template: function AutocompleteComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, AutocompleteComponent_div_2_Template, 6, 3, \"div\", 2);\n          i0.ɵɵelementStart(3, \"input\", 3, 4);\n          i0.ɵɵlistener(\"input\", function AutocompleteComponent_Template_input_input_3_listener($event) {\n            return ctx.onInput($event);\n          })(\"focus\", function AutocompleteComponent_Template_input_focus_3_listener() {\n            return ctx.onFocus();\n          })(\"blur\", function AutocompleteComponent_Template_input_blur_3_listener() {\n            return ctx.onBlur();\n          })(\"keydown\", function AutocompleteComponent_Template_input_keydown_3_listener($event) {\n            return ctx.onKeyDown($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, AutocompleteComponent_button_5_Template, 3, 0, \"button\", 5);\n          i0.ɵɵtemplate(6, AutocompleteComponent_div_6_Template, 3, 0, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, AutocompleteComponent_div_7_Template, 4, 2, \"div\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.icon);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"placeholder\", ctx.placeholder)(\"value\", ctx.value)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.value && !ctx.disabled);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showSuggestions);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i1.NgSwitch, i1.NgSwitchCase, i1.NgSwitchDefault],\n      styles: [\".autocomplete-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.autocomplete-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 1.25rem 1.25rem 1.25rem 3.5rem;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 16px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  background: white;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  outline: none;\\n}\\n\\n.autocomplete-input[_ngcontent-%COMP%]:focus {\\n  border-color: #2c5aa0;\\n  box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);\\n  transform: translateY(-2px);\\n}\\n\\n.autocomplete-input[_ngcontent-%COMP%]:disabled {\\n  background-color: #f7fafc;\\n  color: #a0aec0;\\n  cursor: not-allowed;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1.25rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 20px;\\n  height: 20px;\\n  color: #718096;\\n  z-index: 2;\\n}\\n\\n.input-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.clear-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 24px;\\n  height: 24px;\\n  border: none;\\n  background: transparent;\\n  color: #a0aec0;\\n  cursor: pointer;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n  z-index: 2;\\n}\\n\\n.clear-button[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  color: #718096;\\n}\\n\\n.clear-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.loading-indicator[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  width: 20px;\\n  height: 20px;\\n  color: #2c5aa0;\\n  z-index: 2;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from { transform: rotate(0deg); }\\n  to { transform: rotate(360deg); }\\n}\\n\\n.suggestions-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  left: 0;\\n  right: 0;\\n  z-index: 1000;\\n  margin-top: 4px;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: \\n    0 20px 25px -5px rgba(0, 0, 0, 0.1),\\n    0 10px 10px -5px rgba(0, 0, 0, 0.04);\\n  border: 1px solid #e2e8f0;\\n  overflow: hidden;\\n  max-height: 300px;\\n  overflow-y: auto;\\n}\\n\\n.suggestions-list[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1rem 1.5rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border-bottom: 1px solid #f7fafc;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover, .suggestion-item.selected[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\\n  border-left: 4px solid #2c5aa0;\\n  padding-left: calc(1.5rem - 4px);\\n}\\n\\n.suggestion-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.suggestion-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n}\\n\\n.suggestion-code[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);\\n  color: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 8px;\\n  font-size: 0.75rem;\\n  font-weight: 700;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  min-width: 40px;\\n  text-align: center;\\n}\\n\\n.suggestion-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.95rem;\\n}\\n\\n.suggestion-display[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #718096;\\n  font-weight: 400;\\n}\\n\\n.suggestion-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: #cbd5e0;\\n  transition: all 0.2s ease;\\n}\\n\\n.suggestion-item[_ngcontent-%COMP%]:hover   .suggestion-icon[_ngcontent-%COMP%], .suggestion-item.selected[_ngcontent-%COMP%]   .suggestion-icon[_ngcontent-%COMP%] {\\n  color: #2c5aa0;\\n  transform: translateX(2px);\\n}\\n\\n.suggestion-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.75rem;\\n  padding: 2rem 1.5rem;\\n  color: #a0aec0;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.no-results-icon[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  opacity: 0.6;\\n}\\n\\n.no-results-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n\\n\\n[_ngcontent-%COMP%]:global(.autocomplete-container   mark)[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);\\n  color: #c05621;\\n  padding: 0.125rem 0.25rem;\\n  border-radius: 4px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.suggestions-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.suggestions-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f7fafc;\\n  border-radius: 3px;\\n}\\n\\n.suggestions-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e0;\\n  border-radius: 3px;\\n}\\n\\n.suggestions-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #a0aec0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .autocomplete-input[_ngcontent-%COMP%] {\\n    padding: 1rem 1rem 1rem 3rem;\\n    font-size: 0.95rem;\\n  }\\n  \\n  .input-icon[_ngcontent-%COMP%] {\\n    left: 1rem;\\n    width: 18px;\\n    height: 18px;\\n  }\\n  \\n  .suggestion-item[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1rem;\\n  }\\n  \\n  .suggestion-main[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  \\n  .suggestion-code[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.2rem 0.5rem;\\n    min-width: 35px;\\n  }\\n  \\n  .suggestion-name[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  \\n  .suggestion-display[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n\\n\\n\\n.suggestions-container[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n\\n\\n.autocomplete-input.error[_ngcontent-%COMP%] {\\n  border-color: #e53e3e;\\n  box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.1);\\n}\\n\\n.autocomplete-input.success[_ngcontent-%COMP%] {\\n  border-color: #38a169;\\n  box-shadow: 0 0 0 4px rgba(56, 161, 105, 0.1);\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Subject", "of", "debounceTime", "distinctUntilChanged", "switchMap", "takeUntil", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelementContainerStart", "ɵɵtemplate", "AutocompleteComponent_div_2__svg_path_3_Template", "AutocompleteComponent_div_2__svg_path_4_Template", "AutocompleteComponent_div_2__svg_path_5_Template", "ɵɵelementContainerEnd", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "icon", "ɵɵlistener", "AutocompleteComponent_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "clearInput", "AutocompleteComponent_div_7_div_2_Template_div_click_0_listener", "restoredCtx", "_r15", "suggestion_r12", "$implicit", "ctx_r14", "selectItem", "AutocompleteComponent_div_7_div_2_Template_div_mouseenter_0_listener", "i_r13", "index", "ctx_r16", "selectedIndex", "ɵɵtext", "ɵɵclassProp", "ctx_r10", "ɵɵtextInterpolate", "code", "highlightMatch", "name", "value", "ɵɵsanitizeHtml", "displayName", "ɵɵnamespaceHTML", "AutocompleteComponent_div_7_div_2_Template", "AutocompleteComponent_div_7_div_3_Template", "ctx_r4", "suggestions", "length", "loading", "AutocompleteComponent", "constructor", "placeholder", "disabled", "required", "valueChange", "itemSelected", "searchTerm$", "showSuggestions", "destroy$", "ngOnInit", "setupSearch", "ngOnDestroy", "next", "complete", "pipe", "term", "searchFunction", "subscribe", "results", "error", "onInput", "event", "target", "emit", "onFocus", "onBlur", "setTimeout", "onKeyDown", "key", "preventDefault", "Math", "min", "max", "item", "inputElement", "nativeElement", "focus", "text", "query", "regex", "RegExp", "replace", "selectors", "viewQuery", "AutocompleteComponent_Query", "rf", "ctx", "AutocompleteComponent_div_2_Template", "AutocompleteComponent_Template_input_input_3_listener", "$event", "AutocompleteComponent_Template_input_focus_3_listener", "AutocompleteComponent_Template_input_blur_3_listener", "AutocompleteComponent_Template_input_keydown_3_listener", "AutocompleteComponent_button_5_Template", "AutocompleteComponent_div_6_Template", "AutocompleteComponent_div_7_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\autocomplete\\autocomplete.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\autocomplete\\autocomplete.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild } from '@angular/core';\nimport { Subject, Observable, of } from 'rxjs';\nimport { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';\n\nexport interface AutocompleteItem {\n  code: string;\n  name: string;\n  displayName: string;\n}\n\n@Component({\n  selector: 'app-autocomplete',\n  templateUrl: './autocomplete.component.html',\n  styleUrls: ['./autocomplete.component.css']\n})\nexport class AutocompleteComponent implements OnInit, OnDestroy {\n  @Input() placeholder: string = '';\n  @Input() value: string = '';\n  @Input() searchFunction!: (query: string) => Observable<AutocompleteItem[]>;\n  @Input() disabled: boolean = false;\n  @Input() required: boolean = false;\n  @Input() icon: string = '';\n  \n  @Output() valueChange = new EventEmitter<string>();\n  @Output() itemSelected = new EventEmitter<AutocompleteItem>();\n  \n  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;\n\n  searchTerm$ = new Subject<string>();\n  suggestions: AutocompleteItem[] = [];\n  showSuggestions = false;\n  selectedIndex = -1;\n  loading = false;\n  private destroy$ = new Subject<void>();\n\n  ngOnInit(): void {\n    this.setupSearch();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  private setupSearch(): void {\n    this.searchTerm$.pipe(\n      debounceTime(300),\n      distinctUntilChanged(),\n      switchMap(term => {\n        if (!term || term.length < 2) {\n          return of([]);\n        }\n        this.loading = true;\n        return this.searchFunction(term);\n      }),\n      takeUntil(this.destroy$)\n    ).subscribe({\n      next: (results) => {\n        this.suggestions = results;\n        this.showSuggestions = results.length > 0;\n        this.selectedIndex = -1;\n        this.loading = false;\n      },\n      error: () => {\n        this.loading = false;\n        this.suggestions = [];\n        this.showSuggestions = false;\n      }\n    });\n  }\n\n  onInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    const value = target.value;\n    this.value = value;\n    this.valueChange.emit(value);\n    this.searchTerm$.next(value);\n  }\n\n  onFocus(): void {\n    if (this.suggestions.length > 0) {\n      this.showSuggestions = true;\n    }\n  }\n\n  onBlur(): void {\n    // Délai pour permettre le clic sur une suggestion\n    setTimeout(() => {\n      this.showSuggestions = false;\n      this.selectedIndex = -1;\n    }, 200);\n  }\n\n  onKeyDown(event: KeyboardEvent): void {\n    if (!this.showSuggestions) return;\n\n    switch (event.key) {\n      case 'ArrowDown':\n        event.preventDefault();\n        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);\n        break;\n      \n      case 'ArrowUp':\n        event.preventDefault();\n        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);\n        break;\n      \n      case 'Enter':\n        event.preventDefault();\n        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {\n          this.selectItem(this.suggestions[this.selectedIndex]);\n        }\n        break;\n      \n      case 'Escape':\n        this.showSuggestions = false;\n        this.selectedIndex = -1;\n        break;\n    }\n  }\n\n  selectItem(item: AutocompleteItem): void {\n    this.value = item.displayName;\n    this.valueChange.emit(this.value);\n    this.itemSelected.emit(item);\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.inputElement.nativeElement.value = item.displayName;\n  }\n\n  clearInput(): void {\n    this.value = '';\n    this.valueChange.emit('');\n    this.suggestions = [];\n    this.showSuggestions = false;\n    this.selectedIndex = -1;\n    this.inputElement.nativeElement.value = '';\n    this.inputElement.nativeElement.focus();\n  }\n\n  highlightMatch(text: string, query: string): string {\n    if (!query) return text;\n    \n    const regex = new RegExp(`(${query})`, 'gi');\n    return text.replace(regex, '<mark>$1</mark>');\n  }\n}\n", "<div class=\"autocomplete-container\">\n  <div class=\"input-wrapper\">\n    <!-- I<PERSON>ône -->\n    <div class=\"input-icon\" *ngIf=\"icon\">\n      <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n        <ng-container [ngSwitch]=\"icon\">\n          <!-- Icône d'aéroport/localisation -->\n          <path *ngSwitchCase=\"'location'\" d=\"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z\"/>\n          \n          <!-- Icône d'avion -->\n          <path *ngSwitchCase=\"'plane'\" d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n          \n          <!-- Ic<PERSON> de recherche par défaut -->\n          <path *ngSwitchDefault d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n        </ng-container>\n      </svg>\n    </div>\n\n    <!-- Champ de saisie -->\n    <input \n      #inputElement\n      type=\"text\"\n      [placeholder]=\"placeholder\"\n      [value]=\"value\"\n      [disabled]=\"disabled\"\n      [required]=\"required\"\n      (input)=\"onInput($event)\"\n      (focus)=\"onFocus()\"\n      (blur)=\"onBlur()\"\n      (keydown)=\"onKeyDown($event)\"\n      class=\"autocomplete-input\"\n      autocomplete=\"off\"\n      spellcheck=\"false\">\n\n    <!-- Bouton de suppression -->\n    <button \n      type=\"button\"\n      class=\"clear-button\"\n      *ngIf=\"value && !disabled\"\n      (click)=\"clearInput()\"\n      tabindex=\"-1\">\n      <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n        <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n      </svg>\n    </button>\n\n    <!-- Indicateur de chargement -->\n    <div class=\"loading-indicator\" *ngIf=\"loading\">\n      <svg class=\"spinner\" viewBox=\"0 0 24 24\">\n        <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"/>\n      </svg>\n    </div>\n  </div>\n\n  <!-- Liste des suggestions -->\n  <div class=\"suggestions-container\" *ngIf=\"showSuggestions\">\n    <div class=\"suggestions-list\">\n      <div \n        *ngFor=\"let suggestion of suggestions; let i = index\"\n        class=\"suggestion-item\"\n        [class.selected]=\"i === selectedIndex\"\n        (click)=\"selectItem(suggestion)\"\n        (mouseenter)=\"selectedIndex = i\">\n        \n        <div class=\"suggestion-content\">\n          <div class=\"suggestion-main\">\n            <span class=\"suggestion-code\">{{ suggestion.code }}</span>\n            <span class=\"suggestion-name\" [innerHTML]=\"highlightMatch(suggestion.name, value)\"></span>\n          </div>\n          <div class=\"suggestion-display\" [innerHTML]=\"highlightMatch(suggestion.displayName, value)\"></div>\n        </div>\n        \n        <div class=\"suggestion-icon\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z\"/>\n          </svg>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Message si aucun résultat -->\n    <div class=\"no-results\" *ngIf=\"suggestions.length === 0 && !loading && value.length >= 2\">\n      <div class=\"no-results-icon\">\n        <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n          <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n        </svg>\n      </div>\n      <span>Aucun résultat trouvé</span>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAkD,eAAe;AAChH,SAASC,OAAO,EAAcC,EAAE,QAAQ,MAAM;AAC9C,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;;;;;;;ICK/EC,EAAA,CAAAC,SAAA,eAAiM;;;;;;IAGjMD,EAAA,CAAAC,SAAA,eAA0J;;;;;;IAG1JD,EAAA,CAAAC,SAAA,eAAuQ;;;;;IAV7QD,EAAA,CAAAE,cAAA,aAAqC;IACnCF,EAAA,CAAAG,cAAA,EAA6C;IAA7CH,EAAA,CAAAE,cAAA,aAA6C;IAC3CF,EAAA,CAAAI,uBAAA,OAAgC;IAE9BJ,EAAA,CAAAK,UAAA,IAAAC,gDAAA,mBAAiM;IAGjMN,EAAA,CAAAK,UAAA,IAAAE,gDAAA,mBAA0J;IAG1JP,EAAA,CAAAK,UAAA,IAAAG,gDAAA,mBAAuQ;IACzQR,EAAA,CAAAS,qBAAA,EAAe;IACjBT,EAAA,CAAAU,YAAA,EAAM;;;;IAVUV,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAY,UAAA,aAAAC,MAAA,CAAAC,IAAA,CAAiB;IAEtBd,EAAA,CAAAW,SAAA,GAAwB;IAAxBX,EAAA,CAAAY,UAAA,4BAAwB;IAGxBZ,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAY,UAAA,yBAAqB;;;;;;IAyBlCZ,EAAA,CAAAE,cAAA,iBAKgB;IADdF,EAAA,CAAAe,UAAA,mBAAAC,gEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAEtBtB,EAAA,CAAAG,cAAA,EAA6C;IAA7CH,EAAA,CAAAE,cAAA,aAA6C;IAC3CF,EAAA,CAAAC,SAAA,eAAiH;IACnHD,EAAA,CAAAU,YAAA,EAAM;;;;;IAIRV,EAAA,CAAAE,cAAA,cAA+C;IAC7CF,EAAA,CAAAG,cAAA,EAAyC;IAAzCH,EAAA,CAAAE,cAAA,cAAyC;IACvCF,EAAA,CAAAC,SAAA,iBAAmF;IACrFD,EAAA,CAAAU,YAAA,EAAM;;;;;;IAONV,EAAA,CAAAE,cAAA,cAKmC;IADjCF,EAAA,CAAAe,UAAA,mBAAAQ,gEAAA;MAAA,MAAAC,WAAA,GAAAxB,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAC,cAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAO,OAAA,CAAAC,UAAA,CAAAH,cAAA,CAAsB;IAAA,EAAC,wBAAAI,qEAAA;MAAA,MAAAN,WAAA,GAAAxB,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAM,KAAA,GAAAP,WAAA,CAAAQ,KAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAAY,OAAA,CAAAC,aAAA,GAAAH,KAAA;IAAA;IAGhC/B,EAAA,CAAAE,cAAA,cAAgC;IAEEF,EAAA,CAAAmC,MAAA,GAAqB;IAAAnC,EAAA,CAAAU,YAAA,EAAO;IAC1DV,EAAA,CAAAC,SAAA,eAA0F;IAC5FD,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,SAAA,cAAkG;IACpGD,EAAA,CAAAU,YAAA,EAAM;IAENV,EAAA,CAAAE,cAAA,cAA6B;IAC3BF,EAAA,CAAAG,cAAA,EAA6C;IAA7CH,EAAA,CAAAE,cAAA,aAA6C;IAC3CF,EAAA,CAAAC,SAAA,eAAkE;IACpED,EAAA,CAAAU,YAAA,EAAM;;;;;;IAfRV,EAAA,CAAAoC,WAAA,aAAAL,KAAA,KAAAM,OAAA,CAAAH,aAAA,CAAsC;IAMJlC,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAsC,iBAAA,CAAAZ,cAAA,CAAAa,IAAA,CAAqB;IACrBvC,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAY,UAAA,cAAAyB,OAAA,CAAAG,cAAA,CAAAd,cAAA,CAAAe,IAAA,EAAAJ,OAAA,CAAAK,KAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAAoD;IAEpD3C,EAAA,CAAAW,SAAA,GAA2D;IAA3DX,EAAA,CAAAY,UAAA,cAAAyB,OAAA,CAAAG,cAAA,CAAAd,cAAA,CAAAkB,WAAA,EAAAP,OAAA,CAAAK,KAAA,GAAA1C,EAAA,CAAA2C,cAAA,CAA2D;;;;;IAYjG3C,EAAA,CAAAE,cAAA,cAA0F;IAEtFF,EAAA,CAAAG,cAAA,EAA6C;IAA7CH,EAAA,CAAAE,cAAA,aAA6C;IAC3CF,EAAA,CAAAC,SAAA,eAAsP;IACxPD,EAAA,CAAAU,YAAA,EAAM;IAERV,EAAA,CAAA6C,eAAA,EAAM;IAAN7C,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAmC,MAAA,sCAAqB;IAAAnC,EAAA,CAAAU,YAAA,EAAO;;;;;IAhCtCV,EAAA,CAAAE,cAAA,cAA2D;IAEvDF,EAAA,CAAAK,UAAA,IAAAyC,0CAAA,mBAoBM;IACR9C,EAAA,CAAAU,YAAA,EAAM;IAGNV,EAAA,CAAAK,UAAA,IAAA0C,0CAAA,kBAOM;IACR/C,EAAA,CAAAU,YAAA,EAAM;;;;IA/BuBV,EAAA,CAAAW,SAAA,GAAgB;IAAhBX,EAAA,CAAAY,UAAA,YAAAoC,MAAA,CAAAC,WAAA,CAAgB;IAuBlBjD,EAAA,CAAAW,SAAA,GAA+D;IAA/DX,EAAA,CAAAY,UAAA,SAAAoC,MAAA,CAAAC,WAAA,CAAAC,MAAA,WAAAF,MAAA,CAAAG,OAAA,IAAAH,MAAA,CAAAN,KAAA,CAAAQ,MAAA,MAA+D;;;ADlE5F,OAAM,MAAOE,qBAAqB;EALlCC,YAAA;IAMW,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAZ,KAAK,GAAW,EAAE;IAElB,KAAAa,QAAQ,GAAY,KAAK;IACzB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAA1C,IAAI,GAAW,EAAE;IAEhB,KAAA2C,WAAW,GAAG,IAAIhE,YAAY,EAAU;IACxC,KAAAiE,YAAY,GAAG,IAAIjE,YAAY,EAAoB;IAI7D,KAAAkE,WAAW,GAAG,IAAIjE,OAAO,EAAU;IACnC,KAAAuD,WAAW,GAAuB,EAAE;IACpC,KAAAW,eAAe,GAAG,KAAK;IACvB,KAAA1B,aAAa,GAAG,CAAC,CAAC;IAClB,KAAAiB,OAAO,GAAG,KAAK;IACP,KAAAU,QAAQ,GAAG,IAAInE,OAAO,EAAQ;;EAEtCoE,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,QAAQ,CAACI,IAAI,EAAE;IACpB,IAAI,CAACJ,QAAQ,CAACK,QAAQ,EAAE;EAC1B;EAEQH,WAAWA,CAAA;IACjB,IAAI,CAACJ,WAAW,CAACQ,IAAI,CACnBvE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBC,SAAS,CAACsE,IAAI,IAAG;MACf,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAClB,MAAM,GAAG,CAAC,EAAE;QAC5B,OAAOvD,EAAE,CAAC,EAAE,CAAC;;MAEf,IAAI,CAACwD,OAAO,GAAG,IAAI;MACnB,OAAO,IAAI,CAACkB,cAAc,CAACD,IAAI,CAAC;IAClC,CAAC,CAAC,EACFrE,SAAS,CAAC,IAAI,CAAC8D,QAAQ,CAAC,CACzB,CAACS,SAAS,CAAC;MACVL,IAAI,EAAGM,OAAO,IAAI;QAChB,IAAI,CAACtB,WAAW,GAAGsB,OAAO;QAC1B,IAAI,CAACX,eAAe,GAAGW,OAAO,CAACrB,MAAM,GAAG,CAAC;QACzC,IAAI,CAAChB,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAACiB,OAAO,GAAG,KAAK;MACtB,CAAC;MACDqB,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACrB,OAAO,GAAG,KAAK;QACpB,IAAI,CAACF,WAAW,GAAG,EAAE;QACrB,IAAI,CAACW,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAa,OAAOA,CAACC,KAAY;IAClB,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAA0B;IAC/C,MAAMjC,KAAK,GAAGiC,MAAM,CAACjC,KAAK;IAC1B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACe,WAAW,CAACmB,IAAI,CAAClC,KAAK,CAAC;IAC5B,IAAI,CAACiB,WAAW,CAACM,IAAI,CAACvB,KAAK,CAAC;EAC9B;EAEAmC,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC5B,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACU,eAAe,GAAG,IAAI;;EAE/B;EAEAkB,MAAMA,CAAA;IACJ;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACnB,eAAe,GAAG,KAAK;MAC5B,IAAI,CAAC1B,aAAa,GAAG,CAAC,CAAC;IACzB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA8C,SAASA,CAACN,KAAoB;IAC5B,IAAI,CAAC,IAAI,CAACd,eAAe,EAAE;IAE3B,QAAQc,KAAK,CAACO,GAAG;MACf,KAAK,WAAW;QACdP,KAAK,CAACQ,cAAc,EAAE;QACtB,IAAI,CAAChD,aAAa,GAAGiD,IAAI,CAACC,GAAG,CAAC,IAAI,CAAClD,aAAa,GAAG,CAAC,EAAE,IAAI,CAACe,WAAW,CAACC,MAAM,GAAG,CAAC,CAAC;QAClF;MAEF,KAAK,SAAS;QACZwB,KAAK,CAACQ,cAAc,EAAE;QACtB,IAAI,CAAChD,aAAa,GAAGiD,IAAI,CAACE,GAAG,CAAC,IAAI,CAACnD,aAAa,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD;MAEF,KAAK,OAAO;QACVwC,KAAK,CAACQ,cAAc,EAAE;QACtB,IAAI,IAAI,CAAChD,aAAa,IAAI,CAAC,IAAI,IAAI,CAACA,aAAa,GAAG,IAAI,CAACe,WAAW,CAACC,MAAM,EAAE;UAC3E,IAAI,CAACrB,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,IAAI,CAACf,aAAa,CAAC,CAAC;;QAEvD;MAEF,KAAK,QAAQ;QACX,IAAI,CAAC0B,eAAe,GAAG,KAAK;QAC5B,IAAI,CAAC1B,aAAa,GAAG,CAAC,CAAC;QACvB;;EAEN;EAEAL,UAAUA,CAACyD,IAAsB;IAC/B,IAAI,CAAC5C,KAAK,GAAG4C,IAAI,CAAC1C,WAAW;IAC7B,IAAI,CAACa,WAAW,CAACmB,IAAI,CAAC,IAAI,CAAClC,KAAK,CAAC;IACjC,IAAI,CAACgB,YAAY,CAACkB,IAAI,CAACU,IAAI,CAAC;IAC5B,IAAI,CAAC1B,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC1B,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACqD,YAAY,CAACC,aAAa,CAAC9C,KAAK,GAAG4C,IAAI,CAAC1C,WAAW;EAC1D;EAEAtB,UAAUA,CAAA;IACR,IAAI,CAACoB,KAAK,GAAG,EAAE;IACf,IAAI,CAACe,WAAW,CAACmB,IAAI,CAAC,EAAE,CAAC;IACzB,IAAI,CAAC3B,WAAW,GAAG,EAAE;IACrB,IAAI,CAACW,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC1B,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACqD,YAAY,CAACC,aAAa,CAAC9C,KAAK,GAAG,EAAE;IAC1C,IAAI,CAAC6C,YAAY,CAACC,aAAa,CAACC,KAAK,EAAE;EACzC;EAEAjD,cAAcA,CAACkD,IAAY,EAAEC,KAAa;IACxC,IAAI,CAACA,KAAK,EAAE,OAAOD,IAAI;IAEvB,MAAME,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,KAAK,GAAG,EAAE,IAAI,CAAC;IAC5C,OAAOD,IAAI,CAACI,OAAO,CAACF,KAAK,EAAE,iBAAiB,CAAC;EAC/C;;;uBAlIWxC,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAA2C,SAAA;MAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;UCflClG,EAAA,CAAAE,cAAA,aAAoC;UAGhCF,EAAA,CAAAK,UAAA,IAAA+F,oCAAA,iBAaM;UAGNpG,EAAA,CAAAE,cAAA,kBAaqB;UANnBF,EAAA,CAAAe,UAAA,mBAAAsF,sDAAAC,MAAA;YAAA,OAASH,GAAA,CAAA1B,OAAA,CAAA6B,MAAA,CAAe;UAAA,EAAC,mBAAAC,sDAAA;YAAA,OAChBJ,GAAA,CAAAtB,OAAA,EAAS;UAAA,EADO,kBAAA2B,qDAAA;YAAA,OAEjBL,GAAA,CAAArB,MAAA,EAAQ;UAAA,EAFS,qBAAA2B,wDAAAH,MAAA;YAAA,OAGdH,GAAA,CAAAnB,SAAA,CAAAsB,MAAA,CAAiB;UAAA,EAHH;UAP3BtG,EAAA,CAAAU,YAAA,EAaqB;UAGrBV,EAAA,CAAAK,UAAA,IAAAqG,uCAAA,oBASS;UAGT1G,EAAA,CAAAK,UAAA,IAAAsG,oCAAA,iBAIM;UACR3G,EAAA,CAAAU,YAAA,EAAM;UAGNV,EAAA,CAAAK,UAAA,IAAAuG,oCAAA,iBAkCM;UACR5G,EAAA,CAAAU,YAAA,EAAM;;;UAvFuBV,EAAA,CAAAW,SAAA,GAAU;UAAVX,EAAA,CAAAY,UAAA,SAAAuF,GAAA,CAAArF,IAAA,CAAU;UAmBjCd,EAAA,CAAAW,SAAA,GAA2B;UAA3BX,EAAA,CAAAY,UAAA,gBAAAuF,GAAA,CAAA7C,WAAA,CAA2B,UAAA6C,GAAA,CAAAzD,KAAA,cAAAyD,GAAA,CAAA5C,QAAA,cAAA4C,GAAA,CAAA3C,QAAA;UAgB1BxD,EAAA,CAAAW,SAAA,GAAwB;UAAxBX,EAAA,CAAAY,UAAA,SAAAuF,GAAA,CAAAzD,KAAA,KAAAyD,GAAA,CAAA5C,QAAA,CAAwB;UASKvD,EAAA,CAAAW,SAAA,GAAa;UAAbX,EAAA,CAAAY,UAAA,SAAAuF,GAAA,CAAAhD,OAAA,CAAa;UAQXnD,EAAA,CAAAW,SAAA,GAAqB;UAArBX,EAAA,CAAAY,UAAA,SAAAuF,GAAA,CAAAvC,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}