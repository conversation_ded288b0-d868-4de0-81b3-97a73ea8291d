{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { FlightSearchType, PassengerType, LocationType } from '../models/flight-search-request.interface';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport class FlightSearchService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.baseUrl = environment.apiUrl;\n    this.flightSearchEndpoint = '/api/flights/search'; // Ajustez selon votre endpoint\n  }\n  /**\n   * Recherche de vols aller simple\n   * @param request Requête de recherche aller simple\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchOneWay(request) {\n    const headers = this.getHeaders();\n    return this.http.post(`${this.baseUrl}${this.flightSearchEndpoint}/oneway`, request, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Recherche de vols aller-retour\n   * @param request Requête de recherche aller-retour\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchRoundTrip(request) {\n    const headers = this.getHeaders();\n    return this.http.post(`${this.baseUrl}${this.flightSearchEndpoint}/roundtrip`, request, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Recherche de vols multi-destinations\n   * @param request Requête de recherche multi-destinations\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchMultiCity(request) {\n    const headers = this.getHeaders();\n    return this.http.post(`${this.baseUrl}${this.flightSearchEndpoint}/multicity`, request, {\n      headers\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Recherche de vols basée sur le formulaire utilisateur\n   * @param formData Données du formulaire de recherche\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchFlights(formData) {\n    switch (formData.searchType) {\n      case FlightSearchType.ONE_WAY:\n        return this.searchOneWay(this.buildOneWayRequest(formData));\n      case FlightSearchType.ROUND_TRIP:\n        return this.searchRoundTrip(this.buildRoundTripRequest(formData));\n      case FlightSearchType.MULTI_CITY:\n        return this.searchMultiCity(this.buildMultiCityRequest(formData));\n      default:\n        return throwError(() => new Error('Type de recherche non supporté'));\n    }\n  }\n  /**\n   * Construit une requête OneWay à partir des données du formulaire\n   */\n  buildOneWayRequest(formData) {\n    return {\n      ProductType: 2,\n      ServiceTypes: ['Flight'],\n      CheckIn: formData.departureDate,\n      DepartureLocations: [this.createLocation(formData.departureLocation)],\n      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      Passengers: this.buildPassengers(formData.passengers),\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      calculateFlightFees: true,\n      flightClasses: [formData.flightClass],\n      Culture: formData.culture,\n      Currency: formData.currency\n    };\n  }\n  /**\n   * Construit une requête RoundTrip à partir des données du formulaire\n   */\n  buildRoundTripRequest(formData) {\n    const nights = this.calculateNights(formData.departureDate, formData.returnDate);\n    return {\n      ProductType: 2,\n      ServiceTypes: ['Flight'],\n      DepartureLocations: [this.createLocation(formData.departureLocation)],\n      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      CheckIn: formData.departureDate,\n      Night: nights,\n      Passengers: this.buildPassengers(formData.passengers),\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      supportedFlightReponseListTypes: [1, 2],\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      calculateFlightFees: true,\n      Culture: formData.culture,\n      Currency: formData.currency\n    };\n  }\n  /**\n   * Construit une requête MultiCity à partir des données du formulaire\n   */\n  buildMultiCityRequest(formData) {\n    return {\n      serviceTypes: ['Flight'],\n      productType: 2,\n      arrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      departureLocations: [this.createLocation(formData.departureLocation)],\n      passengers: this.buildPassengers(formData.passengers),\n      checkIns: [formData.departureDate],\n      calculateFlightFees: true,\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      supportedFlightReponseListTypes: [1, 2],\n      culture: formData.culture,\n      currency: formData.currency\n    };\n  }\n  /**\n   * Crée un objet Location\n   */\n  createLocation(locationId) {\n    return {\n      id: locationId,\n      type: LocationType.AIRPORT,\n      provider: 1\n    };\n  }\n  /**\n   * Construit la liste des passagers\n   */\n  buildPassengers(passengers) {\n    const passengerList = [];\n    if (passengers.adults > 0) {\n      passengerList.push({\n        type: PassengerType.ADULT,\n        count: passengers.adults\n      });\n    }\n    if (passengers.children > 0) {\n      passengerList.push({\n        type: PassengerType.CHILD,\n        count: passengers.children\n      });\n    }\n    if (passengers.infants > 0) {\n      passengerList.push({\n        type: PassengerType.INFANT,\n        count: passengers.infants\n      });\n    }\n    return passengerList;\n  }\n  /**\n   * Calcule le nombre de nuits entre deux dates\n   */\n  calculateNights(checkIn, checkOut) {\n    const startDate = new Date(checkIn);\n    const endDate = new Date(checkOut);\n    const timeDiff = endDate.getTime() - startDate.getTime();\n    return Math.ceil(timeDiff / (1000 * 3600 * 24));\n  }\n  /**\n   * Obtient les headers pour les requêtes\n   */\n  getHeaders() {\n    return this.authService.getAuthHeaders();\n  }\n  /**\n   * Gestion des erreurs HTTP\n   */\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue lors de la recherche de vols';\n    if (error.error instanceof ErrorEvent) {\n      errorMessage = `Erreur: ${error.error.message}`;\n    } else {\n      switch (error.status) {\n        case 400:\n          errorMessage = 'Paramètres de recherche invalides';\n          break;\n        case 401:\n          errorMessage = 'Authentification requise';\n          break;\n        case 403:\n          errorMessage = 'Accès refusé';\n          break;\n        case 404:\n          errorMessage = 'Service de recherche non trouvé';\n          break;\n        case 500:\n          errorMessage = 'Erreur interne du serveur';\n          break;\n        case 0:\n          errorMessage = 'Impossible de contacter le serveur';\n          break;\n        default:\n          errorMessage = `Erreur ${error.status}: ${error.message}`;\n      }\n    }\n    console.error('Erreur de recherche de vols:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function FlightSearchService_Factory(t) {\n      return new (t || FlightSearchService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FlightSearchService,\n      factory: FlightSearchService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "catchError", "FlightSearchType", "PassengerType", "LocationType", "environment", "FlightSearchService", "constructor", "http", "authService", "baseUrl", "apiUrl", "flightSearchEndpoint", "searchOneWay", "request", "headers", "getHeaders", "post", "pipe", "handleError", "searchRoundTrip", "searchMultiCity", "searchFlights", "formData", "searchType", "ONE_WAY", "buildOneWayRequest", "ROUND_TRIP", "buildRoundTripRequest", "MULTI_CITY", "buildMultiCityRequest", "Error", "ProductType", "ServiceTypes", "CheckIn", "departureDate", "DepartureLocations", "createLocation", "departureLocation", "ArrivalLocations", "arrivalLocation", "Passengers", "buildPassengers", "passengers", "showOnlyNonStopFlight", "directFlightsOnly", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightClasses", "flightClass", "Culture", "culture", "<PERSON><PERSON><PERSON><PERSON>", "currency", "nights", "calculateNights", "returnDate", "Night", "supportedFlightReponseListTypes", "serviceTypes", "productType", "arrivalLocations", "departureLocations", "checkIns", "locationId", "id", "type", "AIRPORT", "provider", "passengerList", "adults", "push", "ADULT", "count", "children", "CHILD", "infants", "INFANT", "checkIn", "checkOut", "startDate", "Date", "endDate", "timeDiff", "getTime", "Math", "ceil", "getAuthHeaders", "error", "errorMessage", "ErrorEvent", "message", "status", "console", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\flight-search.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\n\nimport { \n  OneWayRequest, \n  RoundTripRequest, \n  MulticityRequest, \n  FlightSearchResponse,\n  FlightSearchForm,\n  FlightSearchType,\n  PassengerType,\n  LocationType,\n  FlightClass,\n  Location,\n  Passenger\n} from '../models/flight-search-request.interface';\nimport { environment } from '../../environments/environment';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class FlightSearchService {\n  private readonly baseUrl = environment.apiUrl;\n  private readonly flightSearchEndpoint = '/api/flights/search'; // Ajustez selon votre endpoint\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  /**\n   * Recherche de vols aller simple\n   * @param request Requête de recherche aller simple\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchOneWay(request: OneWayRequest): Observable<FlightSearchResponse> {\n    const headers = this.getHeaders();\n    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/oneway`, request, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  /**\n   * Recherche de vols aller-retour\n   * @param request Requête de recherche aller-retour\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchRoundTrip(request: RoundTripRequest): Observable<FlightSearchResponse> {\n    const headers = this.getHeaders();\n    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/roundtrip`, request, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  /**\n   * Recherche de vols multi-destinations\n   * @param request Requête de recherche multi-destinations\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchMultiCity(request: MulticityRequest): Observable<FlightSearchResponse> {\n    const headers = this.getHeaders();\n    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/multicity`, request, { headers })\n      .pipe(\n        catchError(this.handleError)\n      );\n  }\n\n  /**\n   * Recherche de vols basée sur le formulaire utilisateur\n   * @param formData Données du formulaire de recherche\n   * @returns Observable<FlightSearchResponse>\n   */\n  searchFlights(formData: FlightSearchForm): Observable<FlightSearchResponse> {\n    switch (formData.searchType) {\n      case FlightSearchType.ONE_WAY:\n        return this.searchOneWay(this.buildOneWayRequest(formData));\n      case FlightSearchType.ROUND_TRIP:\n        return this.searchRoundTrip(this.buildRoundTripRequest(formData));\n      case FlightSearchType.MULTI_CITY:\n        return this.searchMultiCity(this.buildMultiCityRequest(formData));\n      default:\n        return throwError(() => new Error('Type de recherche non supporté'));\n    }\n  }\n\n  /**\n   * Construit une requête OneWay à partir des données du formulaire\n   */\n  private buildOneWayRequest(formData: FlightSearchForm): OneWayRequest {\n    return {\n      ProductType: 2, // Type produit pour les vols\n      ServiceTypes: ['Flight'],\n      CheckIn: formData.departureDate,\n      DepartureLocations: [this.createLocation(formData.departureLocation)],\n      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      Passengers: this.buildPassengers(formData.passengers),\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      calculateFlightFees: true,\n      flightClasses: [formData.flightClass],\n      Culture: formData.culture,\n      Currency: formData.currency\n    };\n  }\n\n  /**\n   * Construit une requête RoundTrip à partir des données du formulaire\n   */\n  private buildRoundTripRequest(formData: FlightSearchForm): RoundTripRequest {\n    const nights = this.calculateNights(formData.departureDate, formData.returnDate!);\n    \n    return {\n      ProductType: 2,\n      ServiceTypes: ['Flight'],\n      DepartureLocations: [this.createLocation(formData.departureLocation)],\n      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      CheckIn: formData.departureDate,\n      Night: nights,\n      Passengers: this.buildPassengers(formData.passengers),\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      supportedFlightReponseListTypes: [1, 2],\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      calculateFlightFees: true,\n      Culture: formData.culture,\n      Currency: formData.currency\n    };\n  }\n\n  /**\n   * Construit une requête MultiCity à partir des données du formulaire\n   */\n  private buildMultiCityRequest(formData: FlightSearchForm): MulticityRequest {\n    return {\n      serviceTypes: ['Flight'],\n      productType: 2,\n      arrivalLocations: [this.createLocation(formData.arrivalLocation)],\n      departureLocations: [this.createLocation(formData.departureLocation)],\n      passengers: this.buildPassengers(formData.passengers),\n      checkIns: [formData.departureDate],\n      calculateFlightFees: true,\n      acceptPendingProviders: true,\n      forceFlightBundlePackage: false,\n      disablePackageOfferTotalPrice: false,\n      showOnlyNonStopFlight: formData.directFlightsOnly,\n      supportedFlightReponseListTypes: [1, 2],\n      culture: formData.culture,\n      currency: formData.currency\n    };\n  }\n\n  /**\n   * Crée un objet Location\n   */\n  private createLocation(locationId: string): Location {\n    return {\n      id: locationId,\n      type: LocationType.AIRPORT, // Par défaut, peut être ajusté\n      provider: 1\n    };\n  }\n\n  /**\n   * Construit la liste des passagers\n   */\n  private buildPassengers(passengers: { adults: number; children: number; infants: number }): Passenger[] {\n    const passengerList: Passenger[] = [];\n    \n    if (passengers.adults > 0) {\n      passengerList.push({ type: PassengerType.ADULT, count: passengers.adults });\n    }\n    if (passengers.children > 0) {\n      passengerList.push({ type: PassengerType.CHILD, count: passengers.children });\n    }\n    if (passengers.infants > 0) {\n      passengerList.push({ type: PassengerType.INFANT, count: passengers.infants });\n    }\n    \n    return passengerList;\n  }\n\n  /**\n   * Calcule le nombre de nuits entre deux dates\n   */\n  private calculateNights(checkIn: string, checkOut: string): number {\n    const startDate = new Date(checkIn);\n    const endDate = new Date(checkOut);\n    const timeDiff = endDate.getTime() - startDate.getTime();\n    return Math.ceil(timeDiff / (1000 * 3600 * 24));\n  }\n\n  /**\n   * Obtient les headers pour les requêtes\n   */\n  private getHeaders(): HttpHeaders {\n    return this.authService.getAuthHeaders();\n  }\n\n  /**\n   * Gestion des erreurs HTTP\n   */\n  private handleError(error: HttpErrorResponse): Observable<never> {\n    let errorMessage = 'Une erreur est survenue lors de la recherche de vols';\n    \n    if (error.error instanceof ErrorEvent) {\n      errorMessage = `Erreur: ${error.error.message}`;\n    } else {\n      switch (error.status) {\n        case 400:\n          errorMessage = 'Paramètres de recherche invalides';\n          break;\n        case 401:\n          errorMessage = 'Authentification requise';\n          break;\n        case 403:\n          errorMessage = 'Accès refusé';\n          break;\n        case 404:\n          errorMessage = 'Service de recherche non trouvé';\n          break;\n        case 500:\n          errorMessage = 'Erreur interne du serveur';\n          break;\n        case 0:\n          errorMessage = 'Impossible de contacter le serveur';\n          break;\n        default:\n          errorMessage = `Erreur ${error.status}: ${error.message}`;\n      }\n    }\n    \n    console.error('Erreur de recherche de vols:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAa,gBAAgB;AAEhD,SAMEC,gBAAgB,EAChBC,aAAa,EACbC,YAAY,QAIP,2CAA2C;AAClD,SAASC,WAAW,QAAQ,gCAAgC;;;;AAM5D,OAAM,MAAOC,mBAAmB;EAI9BC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IALJ,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM;IAC5B,KAAAC,oBAAoB,GAAG,qBAAqB,CAAC,CAAC;EAK5D;EAEH;;;;;EAKAC,YAAYA,CAACC,OAAsB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IACjC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAuB,GAAG,IAAI,CAACP,OAAO,GAAG,IAAI,CAACE,oBAAoB,SAAS,EAAEE,OAAO,EAAE;MAAEC;IAAO,CAAE,CAAC,CACpHG,IAAI,CACHjB,UAAU,CAAC,IAAI,CAACkB,WAAW,CAAC,CAC7B;EACL;EAEA;;;;;EAKAC,eAAeA,CAACN,OAAyB;IACvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IACjC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAuB,GAAG,IAAI,CAACP,OAAO,GAAG,IAAI,CAACE,oBAAoB,YAAY,EAAEE,OAAO,EAAE;MAAEC;IAAO,CAAE,CAAC,CACvHG,IAAI,CACHjB,UAAU,CAAC,IAAI,CAACkB,WAAW,CAAC,CAC7B;EACL;EAEA;;;;;EAKAE,eAAeA,CAACP,OAAyB;IACvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IACjC,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAuB,GAAG,IAAI,CAACP,OAAO,GAAG,IAAI,CAACE,oBAAoB,YAAY,EAAEE,OAAO,EAAE;MAAEC;IAAO,CAAE,CAAC,CACvHG,IAAI,CACHjB,UAAU,CAAC,IAAI,CAACkB,WAAW,CAAC,CAC7B;EACL;EAEA;;;;;EAKAG,aAAaA,CAACC,QAA0B;IACtC,QAAQA,QAAQ,CAACC,UAAU;MACzB,KAAKtB,gBAAgB,CAACuB,OAAO;QAC3B,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAI,CAACa,kBAAkB,CAACH,QAAQ,CAAC,CAAC;MAC7D,KAAKrB,gBAAgB,CAACyB,UAAU;QAC9B,OAAO,IAAI,CAACP,eAAe,CAAC,IAAI,CAACQ,qBAAqB,CAACL,QAAQ,CAAC,CAAC;MACnE,KAAKrB,gBAAgB,CAAC2B,UAAU;QAC9B,OAAO,IAAI,CAACR,eAAe,CAAC,IAAI,CAACS,qBAAqB,CAACP,QAAQ,CAAC,CAAC;MACnE;QACE,OAAOvB,UAAU,CAAC,MAAM,IAAI+B,KAAK,CAAC,gCAAgC,CAAC,CAAC;;EAE1E;EAEA;;;EAGQL,kBAAkBA,CAACH,QAA0B;IACnD,OAAO;MACLS,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC,QAAQ,CAAC;MACxBC,OAAO,EAAEX,QAAQ,CAACY,aAAa;MAC/BC,kBAAkB,EAAE,CAAC,IAAI,CAACC,cAAc,CAACd,QAAQ,CAACe,iBAAiB,CAAC,CAAC;MACrEC,gBAAgB,EAAE,CAAC,IAAI,CAACF,cAAc,CAACd,QAAQ,CAACiB,eAAe,CAAC,CAAC;MACjEC,UAAU,EAAE,IAAI,CAACC,eAAe,CAACnB,QAAQ,CAACoB,UAAU,CAAC;MACrDC,qBAAqB,EAAErB,QAAQ,CAACsB,iBAAiB;MACjDC,sBAAsB,EAAE,IAAI;MAC5BC,wBAAwB,EAAE,KAAK;MAC/BC,6BAA6B,EAAE,KAAK;MACpCC,mBAAmB,EAAE,IAAI;MACzBC,aAAa,EAAE,CAAC3B,QAAQ,CAAC4B,WAAW,CAAC;MACrCC,OAAO,EAAE7B,QAAQ,CAAC8B,OAAO;MACzBC,QAAQ,EAAE/B,QAAQ,CAACgC;KACpB;EACH;EAEA;;;EAGQ3B,qBAAqBA,CAACL,QAA0B;IACtD,MAAMiC,MAAM,GAAG,IAAI,CAACC,eAAe,CAAClC,QAAQ,CAACY,aAAa,EAAEZ,QAAQ,CAACmC,UAAW,CAAC;IAEjF,OAAO;MACL1B,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC,QAAQ,CAAC;MACxBG,kBAAkB,EAAE,CAAC,IAAI,CAACC,cAAc,CAACd,QAAQ,CAACe,iBAAiB,CAAC,CAAC;MACrEC,gBAAgB,EAAE,CAAC,IAAI,CAACF,cAAc,CAACd,QAAQ,CAACiB,eAAe,CAAC,CAAC;MACjEN,OAAO,EAAEX,QAAQ,CAACY,aAAa;MAC/BwB,KAAK,EAAEH,MAAM;MACbf,UAAU,EAAE,IAAI,CAACC,eAAe,CAACnB,QAAQ,CAACoB,UAAU,CAAC;MACrDG,sBAAsB,EAAE,IAAI;MAC5BC,wBAAwB,EAAE,KAAK;MAC/BC,6BAA6B,EAAE,KAAK;MACpCY,+BAA+B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACvChB,qBAAqB,EAAErB,QAAQ,CAACsB,iBAAiB;MACjDI,mBAAmB,EAAE,IAAI;MACzBG,OAAO,EAAE7B,QAAQ,CAAC8B,OAAO;MACzBC,QAAQ,EAAE/B,QAAQ,CAACgC;KACpB;EACH;EAEA;;;EAGQzB,qBAAqBA,CAACP,QAA0B;IACtD,OAAO;MACLsC,YAAY,EAAE,CAAC,QAAQ,CAAC;MACxBC,WAAW,EAAE,CAAC;MACdC,gBAAgB,EAAE,CAAC,IAAI,CAAC1B,cAAc,CAACd,QAAQ,CAACiB,eAAe,CAAC,CAAC;MACjEwB,kBAAkB,EAAE,CAAC,IAAI,CAAC3B,cAAc,CAACd,QAAQ,CAACe,iBAAiB,CAAC,CAAC;MACrEK,UAAU,EAAE,IAAI,CAACD,eAAe,CAACnB,QAAQ,CAACoB,UAAU,CAAC;MACrDsB,QAAQ,EAAE,CAAC1C,QAAQ,CAACY,aAAa,CAAC;MAClCc,mBAAmB,EAAE,IAAI;MACzBH,sBAAsB,EAAE,IAAI;MAC5BC,wBAAwB,EAAE,KAAK;MAC/BC,6BAA6B,EAAE,KAAK;MACpCJ,qBAAqB,EAAErB,QAAQ,CAACsB,iBAAiB;MACjDe,+BAA+B,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACvCP,OAAO,EAAE9B,QAAQ,CAAC8B,OAAO;MACzBE,QAAQ,EAAEhC,QAAQ,CAACgC;KACpB;EACH;EAEA;;;EAGQlB,cAAcA,CAAC6B,UAAkB;IACvC,OAAO;MACLC,EAAE,EAAED,UAAU;MACdE,IAAI,EAAEhE,YAAY,CAACiE,OAAO;MAC1BC,QAAQ,EAAE;KACX;EACH;EAEA;;;EAGQ5B,eAAeA,CAACC,UAAiE;IACvF,MAAM4B,aAAa,GAAgB,EAAE;IAErC,IAAI5B,UAAU,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACzBD,aAAa,CAACE,IAAI,CAAC;QAAEL,IAAI,EAAEjE,aAAa,CAACuE,KAAK;QAAEC,KAAK,EAAEhC,UAAU,CAAC6B;MAAM,CAAE,CAAC;;IAE7E,IAAI7B,UAAU,CAACiC,QAAQ,GAAG,CAAC,EAAE;MAC3BL,aAAa,CAACE,IAAI,CAAC;QAAEL,IAAI,EAAEjE,aAAa,CAAC0E,KAAK;QAAEF,KAAK,EAAEhC,UAAU,CAACiC;MAAQ,CAAE,CAAC;;IAE/E,IAAIjC,UAAU,CAACmC,OAAO,GAAG,CAAC,EAAE;MAC1BP,aAAa,CAACE,IAAI,CAAC;QAAEL,IAAI,EAAEjE,aAAa,CAAC4E,MAAM;QAAEJ,KAAK,EAAEhC,UAAU,CAACmC;MAAO,CAAE,CAAC;;IAG/E,OAAOP,aAAa;EACtB;EAEA;;;EAGQd,eAAeA,CAACuB,OAAe,EAAEC,QAAgB;IACvD,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACH,OAAO,CAAC;IACnC,MAAMI,OAAO,GAAG,IAAID,IAAI,CAACF,QAAQ,CAAC;IAClC,MAAMI,QAAQ,GAAGD,OAAO,CAACE,OAAO,EAAE,GAAGJ,SAAS,CAACI,OAAO,EAAE;IACxD,OAAOC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;EACjD;EAEA;;;EAGQrE,UAAUA,CAAA;IAChB,OAAO,IAAI,CAACP,WAAW,CAACgF,cAAc,EAAE;EAC1C;EAEA;;;EAGQtE,WAAWA,CAACuE,KAAwB;IAC1C,IAAIC,YAAY,GAAG,sDAAsD;IAEzE,IAAID,KAAK,CAACA,KAAK,YAAYE,UAAU,EAAE;MACrCD,YAAY,GAAG,WAAWD,KAAK,CAACA,KAAK,CAACG,OAAO,EAAE;KAChD,MAAM;MACL,QAAQH,KAAK,CAACI,MAAM;QAClB,KAAK,GAAG;UACNH,YAAY,GAAG,mCAAmC;UAClD;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,0BAA0B;UACzC;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,cAAc;UAC7B;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,iCAAiC;UAChD;QACF,KAAK,GAAG;UACNA,YAAY,GAAG,2BAA2B;UAC1C;QACF,KAAK,CAAC;UACJA,YAAY,GAAG,oCAAoC;UACnD;QACF;UACEA,YAAY,GAAG,UAAUD,KAAK,CAACI,MAAM,KAAKJ,KAAK,CAACG,OAAO,EAAE;;;IAI/DE,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO1F,UAAU,CAAC,MAAM,IAAI+B,KAAK,CAAC4D,YAAY,CAAC,CAAC;EAClD;;;uBAzNWrF,mBAAmB,EAAA0F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAnB/F,mBAAmB;MAAAgG,OAAA,EAAnBhG,mBAAmB,CAAAiG,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}