{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightSearchComponent } from './components/flight-search/flight-search.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, LoginComponent, DashboardComponent, FlightSearchComponent],\n  imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: HttpRequestInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "ReactiveFormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "AppRoutingModule", "AppComponent", "LoginComponent", "DashboardComponent", "FlightSearchComponent", "HttpRequestInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { FlightSearchComponent } from './components/flight-search/flight-search.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    DashboardComponent,\n    FlightSearchComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    HttpClientModule\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: HttpRequestInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAE1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,sBAAsB,QAAQ,iCAAiC;AAwBjE,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAtBrBZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CACZP,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,qBAAqB,CACtB;EACDK,OAAO,EAAE,CACPb,aAAa,EACbI,gBAAgB,EAChBH,mBAAmB,EACnBC,gBAAgB,CACjB;EACDY,SAAS,EAAE,CACT;IACEC,OAAO,EAAEZ,iBAAiB;IAC1Ba,QAAQ,EAAEP,sBAAsB;IAChCQ,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAACb,YAAY;CACzB,CAAC,C,EACWK,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}