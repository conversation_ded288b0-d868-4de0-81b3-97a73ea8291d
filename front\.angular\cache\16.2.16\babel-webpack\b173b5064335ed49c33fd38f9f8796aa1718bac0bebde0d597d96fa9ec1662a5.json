{"ast": null, "code": "import { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/flight-search.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../services/airline-logo.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction FlightResultsComponent_div_3__svg_svg_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 8);\n    i0.ɵɵelement(1, \"path\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_3_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r9.searchSummary == null ? null : ctx_r9.searchSummary.returnDate, \"\");\n  }\n}\nfunction FlightResultsComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 8);\n    i0.ɵɵelement(7, \"path\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, FlightResultsComponent_div_3__svg_svg_8_Template, 2, 0, \"svg\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(9, \"span\", 55);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 59)(12, \"span\", 60);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, FlightResultsComponent_div_3_span_14_Template, 2, 1, \"span\", 61);\n    i0.ɵɵelementStart(15, \"span\", 62);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 63)(18, \"span\", 64);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 65);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 66)(23, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_3_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.modifySearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(24, \"svg\", 8);\n    i0.ɵɵelement(25, \"path\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Modifier la recherche \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(27, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_3_Template_button_click_27_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.backToSearch());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(28, \"svg\", 8);\n    i0.ɵɵelement(29, \"path\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(30, \" Nouvelle recherche \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.from);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.searchType) === \"roundtrip\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.to);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.departureDate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.returnDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.getPassengerText());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.totalResults, \" vols trouv\\u00E9s\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Recherche effectu\\u00E9e \\u00E0 \", ctx_r0.searchSummary == null ? null : ctx_r0.searchSummary.searchTime, \"\");\n  }\n}\nfunction FlightResultsComponent_label_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 22)(1, \"input\", 72);\n    i0.ɵɵlistener(\"change\", function FlightResultsComponent_label_31_Template_input_change_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const airline_r13 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.toggleAirlineFilter(airline_r13.code));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(2, \"span\", 24);\n    i0.ɵɵelementStart(3, \"span\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const airline_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", airline_r13.code)(\"checked\", ctx_r1.filters.airlines.includes(airline_r13.code));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(airline_r13.name);\n  }\n}\nfunction FlightResultsComponent__svg_path_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 74);\n  }\n}\nfunction FlightResultsComponent__svg_path_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 75);\n  }\n}\nfunction FlightResultsComponent_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"div\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Recherche de vols en cours...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightResultsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"div\", 79);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Erreur lors de la recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_86_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.backToSearch());\n    });\n    i0.ɵɵtext(9, \"Retour \\u00E0 la recherche\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r5.error);\n  }\n}\nfunction FlightResultsComponent_div_87_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"div\", 85);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 57);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Aucun vol trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Essayez de modifier vos crit\\u00E8res de recherche ou vos filtres.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.clearFilters());\n    });\n    i0.ɵɵtext(9, \"Effacer les filtres\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 136);\n    i0.ɵɵlistener(\"error\", function FlightResultsComponent_div_87_div_2_img_4_Template_img_error_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const flight_r22 = i0.ɵɵnextContext().$implicit;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.onLogoError(flight_r22));\n    })(\"load\", function FlightResultsComponent_div_87_div_2_img_4_Template_img_load_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const flight_r22 = i0.ɵɵnextContext().$implicit;\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.onLogoLoad(flight_r22));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", flight_r22.airline.logo, i0.ɵɵsanitizeUrl)(\"alt\", flight_r22.airline.name);\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", flight_r22.airline.code, \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 138);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 134);\n    i0.ɵɵelement(2, \"path\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Direct \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 140);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 134);\n    i0.ɵɵelement(2, \"path\", 141)(3, \"path\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Remboursable \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 143);\n    i0.ɵɵtext(1, \"\\u00C9conomique\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 144);\n    i0.ɵɵtext(1, \"Affaires\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 145);\n    i0.ɵɵtext(1, \"Premi\\u00E8re\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 146);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const flight_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", flight_r22.stops, \" escale\", flight_r22.stops > 1 ? \"s\" : \"\", \" \");\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_span_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 133);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 134);\n    i0.ɵɵelement(2, \"path\", 141)(3, \"path\", 142);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Remboursable \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FlightResultsComponent_div_87_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_2_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r41);\n      const flight_r22 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r40.selectFlight(flight_r22));\n    });\n    i0.ɵɵelementStart(1, \"div\", 87)(2, \"div\", 88)(3, \"div\", 89);\n    i0.ɵɵtemplate(4, FlightResultsComponent_div_87_div_2_img_4_Template, 1, 2, \"img\", 90);\n    i0.ɵɵtemplate(5, FlightResultsComponent_div_87_div_2_div_5_Template, 2, 1, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 92)(7, \"span\", 73);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 93);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 94);\n    i0.ɵɵtext(12, \"Boeing 737-800\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 95);\n    i0.ɵɵtemplate(14, FlightResultsComponent_div_87_div_2_span_14_Template, 4, 0, \"span\", 96);\n    i0.ɵɵtemplate(15, FlightResultsComponent_div_87_div_2_span_15_Template, 5, 0, \"span\", 97);\n    i0.ɵɵtemplate(16, FlightResultsComponent_div_87_div_2_span_16_Template, 2, 0, \"span\", 98);\n    i0.ɵɵtemplate(17, FlightResultsComponent_div_87_div_2_span_17_Template, 2, 0, \"span\", 99);\n    i0.ɵɵtemplate(18, FlightResultsComponent_div_87_div_2_span_18_Template, 2, 0, \"span\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 101)(20, \"div\", 102)(21, \"div\", 103)(22, \"div\", 104);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 105);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 106);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 107)(29, \"div\", 108);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 109);\n    i0.ɵɵelement(32, \"div\", 110);\n    i0.ɵɵtemplate(33, FlightResultsComponent_div_87_div_2_div_33_Template, 2, 2, \"div\", 111);\n    i0.ɵɵelementStart(34, \"div\", 112);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(35, \"svg\", 8);\n    i0.ɵɵelement(36, \"path\", 57);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(37, \"div\", 113)(38, \"div\", 104);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 105);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 106);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 114)(45, \"div\", 115)(46, \"div\", 116);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 117);\n    i0.ɵɵtext(49, \"par personne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 118);\n    i0.ɵɵtext(51, \"TTC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 119)(53, \"button\", 120);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_87_div_2_Template_button_click_53_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r41);\n      const flight_r22 = restoredCtx.$implicit;\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      $event.stopPropagation();\n      return i0.ɵɵresetView(ctx_r42.showFlightDetails(flight_r22));\n    });\n    i0.ɵɵtext(54, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 121);\n    i0.ɵɵtext(56, \"S\\u00E9lectionner\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(57, \"div\", 122)(58, \"div\", 123)(59, \"div\", 124);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(60, \"svg\", 8);\n    i0.ɵɵelement(61, \"path\", 125);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(62, \"div\", 126)(63, \"span\", 127);\n    i0.ɵɵtext(64, \"Bagage cabine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"span\", 128);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(67, \"div\", 129);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(68, \"svg\", 8);\n    i0.ɵɵelement(69, \"path\", 130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(70, \"div\", 126)(71, \"span\", 127);\n    i0.ɵɵtext(72, \"Bagage soute\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"span\", 128);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(75, \"div\", 131);\n    i0.ɵɵtemplate(76, FlightResultsComponent_div_87_div_2_span_76_Template, 5, 0, \"span\", 132);\n    i0.ɵɵelementStart(77, \"span\", 133);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(78, \"svg\", 134);\n    i0.ɵɵelement(79, \"path\", 135);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(80, \" Modifiable \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const flight_r22 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.airline.logo && !flight_r22.airline.logoError);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !flight_r22.airline.logo || flight_r22.airline.logoError);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r22.airline.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", flight_r22.airline.code, \" \", flight_r22.id.split(\"-\")[1] || \"1234\", \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.stops === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.refundable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"Economy\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"Business\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.class === \"First\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(flight_r22.departure.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.departure.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.departure.city);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(flight_r22.duration);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.stops > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.airport);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(flight_r22.arrival.city);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(flight_r22.price.formatted);\n    i0.ɵɵadvance(19);\n    i0.ɵɵtextInterpolate((flight_r22.baggage == null ? null : flight_r22.baggage.carryOn) || \"1 x 8kg\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((flight_r22.baggage == null ? null : flight_r22.baggage.checked) || \"1 x 23kg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", flight_r22.refundable);\n  }\n}\nfunction FlightResultsComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtemplate(1, FlightResultsComponent_div_87_div_1_Template, 10, 0, \"div\", 82);\n    i0.ɵɵtemplate(2, FlightResultsComponent_div_87_div_2_Template, 81, 22, \"div\", 83);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"grid-view\", ctx_r6.viewMode === \"grid\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.filteredFlights.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.getPaginatedFlights())(\"ngForTrackBy\", ctx_r6.trackByFlightId);\n  }\n}\nfunction FlightResultsComponent_div_88_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_button_6_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r46);\n      const page_r44 = restoredCtx.$implicit;\n      const ctx_r45 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r45.changePage(page_r44));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r44 = ctx.$implicit;\n    const ctx_r43 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r44 === ctx_r43.currentPage);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", page_r44, \" \");\n  }\n}\nfunction FlightResultsComponent_div_88_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.changePage(ctx_r47.currentPage - 1));\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 8);\n    i0.ɵɵelement(3, \"path\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Pr\\u00E9c\\u00E9dent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\", 150);\n    i0.ɵɵtemplate(6, FlightResultsComponent_div_88_button_6_Template, 2, 3, \"button\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 148);\n    i0.ɵɵlistener(\"click\", function FlightResultsComponent_div_88_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.changePage(ctx_r49.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \" Suivant \");\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 8);\n    i0.ɵɵelement(10, \"path\", 152);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.currentPage === 1);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.getPageNumbers());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.currentPage === ctx_r7.totalPages);\n  }\n}\nexport class FlightResultsComponent {\n  constructor(flightSearchService, router, route, airlineLogoService) {\n    this.flightSearchService = flightSearchService;\n    this.router = router;\n    this.route = route;\n    this.airlineLogoService = airlineLogoService;\n    this.destroy$ = new Subject();\n    // État du composant\n    this.loading = false;\n    this.error = null;\n    // Données de recherche\n    this.searchSummary = null;\n    this.flights = [];\n    this.filteredFlights = [];\n    // Filtres et tri\n    this.filters = {\n      minPrice: 0,\n      maxPrice: 0,\n      currentPrice: 0,\n      airlines: [],\n      stops: [],\n      departureTime: {\n        min: 0,\n        max: 24\n      },\n      duration: {\n        min: 0,\n        max: 24\n      }\n    };\n    this.sortBy = 'price'; // price, duration, departure\n    this.sortOrder = 'asc'; // asc, desc\n    // Options d'affichage\n    this.viewMode = 'list'; // list, grid\n    this.showFilters = true;\n    // Pagination\n    this.currentPage = 1;\n    this.itemsPerPage = 10;\n    this.totalPages = 1;\n  }\n  ngOnInit() {\n    this.loadSearchResults();\n    this.initializeFilters();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  /**\n   * Charge les résultats de recherche depuis le service\n   */\n  loadSearchResults() {\n    // Récupérer les paramètres de recherche depuis l'URL ou le service\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['searchData']) {\n        try {\n          const searchData = JSON.parse(decodeURIComponent(params['searchData']));\n          this.performSearch(searchData);\n        } catch (error) {\n          console.error('Erreur lors du parsing des données de recherche:', error);\n          this.router.navigate(['/search-flight']);\n        }\n      } else {\n        // Rediriger vers la page de recherche si aucune donnée\n        this.router.navigate(['/search-flight']);\n      }\n    });\n  }\n  /**\n   * Effectue la recherche de vols\n   */\n  performSearch(searchData) {\n    this.loading = true;\n    this.error = null;\n    // Simuler une recherche pour le moment\n    setTimeout(() => {\n      this.processSearchResults(this.generateMockResults(), searchData);\n      this.loading = false;\n    }, 2000);\n  }\n  /**\n   * Traite les résultats de recherche\n   */\n  processSearchResults(response, searchData) {\n    // Créer le résumé de recherche\n    this.searchSummary = {\n      searchType: searchData.type,\n      from: searchData.params.departureLocation,\n      to: searchData.params.arrivalLocation,\n      departureDate: searchData.params.departureDate,\n      returnDate: searchData.params.returnDate,\n      passengers: searchData.params.passengers,\n      totalResults: 0,\n      searchTime: new Date().toLocaleTimeString()\n    };\n    // Traiter les vols selon le type de réponse\n    this.flights = this.extractFlights(response);\n    this.searchSummary.totalResults = this.flights.length;\n    // Appliquer les filtres initiaux\n    this.applyFilters();\n    this.updatePagination();\n  }\n  /**\n   * Extrait les vols de la réponse API\n   */\n  extractFlights(response) {\n    // Cette méthode devra être adaptée selon la structure exacte de la réponse\n    const flights = [];\n    // Exemple de traitement - à adapter selon la vraie structure\n    if (response.body?.flights) {\n      response.body.flights.forEach((flight, index) => {\n        flights.push({\n          id: flight.id || `flight-${index}`,\n          airline: {\n            code: flight.airline?.code || 'XX',\n            name: flight.airline?.name || 'Compagnie inconnue',\n            logo: flight.airline?.logo || this.airlineLogoService.getAirlineLogo(flight.airline?.code || '')\n          },\n          departure: {\n            airport: flight.departure?.airport || '',\n            city: flight.departure?.city || '',\n            time: flight.departure?.time || '',\n            date: flight.departure?.date || ''\n          },\n          arrival: {\n            airport: flight.arrival?.airport || '',\n            city: flight.arrival?.city || '',\n            time: flight.arrival?.time || '',\n            date: flight.arrival?.date || ''\n          },\n          duration: flight.duration || '0h 00m',\n          stops: flight.stops || 0,\n          price: {\n            amount: flight.price?.amount || 0,\n            currency: flight.price?.currency || 'EUR',\n            formatted: this.formatPrice(flight.price?.amount || 0)\n          },\n          class: flight.class || 'Economy',\n          refundable: flight.refundable || false,\n          baggage: flight.baggage\n        });\n      });\n    }\n    return flights;\n  }\n  /**\n   * Initialise les filtres avec les valeurs par défaut\n   */\n  initializeFilters() {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice; // Commencer avec le prix maximum\n      // Initialiser les filtres de compagnies aériennes (tous désélectionnés au début)\n      this.filters.airlines = [];\n      // Initialiser les filtres d'escales (tous désélectionnés au début)\n      this.filters.stops = [];\n    } else {\n      // Valeurs par défaut si aucun vol\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n  }\n  /**\n   * Applique les filtres aux vols\n   */\n  applyFilters() {\n    this.filteredFlights = this.flights.filter(flight => {\n      // Filtre par prix (utiliser currentPrice au lieu de maxPrice)\n      if (flight.price.amount > this.filters.currentPrice) return false;\n      // Filtre par compagnie aérienne (si des compagnies sont sélectionnées)\n      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {\n        return false;\n      }\n      // Filtre par nombre d'escales (si des escales sont sélectionnées)\n      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {\n        return false;\n      }\n      return true;\n    });\n    this.sortFlights();\n    this.updatePagination();\n  }\n  /**\n   * Trie les vols selon les critères sélectionnés\n   */\n  sortFlights() {\n    this.filteredFlights.sort((a, b) => {\n      let comparison = 0;\n      switch (this.sortBy) {\n        case 'price':\n          comparison = a.price.amount - b.price.amount;\n          break;\n        case 'duration':\n          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);\n          break;\n        case 'departure':\n          comparison = a.departure.time.localeCompare(b.departure.time);\n          break;\n      }\n      return this.sortOrder === 'asc' ? comparison : -comparison;\n    });\n  }\n  /**\n   * Met à jour la pagination\n   */\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);\n    if (this.currentPage > this.totalPages) {\n      this.currentPage = 1;\n    }\n  }\n  /**\n   * Obtient les vols pour la page actuelle\n   */\n  getPaginatedFlights() {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredFlights.slice(startIndex, endIndex);\n  }\n  /**\n   * Change de page\n   */\n  changePage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n  /**\n   * Sélectionne un vol\n   */\n  selectFlight(flight) {\n    // Naviguer vers la page de détails ou de réservation\n    console.log('Vol sélectionné:', flight);\n    // TODO: Implémenter la navigation vers la page de réservation\n  }\n  /**\n   * Affiche les détails d'un vol\n   */\n  showFlightDetails(flight) {\n    console.log('Affichage des détails du vol:', flight);\n    // TODO: Ouvrir un modal avec les détails complets du vol\n    // ou naviguer vers une page de détails\n  }\n  /**\n   * Gère l'erreur de chargement du logo\n   */\n  onLogoError(flight) {\n    flight.airline.logoError = true;\n    console.log(`Erreur de chargement du logo pour ${flight.airline.code}`);\n  }\n  /**\n   * Gère le chargement réussi du logo\n   */\n  onLogoLoad(flight) {\n    flight.airline.logoError = false;\n  }\n  /**\n   * Retourne à la recherche\n   */\n  backToSearch() {\n    this.router.navigate(['/search-flight']);\n  }\n  /**\n   * Modifie la recherche\n   */\n  modifySearch() {\n    // Pré-remplir le formulaire avec les critères actuels\n    this.router.navigate(['/search-flight'], {\n      queryParams: {\n        modify: true,\n        searchData: JSON.stringify(this.searchSummary)\n      }\n    });\n  }\n  /**\n   * Formate le prix\n   */\n  formatPrice(amount) {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  }\n  /**\n   * Parse la durée en minutes\n   */\n  parseDuration(duration) {\n    const match = duration.match(/(\\d+)h\\s*(\\d+)m/);\n    if (match) {\n      return parseInt(match[1]) * 60 + parseInt(match[2]);\n    }\n    return 0;\n  }\n  /**\n   * Obtient le texte des passagers\n   */\n  getPassengerText() {\n    if (!this.searchSummary) return '';\n    const parts = [];\n    const p = this.searchSummary.passengers;\n    if (p.adults > 0) {\n      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);\n    }\n    if (p.children > 0) {\n      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);\n    }\n    if (p.infants > 0) {\n      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);\n    }\n    return parts.join(', ');\n  }\n  /**\n   * Obtient les compagnies aériennes uniques\n   */\n  getUniqueAirlines() {\n    const airlines = new Map();\n    this.flights.forEach(flight => {\n      airlines.set(flight.airline.code, flight.airline.name);\n    });\n    return Array.from(airlines.entries()).map(([code, name]) => ({\n      code,\n      name\n    }));\n  }\n  /**\n   * Toggle le filtre de compagnie aérienne\n   */\n  toggleAirlineFilter(airlineCode) {\n    const index = this.filters.airlines.indexOf(airlineCode);\n    if (index > -1) {\n      this.filters.airlines.splice(index, 1);\n    } else {\n      this.filters.airlines.push(airlineCode);\n    }\n    this.applyFilters();\n  }\n  /**\n   * Toggle le filtre d'escales\n   */\n  toggleStopsFilter(stops) {\n    const index = this.filters.stops.indexOf(stops);\n    if (index > -1) {\n      this.filters.stops.splice(index, 1);\n    } else {\n      this.filters.stops.push(stops);\n    }\n    this.applyFilters();\n  }\n  /**\n   * Efface tous les filtres\n   */\n  clearFilters() {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice;\n    } else {\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n    this.filters.airlines = [];\n    this.filters.stops = [];\n    this.filters.departureTime = {\n      min: 0,\n      max: 24\n    };\n    this.filters.duration = {\n      min: 0,\n      max: 24\n    };\n    this.applyFilters();\n  }\n  /**\n   * Toggle l'ordre de tri\n   */\n  toggleSortOrder() {\n    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    this.sortFlights();\n  }\n  /**\n   * Obtient les numéros de page pour la pagination\n   */\n  getPageNumbers() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  /**\n   * TrackBy function pour la performance\n   */\n  trackByFlightId(index, flight) {\n    return flight.id;\n  }\n  /**\n   * Obtient le prix minimum pour le slider\n   */\n  getMinPrice() {\n    if (this.flights.length === 0) return 0;\n    return Math.min(...this.flights.map(f => f.price.amount));\n  }\n  /**\n   * Obtient le prix maximum pour le slider\n   */\n  getMaxPrice() {\n    if (this.flights.length === 0) return 1000;\n    return Math.max(...this.flights.map(f => f.price.amount));\n  }\n  /**\n   * Met à jour le prix courant du filtre\n   */\n  updateCurrentPrice(price) {\n    this.filters.currentPrice = price;\n    this.applyFilters();\n  }\n  /**\n   * Génère des résultats de test\n   */\n  generateMockResults() {\n    return {\n      body: {\n        flights: [{\n          id: 'flight-1',\n          airline: {\n            code: 'TK',\n            name: 'Turkish Airlines'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '08:30',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '11:45',\n            date: '2025-06-17'\n          },\n          duration: '3h 15m',\n          stops: 0,\n          price: {\n            amount: 450,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: true,\n          baggage: {\n            carryOn: '8kg',\n            checked: '23kg'\n          }\n        }, {\n          id: 'flight-2',\n          airline: {\n            code: 'TU',\n            name: 'Tunisair'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '14:20',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '17:35',\n            date: '2025-06-17'\n          },\n          duration: '3h 15m',\n          stops: 0,\n          price: {\n            amount: 380,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: false,\n          baggage: {\n            carryOn: '8kg',\n            checked: '20kg'\n          }\n        }, {\n          id: 'flight-3',\n          airline: {\n            code: 'AF',\n            name: 'Air France'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '10:15',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '16:30',\n            date: '2025-06-17'\n          },\n          duration: '6h 15m',\n          stops: 1,\n          price: {\n            amount: 520,\n            currency: 'EUR'\n          },\n          class: 'Economy',\n          refundable: true,\n          baggage: {\n            carryOn: '12kg',\n            checked: '23kg'\n          }\n        }, {\n          id: 'flight-4',\n          airline: {\n            code: 'LH',\n            name: 'Lufthansa'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '16:45',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '22:10',\n            date: '2025-06-17'\n          },\n          duration: '5h 25m',\n          stops: 1,\n          price: {\n            amount: 610,\n            currency: 'EUR'\n          },\n          class: 'Business',\n          refundable: true,\n          baggage: {\n            carryOn: '8kg',\n            checked: '32kg'\n          }\n        }, {\n          id: 'flight-5',\n          airline: {\n            code: 'EK',\n            name: 'Emirates'\n          },\n          departure: {\n            airport: 'IST',\n            city: 'Istanbul',\n            time: '22:30',\n            date: '2025-06-17'\n          },\n          arrival: {\n            airport: 'TUN',\n            city: 'Tunis',\n            time: '08:45',\n            date: '2025-06-18'\n          },\n          duration: '10h 15m',\n          stops: 1,\n          price: {\n            amount: 750,\n            currency: 'EUR'\n          },\n          class: 'Business',\n          refundable: true,\n          baggage: {\n            carryOn: '7kg',\n            checked: '30kg'\n          }\n        }]\n      }\n    };\n  }\n  static {\n    this.ɵfac = function FlightResultsComponent_Factory(t) {\n      return new (t || FlightResultsComponent)(i0.ɵɵdirectiveInject(i1.FlightSearchService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.AirlineLogoService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FlightResultsComponent,\n      selectors: [[\"app-flight-results\"]],\n      decls: 89,\n      vars: 26,\n      consts: [[1, \"flight-results-container\"], [1, \"results-header\"], [1, \"header-content\"], [\"class\", \"search-summary\", 4, \"ngIf\"], [1, \"results-content\"], [1, \"filters-sidebar\"], [1, \"filters-header\"], [1, \"btn-toggle\", 3, \"click\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\"], [\"d\", \"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"], [1, \"filter-section\"], [1, \"price-filter\"], [1, \"price-range-info\"], [1, \"price-min\"], [1, \"price-max\"], [\"type\", \"range\", 1, \"price-slider\", 3, \"ngModel\", \"min\", \"max\", \"step\", \"ngModelChange\", \"input\"], [1, \"price-current\"], [1, \"price-label\"], [1, \"price-value\"], [1, \"airline-filters\"], [\"class\", \"checkbox-label\", 4, \"ngFor\", \"ngForOf\"], [1, \"stops-filters\"], [1, \"checkbox-label\"], [\"type\", \"checkbox\", \"value\", \"0\", 3, \"checked\", \"change\"], [1, \"checkmark\"], [\"type\", \"checkbox\", \"value\", \"1\", 3, \"checked\", \"change\"], [\"type\", \"checkbox\", \"value\", \"2\", 3, \"checked\", \"change\"], [1, \"filter-actions\"], [1, \"btn-clear\", 3, \"click\"], [1, \"results-main\"], [1, \"results-toolbar\"], [1, \"toolbar-left\"], [1, \"btn-filter-toggle\", 3, \"click\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h10V6H3zm0 7h8v-2H3v2zm16-1v8h2v-8h-2zm-3-3h2V6h-2v3zm3-3v3h2V6h-2z\"], [1, \"view-toggle\"], [1, \"view-btn\", 3, \"click\"], [\"d\", \"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z\"], [\"d\", \"M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z\"], [1, \"toolbar-right\"], [1, \"sort-controls\"], [3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"price\"], [\"value\", \"duration\"], [\"value\", \"departure\"], [1, \"sort-order-btn\", 3, \"click\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\", 4, \"ngIf\"], [\"d\", \"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\", 4, \"ngIf\"], [1, \"results-info\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [\"class\", \"flights-container\", 3, \"grid-view\", 4, \"ngIf\"], [\"class\", \"pagination\", 4, \"ngIf\"], [1, \"search-summary\"], [1, \"route-info\"], [1, \"route-main\"], [1, \"airport-code\"], [1, \"route-arrow\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"], [\"viewBox\", \"0 0 24 24\", \"fill\", \"currentColor\", 4, \"ngIf\"], [1, \"route-details\"], [1, \"date\"], [\"class\", \"date\", 4, \"ngIf\"], [1, \"passengers\"], [1, \"results-count\"], [1, \"count\"], [1, \"search-time\"], [1, \"header-actions\"], [1, \"btn-secondary\", 3, \"click\"], [\"d\", \"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"], [1, \"btn-primary\", 3, \"click\"], [\"d\", \"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\"], [\"d\", \"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\", \"transform\", \"rotate(180 12 12)\"], [\"type\", \"checkbox\", 3, \"value\", \"checked\", \"change\"], [1, \"airline-name\"], [\"d\", \"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\"], [\"d\", \"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\"], [1, \"loading-state\"], [1, \"loading-spinner\"], [1, \"error-state\"], [1, \"error-icon\"], [\"d\", \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"], [1, \"flights-container\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"flight-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"flight-card\", 3, \"click\"], [1, \"flight-header\"], [1, \"airline-info\"], [1, \"airline-logo-container\"], [\"class\", \"airline-logo\", 3, \"src\", \"alt\", \"error\", \"load\", 4, \"ngIf\"], [\"class\", \"airline-placeholder\", 4, \"ngIf\"], [1, \"airline-details\"], [1, \"flight-number\"], [1, \"aircraft-type\"], [1, \"flight-badges\"], [\"class\", \"badge direct\", 4, \"ngIf\"], [\"class\", \"badge refundable\", 4, \"ngIf\"], [\"class\", \"badge economy\", 4, \"ngIf\"], [\"class\", \"badge business\", 4, \"ngIf\"], [\"class\", \"badge first\", 4, \"ngIf\"], [1, \"flight-details\"], [1, \"flight-route\"], [1, \"departure\"], [1, \"time\"], [1, \"airport\"], [1, \"city\"], [1, \"flight-info\"], [1, \"duration\"], [1, \"route-line\"], [1, \"line\"], [\"class\", \"stops\", 4, \"ngIf\"], [1, \"plane-icon\"], [1, \"arrival\"], [1, \"flight-price\"], [1, \"price-container\"], [1, \"price-amount\"], [1, \"price-details\"], [1, \"price-taxes\"], [1, \"action-buttons\"], [1, \"btn-details\", 3, \"click\"], [1, \"btn-select\"], [1, \"flight-baggage\"], [1, \"baggage-info\"], [1, \"baggage-item\", \"carry-on\"], [\"d\", \"M8.5 6h7l1.5 9H7l1.5-9zM10 4V2h4v2h-4z\"], [1, \"baggage-details\"], [1, \"baggage-type\"], [1, \"baggage-allowance\"], [1, \"baggage-item\", \"checked\"], [\"d\", \"M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z\"], [1, \"flight-extras\"], [\"class\", \"extra-item\", 4, \"ngIf\"], [1, \"extra-item\"], [\"viewBox\", \"0 0 16 16\", \"fill\", \"currentColor\"], [\"d\", \"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z\"], [1, \"airline-logo\", 3, \"src\", \"alt\", \"error\", \"load\"], [1, \"airline-placeholder\"], [1, \"badge\", \"direct\"], [\"d\", \"M8 0L6.5 1.5L10 5H3v2h7l-3.5 3.5L8 12l6-6L8 0z\"], [1, \"badge\", \"refundable\"], [\"d\", \"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"], [\"d\", \"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"], [1, \"badge\", \"economy\"], [1, \"badge\", \"business\"], [1, \"badge\", \"first\"], [1, \"stops\"], [1, \"pagination\"], [1, \"page-btn\", 3, \"disabled\", \"click\"], [\"d\", \"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"], [1, \"page-numbers\"], [\"class\", \"page-number\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [1, \"page-number\", 3, \"click\"]],\n      template: function FlightResultsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, FlightResultsComponent_div_3_Template, 31, 8, \"div\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"aside\", 5)(6, \"div\", 6)(7, \"h3\");\n          i0.ɵɵtext(8, \"Filtres\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_9_listener() {\n            return ctx.showFilters = !ctx.showFilters;\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(10, \"svg\", 8);\n          i0.ɵɵelement(11, \"path\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"h4\");\n          i0.ɵɵtext(14, \"Prix maximum\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"div\", 12)(17, \"span\", 13);\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 14);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function FlightResultsComponent_Template_input_ngModelChange_21_listener($event) {\n            return ctx.filters.currentPrice = $event;\n          })(\"input\", function FlightResultsComponent_Template_input_input_21_listener($event) {\n            return ctx.updateCurrentPrice($event.target.value);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"span\", 17);\n          i0.ɵɵtext(24, \"Prix maximum:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 18);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 10)(28, \"h4\");\n          i0.ɵɵtext(29, \"Compagnies a\\u00E9riennes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 19);\n          i0.ɵɵtemplate(31, FlightResultsComponent_label_31_Template, 5, 3, \"label\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 10)(33, \"h4\");\n          i0.ɵɵtext(34, \"Nombre d'escales\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 21)(36, \"label\", 22)(37, \"input\", 23);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_37_listener() {\n            return ctx.toggleStopsFilter(0);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"span\", 24);\n          i0.ɵɵelementStart(39, \"span\");\n          i0.ɵɵtext(40, \"Vol direct\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"label\", 22)(42, \"input\", 25);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_42_listener() {\n            return ctx.toggleStopsFilter(1);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"span\", 24);\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45, \"1 escale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"label\", 22)(47, \"input\", 26);\n          i0.ɵɵlistener(\"change\", function FlightResultsComponent_Template_input_change_47_listener() {\n            return ctx.toggleStopsFilter(2);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"span\", 24);\n          i0.ɵɵelementStart(49, \"span\");\n          i0.ɵɵtext(50, \"2+ escales\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 27)(52, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_52_listener() {\n            return ctx.clearFilters();\n          });\n          i0.ɵɵtext(53, \"Effacer les filtres\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"main\", 29)(55, \"div\", 30)(56, \"div\", 31)(57, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_57_listener() {\n            return ctx.showFilters = !ctx.showFilters;\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(58, \"svg\", 8);\n          i0.ɵɵelement(59, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Filtres \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(61, \"div\", 34)(62, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_62_listener() {\n            return ctx.viewMode = \"list\";\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 8);\n          i0.ɵɵelement(64, \"path\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(65, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_65_listener() {\n            return ctx.viewMode = \"grid\";\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(66, \"svg\", 8);\n          i0.ɵɵelement(67, \"path\", 37);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(68, \"div\", 38)(69, \"div\", 39)(70, \"label\");\n          i0.ɵɵtext(71, \"Trier par:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"select\", 40);\n          i0.ɵɵlistener(\"ngModelChange\", function FlightResultsComponent_Template_select_ngModelChange_72_listener($event) {\n            return ctx.sortBy = $event;\n          })(\"change\", function FlightResultsComponent_Template_select_change_72_listener() {\n            ctx.sortFlights();\n            return ctx.applyFilters();\n          });\n          i0.ɵɵelementStart(73, \"option\", 41);\n          i0.ɵɵtext(74, \"Prix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"option\", 42);\n          i0.ɵɵtext(76, \"Dur\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"option\", 43);\n          i0.ɵɵtext(78, \"Heure de d\\u00E9part\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"button\", 44);\n          i0.ɵɵlistener(\"click\", function FlightResultsComponent_Template_button_click_79_listener() {\n            return ctx.toggleSortOrder();\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(80, \"svg\", 8);\n          i0.ɵɵtemplate(81, FlightResultsComponent__svg_path_81_Template, 1, 0, \"path\", 45);\n          i0.ɵɵtemplate(82, FlightResultsComponent__svg_path_82_Template, 1, 0, \"path\", 46);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(83, \"span\", 47);\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(85, FlightResultsComponent_div_85_Template, 4, 0, \"div\", 48);\n          i0.ɵɵtemplate(86, FlightResultsComponent_div_86_Template, 10, 1, \"div\", 49);\n          i0.ɵɵtemplate(87, FlightResultsComponent_div_87_Template, 3, 5, \"div\", 50);\n          i0.ɵɵtemplate(88, FlightResultsComponent_div_88_Template, 11, 3, \"div\", 51);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.searchSummary);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"hidden\", !ctx.showFilters);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getMinPrice(), \"\\u20AC\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.getMaxPrice(), \"\\u20AC\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngModel\", ctx.filters.currentPrice)(\"min\", ctx.getMinPrice())(\"max\", ctx.getMaxPrice())(\"step\", 10);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"\", ctx.filters.currentPrice, \"\\u20AC\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getUniqueAirlines());\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(0));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"checked\", ctx.filters.stops.includes(2));\n          i0.ɵɵadvance(15);\n          i0.ɵɵclassProp(\"active\", ctx.viewMode === \"list\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.viewMode === \"grid\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngModel\", ctx.sortBy);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.sortOrder === \"asc\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.sortOrder === \"desc\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.filteredFlights.length, \" r\\u00E9sultats \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.RangeValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      styles: [\".flight-results-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n\\n\\n.results-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  padding: 2rem 0;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n}\\n\\n.search-summary[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  flex-wrap: wrap;\\n  gap: 2rem;\\n}\\n\\n.route-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n}\\n\\n.route-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.airport-code[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 0.5rem 1rem;\\n  border-radius: 12px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.route-arrow[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.route-arrow[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  opacity: 0.8;\\n}\\n\\n.route-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n}\\n\\n.results-count[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.count[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.search-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  opacity: 0.8;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1.5rem;\\n  border-radius: 12px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: none;\\n  font-size: 0.9rem;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.2);\\n  color: white;\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2c5aa0;\\n}\\n\\n.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n\\n\\n.results-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  gap: 2rem;\\n  padding: 2rem;\\n}\\n\\n\\n\\n.filters-sidebar[_ngcontent-%COMP%] {\\n  width: 300px;\\n  background: white;\\n  border-radius: 20px;\\n  padding: 2rem;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.filters-sidebar.hidden[_ngcontent-%COMP%] {\\n  transform: translateX(-100%);\\n  opacity: 0;\\n  pointer-events: none;\\n}\\n\\n.filters-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f7fafc;\\n}\\n\\n.filters-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 700;\\n}\\n\\n.btn-toggle[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border: none;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-toggle[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  color: #2d3748;\\n}\\n\\n.filter-section[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n\\n.filter-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #4a5568;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n\\n.price-filter[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.price-range-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 0.8rem;\\n  color: #718096;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.price-min[_ngcontent-%COMP%], .price-max[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.price-slider[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  border-radius: 3px;\\n  background: linear-gradient(90deg, #e2e8f0 0%, #2c5aa0 50%, #e2e8f0 100%);\\n  outline: none;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.price-slider[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #cbd5e0 0%, #2c5aa0 50%, #cbd5e0 100%);\\n}\\n\\n.price-slider[_ngcontent-%COMP%]::-webkit-slider-thumb {\\n  appearance: none;\\n  width: 22px;\\n  height: 22px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);\\n  cursor: pointer;\\n  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.4);\\n  border: 2px solid white;\\n  -webkit-transition: all 0.3s ease;\\n  transition: all 0.3s ease;\\n}\\n\\n.price-slider[_ngcontent-%COMP%]::-webkit-slider-thumb:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 6px 16px rgba(44, 90, 160, 0.5);\\n}\\n\\n.price-current[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0.75rem;\\n  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\\n  border-radius: 12px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.price-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 500;\\n}\\n\\n.price-value[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2c5aa0;\\n  font-size: 1.1rem;\\n  background: white;\\n  padding: 0.25rem 0.75rem;\\n  border-radius: 8px;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.airline-filters[_ngcontent-%COMP%], .stops-filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.checkbox-label[_ngcontent-%COMP%]   input[type=\\\"checkbox\\\"][_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  accent-color: #2c5aa0;\\n  cursor: pointer;\\n}\\n\\n.checkmark[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 4px;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n\\n.airline-name[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #4a5568;\\n}\\n\\n.btn-clear[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.75rem;\\n  background: transparent;\\n  border: 2px solid #e2e8f0;\\n  border-radius: 12px;\\n  color: #718096;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-clear[_ngcontent-%COMP%]:hover {\\n  border-color: #dc3545;\\n  color: #dc3545;\\n}\\n\\n\\n\\n.results-main[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.results-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 2rem;\\n  padding: 1.5rem;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.toolbar-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  background: #f7fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 12px;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  border-color: #cbd5e0;\\n}\\n\\n.btn-filter-toggle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.view-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  padding: 4px;\\n}\\n\\n.view-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: none;\\n  background: transparent;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.view-btn.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #2c5aa0;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.view-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.toolbar-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n}\\n\\n.sort-controls[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  cursor: pointer;\\n  color: #718096;\\n  transition: all 0.3s ease;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n  color: #2c5aa0;\\n}\\n\\n.sort-order-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.results-info[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.loading-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 4rem 2rem;\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #2c5aa0;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 1rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.error-icon[_ngcontent-%COMP%], .no-results-icon[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  margin: 0 auto 1rem;\\n  color: #e53e3e;\\n}\\n\\n.no-results-icon[_ngcontent-%COMP%] {\\n  color: #718096;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #2d3748;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 2rem 0;\\n  color: #718096;\\n  font-size: 1rem;\\n}\\n\\n\\n\\n.flights-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.flights-container.grid-view[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.flight-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 0;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e2e8f0;\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n}\\n\\n.flight-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\\n  border-color: #6366f1;\\n}\\n\\n.flight-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 1.5rem 1.5rem 1rem 1.5rem;\\n  border-bottom: 1px solid #f1f5f9;\\n}\\n\\n.airline-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n\\n.airline-logo-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.airline-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.airline-logo[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 6px;\\n  object-fit: contain;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.airline-placeholder[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);\\n  color: white;\\n  border-radius: 6px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.75rem;\\n}\\n\\n.airline-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #1e293b;\\n  font-size: 0.9rem;\\n}\\n\\n.flight-number[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #64748b;\\n  font-weight: 500;\\n}\\n\\n.aircraft-type[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #94a3b8;\\n}\\n\\n.flight-badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  flex-wrap: wrap;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 6px;\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.3px;\\n}\\n\\n.badge[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 12px;\\n  height: 12px;\\n}\\n\\n.badge.direct[_ngcontent-%COMP%] {\\n  background: #dcfce7;\\n  color: #166534;\\n  border: 1px solid #bbf7d0;\\n}\\n\\n.badge.refundable[_ngcontent-%COMP%] {\\n  background: #dbeafe;\\n  color: #1e40af;\\n  border: 1px solid #bfdbfe;\\n}\\n\\n.badge.economy[_ngcontent-%COMP%] {\\n  background: #f1f5f9;\\n  color: #475569;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.badge.business[_ngcontent-%COMP%] {\\n  background: #fef3c7;\\n  color: #92400e;\\n  border: 1px solid #fde68a;\\n}\\n\\n.badge.first[_ngcontent-%COMP%] {\\n  background: #fce7f3;\\n  color: #be185d;\\n  border: 1px solid #f9a8d4;\\n}\\n\\n.flight-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  gap: 2rem;\\n  padding: 1.5rem;\\n}\\n\\n.flight-route[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  gap: 2rem;\\n}\\n\\n.departure[_ngcontent-%COMP%], .arrival[_ngcontent-%COMP%] {\\n  text-align: center;\\n  min-width: 80px;\\n}\\n\\n.time[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.airport[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: #64748b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.city[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #94a3b8;\\n}\\n\\n.flight-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: center;\\n}\\n\\n.duration[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #4a5568;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.route-line[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.line[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, #e2e8f0 0%, #2c5aa0 50%, #e2e8f0 100%);\\n  flex: 1;\\n  position: relative;\\n}\\n\\n.stops[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -1.5rem;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  font-size: 0.75rem;\\n  color: #718096;\\n  background: white;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 12px;\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.plane-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 24px;\\n  height: 24px;\\n  background: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #2c5aa0;\\n  border: 2px solid #e2e8f0;\\n}\\n\\n.plane-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.flight-price[_ngcontent-%COMP%] {\\n  text-align: right;\\n  min-width: 140px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 1rem;\\n}\\n\\n.price-container[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.price-amount[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #1e293b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.price-details[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #64748b;\\n  margin-bottom: 0.25rem;\\n}\\n\\n.price-taxes[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #94a3b8;\\n}\\n\\n.action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n  width: 100%;\\n}\\n\\n.btn-details[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #6366f1;\\n  border: 1px solid #6366f1;\\n  padding: 0.5rem 1rem;\\n  border-radius: 6px;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-details[_ngcontent-%COMP%]:hover {\\n  background: #6366f1;\\n  color: white;\\n}\\n\\n.btn-select[_ngcontent-%COMP%] {\\n  background: #6366f1;\\n  color: white;\\n  border: none;\\n  padding: 0.75rem 1rem;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  font-size: 0.9rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n}\\n\\n.btn-select[_ngcontent-%COMP%]:hover {\\n  background: #4f46e5;\\n  transform: translateY(-1px);\\n}\\n\\n.flight-baggage[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  padding: 1rem 1.5rem;\\n  border-top: 1px solid #e2e8f0;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.baggage-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  flex-wrap: wrap;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  flex: 1;\\n  min-width: 150px;\\n}\\n\\n.baggage-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  color: #64748b;\\n}\\n\\n.baggage-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.baggage-type[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: #374151;\\n}\\n\\n.baggage-allowance[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #6b7280;\\n}\\n\\n.carry-on[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n\\n.checked[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n}\\n\\n.flight-extras[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  flex-wrap: wrap;\\n}\\n\\n.extra-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.25rem 0.75rem;\\n  background: white;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 6px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: #374151;\\n}\\n\\n.extra-item[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 14px;\\n  height: 14px;\\n  color: #059669;\\n}\\n\\n\\n\\n.pagination[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-top: 3rem;\\n  padding: 2rem;\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\\n  border: 1px solid #e2e8f0;\\n}\\n\\n.page-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  padding: 0.75rem 1rem;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 12px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #f7fafc;\\n  border-color: #2c5aa0;\\n  color: #2c5aa0;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n\\n.page-btn[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 16px;\\n  height: 16px;\\n}\\n\\n.page-numbers[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n}\\n\\n.page-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 1px solid #e2e8f0;\\n  border-radius: 8px;\\n  background: white;\\n  color: #4a5568;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.page-number.active[_ngcontent-%COMP%] {\\n  background: #2c5aa0;\\n  color: white;\\n  border-color: #2c5aa0;\\n}\\n\\n.page-number[_ngcontent-%COMP%]:hover:not(.active) {\\n  background: #f7fafc;\\n  border-color: #2c5aa0;\\n  color: #2c5aa0;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .results-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .filters-sidebar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    order: 2;\\n  }\\n  \\n  .results-main[_ngcontent-%COMP%] {\\n    order: 1;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .search-summary[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 1rem;\\n  }\\n  \\n  .header-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  \\n  .btn-secondary[_ngcontent-%COMP%], .btn-primary[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  \\n  .results-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .toolbar-left[_ngcontent-%COMP%], .toolbar-right[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  \\n  .flight-details[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .flight-route[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  \\n  .flights-container.grid-view[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .pagination[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n  \\n  .page-numbers[_ngcontent-%COMP%] {\\n    order: -1;\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r9", "searchSummary", "returnDate", "ɵɵnamespaceSVG", "ɵɵtemplate", "FlightResultsComponent_div_3__svg_svg_8_Template", "ɵɵnamespaceHTML", "FlightResultsComponent_div_3_span_14_Template", "ɵɵlistener", "FlightResultsComponent_div_3_Template_button_click_23_listener", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "modifySearch", "FlightResultsComponent_div_3_Template_button_click_27_listener", "ctx_r12", "backToSearch", "ɵɵtextInterpolate", "ctx_r0", "from", "ɵɵproperty", "searchType", "to", "departureDate", "getPassengerText", "totalResults", "searchTime", "FlightResultsComponent_label_31_Template_input_change_1_listener", "restoredCtx", "_r15", "airline_r13", "$implicit", "ctx_r14", "toggleAirlineFilter", "code", "ctx_r1", "filters", "airlines", "includes", "name", "FlightResultsComponent_div_86_Template_button_click_8_listener", "_r17", "ctx_r16", "ctx_r5", "error", "FlightResultsComponent_div_87_div_1_Template_button_click_8_listener", "_r21", "ctx_r20", "clearFilters", "FlightResultsComponent_div_87_div_2_img_4_Template_img_error_0_listener", "_r34", "flight_r22", "ctx_r32", "onLogoError", "FlightResultsComponent_div_87_div_2_img_4_Template_img_load_0_listener", "ctx_r35", "onLogoLoad", "airline", "logo", "ɵɵsanitizeUrl", "ɵɵtextInterpolate2", "stops", "FlightResultsComponent_div_87_div_2_Template_div_click_0_listener", "_r41", "ctx_r40", "selectFlight", "FlightResultsComponent_div_87_div_2_img_4_Template", "FlightResultsComponent_div_87_div_2_div_5_Template", "FlightResultsComponent_div_87_div_2_span_14_Template", "FlightResultsComponent_div_87_div_2_span_15_Template", "FlightResultsComponent_div_87_div_2_span_16_Template", "FlightResultsComponent_div_87_div_2_span_17_Template", "FlightResultsComponent_div_87_div_2_span_18_Template", "FlightResultsComponent_div_87_div_2_div_33_Template", "FlightResultsComponent_div_87_div_2_Template_button_click_53_listener", "$event", "ctx_r42", "stopPropagation", "showFlightDetails", "FlightResultsComponent_div_87_div_2_span_76_Template", "logoError", "id", "split", "refundable", "class", "departure", "time", "airport", "city", "duration", "arrival", "price", "formatted", "baggage", "carryOn", "checked", "FlightResultsComponent_div_87_div_1_Template", "FlightResultsComponent_div_87_div_2_Template", "ɵɵclassProp", "ctx_r6", "viewMode", "filteredFlights", "length", "getPaginatedFlights", "trackByFlightId", "FlightResultsComponent_div_88_button_6_Template_button_click_0_listener", "_r46", "page_r44", "ctx_r45", "changePage", "ctx_r43", "currentPage", "FlightResultsComponent_div_88_Template_button_click_1_listener", "_r48", "ctx_r47", "FlightResultsComponent_div_88_button_6_Template", "FlightResultsComponent_div_88_Template_button_click_7_listener", "ctx_r49", "ctx_r7", "getPageNumbers", "totalPages", "FlightResultsComponent", "constructor", "flightSearchService", "router", "route", "airlineLogoService", "destroy$", "loading", "flights", "minPrice", "maxPrice", "currentPrice", "departureTime", "min", "max", "sortBy", "sortOrder", "showFilters", "itemsPerPage", "ngOnInit", "loadSearchResults", "initializeFilters", "ngOnDestroy", "next", "complete", "queryParams", "pipe", "subscribe", "params", "searchData", "JSON", "parse", "decodeURIComponent", "performSearch", "console", "navigate", "setTimeout", "processSearchResults", "generateMockResults", "response", "type", "departureLocation", "arrivalLocation", "passengers", "Date", "toLocaleTimeString", "extractFlights", "applyFilters", "updatePagination", "body", "for<PERSON>ach", "flight", "index", "push", "getAirlineLogo", "date", "amount", "currency", "formatPrice", "prices", "map", "f", "Math", "filter", "sortFlights", "sort", "a", "b", "comparison", "parseDuration", "localeCompare", "ceil", "startIndex", "endIndex", "slice", "page", "log", "modify", "stringify", "Intl", "NumberFormat", "style", "format", "match", "parseInt", "parts", "p", "adults", "children", "infants", "join", "getUniqueAirlines", "Map", "set", "Array", "entries", "airlineCode", "indexOf", "splice", "toggleStopsFilter", "toggleSortOrder", "pages", "maxVisible", "start", "floor", "end", "i", "getMinPrice", "getMaxPrice", "updateCurrentPrice", "ɵɵdirectiveInject", "i1", "FlightSearchService", "i2", "Router", "ActivatedRoute", "i3", "AirlineLogoService", "selectors", "decls", "vars", "consts", "template", "FlightResultsComponent_Template", "rf", "ctx", "FlightResultsComponent_div_3_Template", "FlightResultsComponent_Template_button_click_9_listener", "FlightResultsComponent_Template_input_ngModelChange_21_listener", "FlightResultsComponent_Template_input_input_21_listener", "target", "value", "FlightResultsComponent_label_31_Template", "FlightResultsComponent_Template_input_change_37_listener", "FlightResultsComponent_Template_input_change_42_listener", "FlightResultsComponent_Template_input_change_47_listener", "FlightResultsComponent_Template_button_click_52_listener", "FlightResultsComponent_Template_button_click_57_listener", "FlightResultsComponent_Template_button_click_62_listener", "FlightResultsComponent_Template_button_click_65_listener", "FlightResultsComponent_Template_select_ngModelChange_72_listener", "FlightResultsComponent_Template_select_change_72_listener", "FlightResultsComponent_Template_button_click_79_listener", "FlightResultsComponent__svg_path_81_Template", "FlightResultsComponent__svg_path_82_Template", "FlightResultsComponent_div_85_Template", "FlightResultsComponent_div_86_Template", "FlightResultsComponent_div_87_Template", "FlightResultsComponent_div_88_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-results\\flight-results.component.ts", "C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\components\\flight-results\\flight-results.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { Subject, takeUntil } from 'rxjs';\nimport { FlightSearchService } from '../../services/flight-search.service';\nimport { AirlineLogoService } from '../../services/airline-logo.service';\nimport { OneWayResponse } from '../../models/oneway-response.interface';\nimport { RoundTripResponse } from '../../models/roundtrip-response.interface';\nimport { MulticityResponse } from '../../models/multicity-response.interface';\n\nexport interface FlightResult {\n  id: string;\n  airline: {\n    code: string;\n    name: string;\n    logo?: string;\n    logoError?: boolean;\n  };\n  departure: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  arrival: {\n    airport: string;\n    city: string;\n    time: string;\n    date: string;\n  };\n  duration: string;\n  stops: number;\n  price: {\n    amount: number;\n    currency: string;\n    formatted: string;\n  };\n  class: string;\n  refundable: boolean;\n  baggage?: {\n    carryOn: string;\n    checked: string;\n  };\n}\n\nexport interface SearchSummary {\n  searchType: 'oneway' | 'roundtrip' | 'multicity';\n  from: string;\n  to: string;\n  departureDate: string;\n  returnDate?: string;\n  passengers: {\n    adults: number;\n    children: number;\n    infants: number;\n  };\n  totalResults: number;\n  searchTime: string;\n}\n\n@Component({\n  selector: 'app-flight-results',\n  templateUrl: './flight-results.component.html',\n  styleUrls: ['./flight-results.component.css']\n})\nexport class FlightResultsComponent implements OnInit, OnDestroy {\n  private destroy$ = new Subject<void>();\n  \n  // État du composant\n  loading = false;\n  error: string | null = null;\n  \n  // Données de recherche\n  searchSummary: SearchSummary | null = null;\n  flights: FlightResult[] = [];\n  filteredFlights: FlightResult[] = [];\n  \n  // Filtres et tri\n  filters = {\n    minPrice: 0,\n    maxPrice: 0,\n    currentPrice: 0,\n    airlines: [] as string[],\n    stops: [] as number[],\n    departureTime: { min: 0, max: 24 },\n    duration: { min: 0, max: 24 }\n  };\n  \n  sortBy = 'price'; // price, duration, departure\n  sortOrder = 'asc'; // asc, desc\n  \n  // Options d'affichage\n  viewMode = 'list'; // list, grid\n  showFilters = true;\n  \n  // Pagination\n  currentPage = 1;\n  itemsPerPage = 10;\n  totalPages = 1;\n\n  constructor(\n    private flightSearchService: FlightSearchService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private airlineLogoService: AirlineLogoService\n  ) {}\n\n  ngOnInit(): void {\n    this.loadSearchResults();\n    this.initializeFilters();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  /**\n   * Charge les résultats de recherche depuis le service\n   */\n  private loadSearchResults(): void {\n    // Récupérer les paramètres de recherche depuis l'URL ou le service\n    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {\n      if (params['searchData']) {\n        try {\n          const searchData = JSON.parse(decodeURIComponent(params['searchData']));\n          this.performSearch(searchData);\n        } catch (error) {\n          console.error('Erreur lors du parsing des données de recherche:', error);\n          this.router.navigate(['/search-flight']);\n        }\n      } else {\n        // Rediriger vers la page de recherche si aucune donnée\n        this.router.navigate(['/search-flight']);\n      }\n    });\n  }\n\n  /**\n   * Effectue la recherche de vols\n   */\n  private performSearch(searchData: any): void {\n    this.loading = true;\n    this.error = null;\n    \n    // Simuler une recherche pour le moment\n    setTimeout(() => {\n      this.processSearchResults(this.generateMockResults(), searchData);\n      this.loading = false;\n    }, 2000);\n  }\n\n  /**\n   * Traite les résultats de recherche\n   */\n  private processSearchResults(response: any, searchData: any): void {\n    // Créer le résumé de recherche\n    this.searchSummary = {\n      searchType: searchData.type,\n      from: searchData.params.departureLocation,\n      to: searchData.params.arrivalLocation,\n      departureDate: searchData.params.departureDate,\n      returnDate: searchData.params.returnDate,\n      passengers: searchData.params.passengers,\n      totalResults: 0,\n      searchTime: new Date().toLocaleTimeString()\n    };\n\n    // Traiter les vols selon le type de réponse\n    this.flights = this.extractFlights(response);\n    this.searchSummary.totalResults = this.flights.length;\n    \n    // Appliquer les filtres initiaux\n    this.applyFilters();\n    this.updatePagination();\n  }\n\n  /**\n   * Extrait les vols de la réponse API\n   */\n  private extractFlights(response: any): FlightResult[] {\n    // Cette méthode devra être adaptée selon la structure exacte de la réponse\n    const flights: FlightResult[] = [];\n    \n    // Exemple de traitement - à adapter selon la vraie structure\n    if (response.body?.flights) {\n      response.body.flights.forEach((flight: any, index: number) => {\n        flights.push({\n          id: flight.id || `flight-${index}`,\n          airline: {\n            code: flight.airline?.code || 'XX',\n            name: flight.airline?.name || 'Compagnie inconnue',\n            logo: flight.airline?.logo || this.airlineLogoService.getAirlineLogo(flight.airline?.code || '')\n          },\n          departure: {\n            airport: flight.departure?.airport || '',\n            city: flight.departure?.city || '',\n            time: flight.departure?.time || '',\n            date: flight.departure?.date || ''\n          },\n          arrival: {\n            airport: flight.arrival?.airport || '',\n            city: flight.arrival?.city || '',\n            time: flight.arrival?.time || '',\n            date: flight.arrival?.date || ''\n          },\n          duration: flight.duration || '0h 00m',\n          stops: flight.stops || 0,\n          price: {\n            amount: flight.price?.amount || 0,\n            currency: flight.price?.currency || 'EUR',\n            formatted: this.formatPrice(flight.price?.amount || 0)\n          },\n          class: flight.class || 'Economy',\n          refundable: flight.refundable || false,\n          baggage: flight.baggage\n        });\n      });\n    }\n    \n    return flights;\n  }\n\n  /**\n   * Initialise les filtres avec les valeurs par défaut\n   */\n  private initializeFilters(): void {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice; // Commencer avec le prix maximum\n\n      // Initialiser les filtres de compagnies aériennes (tous désélectionnés au début)\n      this.filters.airlines = [];\n\n      // Initialiser les filtres d'escales (tous désélectionnés au début)\n      this.filters.stops = [];\n    } else {\n      // Valeurs par défaut si aucun vol\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n  }\n\n  /**\n   * Applique les filtres aux vols\n   */\n  applyFilters(): void {\n    this.filteredFlights = this.flights.filter(flight => {\n      // Filtre par prix (utiliser currentPrice au lieu de maxPrice)\n      if (flight.price.amount > this.filters.currentPrice) return false;\n\n      // Filtre par compagnie aérienne (si des compagnies sont sélectionnées)\n      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {\n        return false;\n      }\n\n      // Filtre par nombre d'escales (si des escales sont sélectionnées)\n      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {\n        return false;\n      }\n\n      return true;\n    });\n\n    this.sortFlights();\n    this.updatePagination();\n  }\n\n  /**\n   * Trie les vols selon les critères sélectionnés\n   */\n  sortFlights(): void {\n    this.filteredFlights.sort((a, b) => {\n      let comparison = 0;\n      \n      switch (this.sortBy) {\n        case 'price':\n          comparison = a.price.amount - b.price.amount;\n          break;\n        case 'duration':\n          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);\n          break;\n        case 'departure':\n          comparison = a.departure.time.localeCompare(b.departure.time);\n          break;\n      }\n      \n      return this.sortOrder === 'asc' ? comparison : -comparison;\n    });\n  }\n\n  /**\n   * Met à jour la pagination\n   */\n  updatePagination(): void {\n    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);\n    if (this.currentPage > this.totalPages) {\n      this.currentPage = 1;\n    }\n  }\n\n  /**\n   * Obtient les vols pour la page actuelle\n   */\n  getPaginatedFlights(): FlightResult[] {\n    const startIndex = (this.currentPage - 1) * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    return this.filteredFlights.slice(startIndex, endIndex);\n  }\n\n  /**\n   * Change de page\n   */\n  changePage(page: number): void {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n    }\n  }\n\n  /**\n   * Sélectionne un vol\n   */\n  selectFlight(flight: FlightResult): void {\n    // Naviguer vers la page de détails ou de réservation\n    console.log('Vol sélectionné:', flight);\n    // TODO: Implémenter la navigation vers la page de réservation\n  }\n\n  /**\n   * Affiche les détails d'un vol\n   */\n  showFlightDetails(flight: FlightResult): void {\n    console.log('Affichage des détails du vol:', flight);\n    // TODO: Ouvrir un modal avec les détails complets du vol\n    // ou naviguer vers une page de détails\n  }\n\n  /**\n   * Gère l'erreur de chargement du logo\n   */\n  onLogoError(flight: FlightResult): void {\n    flight.airline.logoError = true;\n    console.log(`Erreur de chargement du logo pour ${flight.airline.code}`);\n  }\n\n  /**\n   * Gère le chargement réussi du logo\n   */\n  onLogoLoad(flight: FlightResult): void {\n    flight.airline.logoError = false;\n  }\n\n  /**\n   * Retourne à la recherche\n   */\n  backToSearch(): void {\n    this.router.navigate(['/search-flight']);\n  }\n\n  /**\n   * Modifie la recherche\n   */\n  modifySearch(): void {\n    // Pré-remplir le formulaire avec les critères actuels\n    this.router.navigate(['/search-flight'], {\n      queryParams: { modify: true, searchData: JSON.stringify(this.searchSummary) }\n    });\n  }\n\n  /**\n   * Formate le prix\n   */\n  private formatPrice(amount: number): string {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: 'EUR'\n    }).format(amount);\n  }\n\n  /**\n   * Parse la durée en minutes\n   */\n  private parseDuration(duration: string): number {\n    const match = duration.match(/(\\d+)h\\s*(\\d+)m/);\n    if (match) {\n      return parseInt(match[1]) * 60 + parseInt(match[2]);\n    }\n    return 0;\n  }\n\n  /**\n   * Obtient le texte des passagers\n   */\n  getPassengerText(): string {\n    if (!this.searchSummary) return '';\n\n    const parts: string[] = [];\n    const p = this.searchSummary.passengers;\n\n    if (p.adults > 0) {\n      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);\n    }\n    if (p.children > 0) {\n      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);\n    }\n    if (p.infants > 0) {\n      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);\n    }\n\n    return parts.join(', ');\n  }\n\n  /**\n   * Obtient les compagnies aériennes uniques\n   */\n  getUniqueAirlines(): { code: string; name: string }[] {\n    const airlines = new Map<string, string>();\n    this.flights.forEach(flight => {\n      airlines.set(flight.airline.code, flight.airline.name);\n    });\n\n    return Array.from(airlines.entries()).map(([code, name]) => ({ code, name }));\n  }\n\n  /**\n   * Toggle le filtre de compagnie aérienne\n   */\n  toggleAirlineFilter(airlineCode: string): void {\n    const index = this.filters.airlines.indexOf(airlineCode);\n    if (index > -1) {\n      this.filters.airlines.splice(index, 1);\n    } else {\n      this.filters.airlines.push(airlineCode);\n    }\n    this.applyFilters();\n  }\n\n  /**\n   * Toggle le filtre d'escales\n   */\n  toggleStopsFilter(stops: number): void {\n    const index = this.filters.stops.indexOf(stops);\n    if (index > -1) {\n      this.filters.stops.splice(index, 1);\n    } else {\n      this.filters.stops.push(stops);\n    }\n    this.applyFilters();\n  }\n\n  /**\n   * Efface tous les filtres\n   */\n  clearFilters(): void {\n    if (this.flights.length > 0) {\n      const prices = this.flights.map(f => f.price.amount);\n      this.filters.minPrice = Math.min(...prices);\n      this.filters.maxPrice = Math.max(...prices);\n      this.filters.currentPrice = this.filters.maxPrice;\n    } else {\n      this.filters.minPrice = 0;\n      this.filters.maxPrice = 1000;\n      this.filters.currentPrice = 1000;\n    }\n\n    this.filters.airlines = [];\n    this.filters.stops = [];\n    this.filters.departureTime = { min: 0, max: 24 };\n    this.filters.duration = { min: 0, max: 24 };\n\n    this.applyFilters();\n  }\n\n  /**\n   * Toggle l'ordre de tri\n   */\n  toggleSortOrder(): void {\n    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';\n    this.sortFlights();\n  }\n\n  /**\n   * Obtient les numéros de page pour la pagination\n   */\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n  /**\n   * TrackBy function pour la performance\n   */\n  trackByFlightId(index: number, flight: FlightResult): string {\n    return flight.id;\n  }\n\n  /**\n   * Obtient le prix minimum pour le slider\n   */\n  getMinPrice(): number {\n    if (this.flights.length === 0) return 0;\n    return Math.min(...this.flights.map(f => f.price.amount));\n  }\n\n  /**\n   * Obtient le prix maximum pour le slider\n   */\n  getMaxPrice(): number {\n    if (this.flights.length === 0) return 1000;\n    return Math.max(...this.flights.map(f => f.price.amount));\n  }\n\n  /**\n   * Met à jour le prix courant du filtre\n   */\n  updateCurrentPrice(price: number): void {\n    this.filters.currentPrice = price;\n    this.applyFilters();\n  }\n\n  /**\n   * Génère des résultats de test\n   */\n  private generateMockResults(): any {\n    return {\n      body: {\n        flights: [\n          {\n            id: 'flight-1',\n            airline: { code: 'TK', name: 'Turkish Airlines' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },\n            duration: '3h 15m',\n            stops: 0,\n            price: { amount: 450, currency: 'EUR' },\n            class: 'Economy',\n            refundable: true,\n            baggage: { carryOn: '8kg', checked: '23kg' }\n          },\n          {\n            id: 'flight-2',\n            airline: { code: 'TU', name: 'Tunisair' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '14:20', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '17:35', date: '2025-06-17' },\n            duration: '3h 15m',\n            stops: 0,\n            price: { amount: 380, currency: 'EUR' },\n            class: 'Economy',\n            refundable: false,\n            baggage: { carryOn: '8kg', checked: '20kg' }\n          },\n          {\n            id: 'flight-3',\n            airline: { code: 'AF', name: 'Air France' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '10:15', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '16:30', date: '2025-06-17' },\n            duration: '6h 15m',\n            stops: 1,\n            price: { amount: 520, currency: 'EUR' },\n            class: 'Economy',\n            refundable: true,\n            baggage: { carryOn: '12kg', checked: '23kg' }\n          },\n          {\n            id: 'flight-4',\n            airline: { code: 'LH', name: 'Lufthansa' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '16:45', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '22:10', date: '2025-06-17' },\n            duration: '5h 25m',\n            stops: 1,\n            price: { amount: 610, currency: 'EUR' },\n            class: 'Business',\n            refundable: true,\n            baggage: { carryOn: '8kg', checked: '32kg' }\n          },\n          {\n            id: 'flight-5',\n            airline: { code: 'EK', name: 'Emirates' },\n            departure: { airport: 'IST', city: 'Istanbul', time: '22:30', date: '2025-06-17' },\n            arrival: { airport: 'TUN', city: 'Tunis', time: '08:45', date: '2025-06-18' },\n            duration: '10h 15m',\n            stops: 1,\n            price: { amount: 750, currency: 'EUR' },\n            class: 'Business',\n            refundable: true,\n            baggage: { carryOn: '7kg', checked: '30kg' }\n          }\n        ]\n      }\n    };\n  }\n}\n", "<div class=\"flight-results-container\">\n  <!-- Header avec r<PERSON> de recherche -->\n  <div class=\"results-header\">\n    <div class=\"header-content\">\n      <div class=\"search-summary\" *ngIf=\"searchSummary\">\n        <div class=\"route-info\">\n          <div class=\"route-main\">\n            <span class=\"airport-code\">{{ searchSummary?.from }}</span>\n            <div class=\"route-arrow\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n              </svg>\n              <svg *ngIf=\"searchSummary?.searchType === 'roundtrip'\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\" transform=\"rotate(180 12 12)\"/>\n              </svg>\n            </div>\n            <span class=\"airport-code\">{{ searchSummary?.to }}</span>\n          </div>\n          <div class=\"route-details\">\n            <span class=\"date\">{{ searchSummary?.departureDate }}</span>\n            <span *ngIf=\"searchSummary?.returnDate\" class=\"date\"> - {{ searchSummary?.returnDate }}</span>\n            <span class=\"passengers\">{{ getPassengerText() }}</span>\n          </div>\n        </div>\n\n        <div class=\"results-count\">\n          <span class=\"count\">{{ searchSummary?.totalResults }} vols trouvés</span>\n          <span class=\"search-time\">Recherche effectuée à {{ searchSummary?.searchTime }}</span>\n        </div>\n        \n        <div class=\"header-actions\">\n          <button class=\"btn-secondary\" (click)=\"modifySearch()\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n            </svg>\n            Modifier la recherche\n          </button>\n          <button class=\"btn-primary\" (click)=\"backToSearch()\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z\"/>\n            </svg>\n            Nouvelle recherche\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Contenu principal -->\n  <div class=\"results-content\">\n    <!-- Sidebar avec filtres -->\n    <aside class=\"filters-sidebar\" [class.hidden]=\"!showFilters\">\n      <div class=\"filters-header\">\n        <h3>Filtres</h3>\n        <button class=\"btn-toggle\" (click)=\"showFilters = !showFilters\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n          </svg>\n        </button>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Prix maximum</h4>\n        <div class=\"price-filter\">\n          <div class=\"price-range-info\">\n            <span class=\"price-min\">{{ getMinPrice() }}€</span>\n            <span class=\"price-max\">{{ getMaxPrice() }}€</span>\n          </div>\n          <input type=\"range\"\n                 [(ngModel)]=\"filters.currentPrice\"\n                 [min]=\"getMinPrice()\"\n                 [max]=\"getMaxPrice()\"\n                 [step]=\"10\"\n                 (input)=\"updateCurrentPrice($any($event.target).value)\"\n                 class=\"price-slider\">\n          <div class=\"price-current\">\n            <span class=\"price-label\">Prix maximum:</span>\n            <span class=\"price-value\">{{ filters.currentPrice }}€</span>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Compagnies aériennes</h4>\n        <div class=\"airline-filters\">\n          <label *ngFor=\"let airline of getUniqueAirlines()\" class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   [value]=\"airline.code\"\n                   (change)=\"toggleAirlineFilter(airline.code)\"\n                   [checked]=\"filters.airlines.includes(airline.code)\">\n            <span class=\"checkmark\"></span>\n            <span class=\"airline-name\">{{ airline.name }}</span>\n          </label>\n        </div>\n      </div>\n      \n      <div class=\"filter-section\">\n        <h4>Nombre d'escales</h4>\n        <div class=\"stops-filters\">\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"0\"\n                   (change)=\"toggleStopsFilter(0)\"\n                   [checked]=\"filters.stops.includes(0)\">\n            <span class=\"checkmark\"></span>\n            <span>Vol direct</span>\n          </label>\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"1\"\n                   (change)=\"toggleStopsFilter(1)\"\n                   [checked]=\"filters.stops.includes(1)\">\n            <span class=\"checkmark\"></span>\n            <span>1 escale</span>\n          </label>\n          <label class=\"checkbox-label\">\n            <input type=\"checkbox\" \n                   value=\"2\"\n                   (change)=\"toggleStopsFilter(2)\"\n                   [checked]=\"filters.stops.includes(2)\">\n            <span class=\"checkmark\"></span>\n            <span>2+ escales</span>\n          </label>\n        </div>\n      </div>\n      \n      <div class=\"filter-actions\">\n        <button class=\"btn-clear\" (click)=\"clearFilters()\">Effacer les filtres</button>\n      </div>\n    </aside>\n\n    <!-- Zone des résultats -->\n    <main class=\"results-main\">\n      <!-- Barre d'outils -->\n      <div class=\"results-toolbar\">\n        <div class=\"toolbar-left\">\n          <button class=\"btn-filter-toggle\" (click)=\"showFilters = !showFilters\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M3 18h6v-2H3v2zM3 6v2h10V6H3zm0 7h8v-2H3v2zm16-1v8h2v-8h-2zm-3-3h2V6h-2v3zm3-3v3h2V6h-2z\"/>\n            </svg>\n            Filtres\n          </button>\n          \n          <div class=\"view-toggle\">\n            <button class=\"view-btn\" \n                    [class.active]=\"viewMode === 'list'\"\n                    (click)=\"viewMode = 'list'\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z\"/>\n              </svg>\n            </button>\n            <button class=\"view-btn\" \n                    [class.active]=\"viewMode === 'grid'\"\n                    (click)=\"viewMode = 'grid'\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z\"/>\n              </svg>\n            </button>\n          </div>\n        </div>\n        \n        <div class=\"toolbar-right\">\n          <div class=\"sort-controls\">\n            <label>Trier par:</label>\n            <select [(ngModel)]=\"sortBy\" (change)=\"sortFlights(); applyFilters()\">\n              <option value=\"price\">Prix</option>\n              <option value=\"duration\">Durée</option>\n              <option value=\"departure\">Heure de départ</option>\n            </select>\n            <button class=\"sort-order-btn\" (click)=\"toggleSortOrder()\">\n              <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path *ngIf=\"sortOrder === 'asc'\" d=\"M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z\"/>\n                <path *ngIf=\"sortOrder === 'desc'\" d=\"M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z\"/>\n              </svg>\n            </button>\n          </div>\n          \n          <span class=\"results-info\">\n            {{ filteredFlights.length }} résultats\n          </span>\n        </div>\n      </div>\n\n      <!-- État de chargement -->\n      <div *ngIf=\"loading\" class=\"loading-state\">\n        <div class=\"loading-spinner\"></div>\n        <p>Recherche de vols en cours...</p>\n      </div>\n\n      <!-- État d'erreur -->\n      <div *ngIf=\"error\" class=\"error-state\">\n        <div class=\"error-icon\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n          </svg>\n        </div>\n        <h3>Erreur lors de la recherche</h3>\n        <p>{{ error }}</p>\n        <button class=\"btn-primary\" (click)=\"backToSearch()\">Retour à la recherche</button>\n      </div>\n\n      <!-- Liste des vols -->\n      <div *ngIf=\"!loading && !error\" class=\"flights-container\" [class.grid-view]=\"viewMode === 'grid'\">\n        <div *ngIf=\"filteredFlights.length === 0\" class=\"no-results\">\n          <div class=\"no-results-icon\">\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n            </svg>\n          </div>\n          <h3>Aucun vol trouvé</h3>\n          <p>Essayez de modifier vos critères de recherche ou vos filtres.</p>\n          <button class=\"btn-primary\" (click)=\"clearFilters()\">Effacer les filtres</button>\n        </div>\n\n        <div *ngFor=\"let flight of getPaginatedFlights(); trackBy: trackByFlightId\" \n             class=\"flight-card\"\n             (click)=\"selectFlight(flight)\">\n          \n          <div class=\"flight-header\">\n            <div class=\"airline-info\">\n              <div class=\"airline-logo-container\">\n                <img *ngIf=\"flight.airline.logo && !flight.airline.logoError\"\n                     [src]=\"flight.airline.logo\"\n                     [alt]=\"flight.airline.name\"\n                     class=\"airline-logo\"\n                     (error)=\"onLogoError(flight)\"\n                     (load)=\"onLogoLoad(flight)\">\n                <div *ngIf=\"!flight.airline.logo || flight.airline.logoError\" class=\"airline-placeholder\">\n                  {{ flight.airline.code }}\n                </div>\n              </div>\n              <div class=\"airline-details\">\n                <span class=\"airline-name\">{{ flight.airline.name }}</span>\n                <span class=\"flight-number\">{{ flight.airline.code }} {{ flight.id.split('-')[1] || '1234' }}</span>\n                <span class=\"aircraft-type\">Boeing 737-800</span>\n              </div>\n            </div>\n\n            <div class=\"flight-badges\">\n              <span *ngIf=\"flight.stops === 0\" class=\"badge direct\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 0L6.5 1.5L10 5H3v2h7l-3.5 3.5L8 12l6-6L8 0z\"/>\n                </svg>\n                Direct\n              </span>\n              <span *ngIf=\"flight.refundable\" class=\"badge refundable\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"/>\n                  <path d=\"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"/>\n                </svg>\n                Remboursable\n              </span>\n              <span class=\"badge economy\" *ngIf=\"flight.class === 'Economy'\">Économique</span>\n              <span class=\"badge business\" *ngIf=\"flight.class === 'Business'\">Affaires</span>\n              <span class=\"badge first\" *ngIf=\"flight.class === 'First'\">Première</span>\n            </div>\n          </div>\n\n          <div class=\"flight-details\">\n            <div class=\"flight-route\">\n              <div class=\"departure\">\n                <div class=\"time\">{{ flight.departure.time }}</div>\n                <div class=\"airport\">{{ flight.departure.airport }}</div>\n                <div class=\"city\">{{ flight.departure.city }}</div>\n              </div>\n              \n              <div class=\"flight-info\">\n                <div class=\"duration\">{{ flight.duration }}</div>\n                <div class=\"route-line\">\n                  <div class=\"line\"></div>\n                  <div *ngIf=\"flight.stops > 0\" class=\"stops\">\n                    {{ flight.stops }} escale{{ flight.stops > 1 ? 's' : '' }}\n                  </div>\n                  <div class=\"plane-icon\">\n                    <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z\"/>\n                    </svg>\n                  </div>\n                </div>\n              </div>\n              \n              <div class=\"arrival\">\n                <div class=\"time\">{{ flight.arrival.time }}</div>\n                <div class=\"airport\">{{ flight.arrival.airport }}</div>\n                <div class=\"city\">{{ flight.arrival.city }}</div>\n              </div>\n            </div>\n            \n            <div class=\"flight-price\">\n              <div class=\"price-container\">\n                <div class=\"price-amount\">{{ flight.price.formatted }}</div>\n                <div class=\"price-details\">par personne</div>\n                <div class=\"price-taxes\">TTC</div>\n              </div>\n              <div class=\"action-buttons\">\n                <button class=\"btn-details\" (click)=\"$event.stopPropagation(); showFlightDetails(flight)\">\n                  Détails\n                </button>\n                <button class=\"btn-select\">Sélectionner</button>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"flight-baggage\">\n            <div class=\"baggage-info\">\n              <div class=\"baggage-item carry-on\">\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M8.5 6h7l1.5 9H7l1.5-9zM10 4V2h4v2h-4z\"/>\n                </svg>\n                <div class=\"baggage-details\">\n                  <span class=\"baggage-type\">Bagage cabine</span>\n                  <span class=\"baggage-allowance\">{{ flight.baggage?.carryOn || '1 x 8kg' }}</span>\n                </div>\n              </div>\n              <div class=\"baggage-item checked\">\n                <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z\"/>\n                </svg>\n                <div class=\"baggage-details\">\n                  <span class=\"baggage-type\">Bagage soute</span>\n                  <span class=\"baggage-allowance\">{{ flight.baggage?.checked || '1 x 23kg' }}</span>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flight-extras\">\n              <span class=\"extra-item\" *ngIf=\"flight.refundable\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z\"/>\n                  <path d=\"M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z\"/>\n                </svg>\n                Remboursable\n              </span>\n              <span class=\"extra-item\">\n                <svg viewBox=\"0 0 16 16\" fill=\"currentColor\">\n                  <path d=\"M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z\"/>\n                </svg>\n                Modifiable\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pagination -->\n      <div *ngIf=\"totalPages > 1\" class=\"pagination\">\n        <button class=\"page-btn\" \n                [disabled]=\"currentPage === 1\"\n                (click)=\"changePage(currentPage - 1)\">\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n          </svg>\n          Précédent\n        </button>\n        \n        <div class=\"page-numbers\">\n          <button *ngFor=\"let page of getPageNumbers()\" \n                  class=\"page-number\"\n                  [class.active]=\"page === currentPage\"\n                  (click)=\"changePage(page)\">\n            {{ page }}\n          </button>\n        </div>\n        \n        <button class=\"page-btn\" \n                [disabled]=\"currentPage === totalPages\"\n                (click)=\"changePage(currentPage + 1)\">\n          Suivant\n          <svg viewBox=\"0 0 24 24\" fill=\"currentColor\">\n            <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n          </svg>\n        </button>\n      </div>\n    </main>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICU3BC,EAAA,CAAAC,cAAA,aAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAgK;IAClKF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMRH,EAAA,CAAAC,cAAA,eAAqD;IAACD,EAAA,CAAAI,MAAA,GAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAM,kBAAA,QAAAC,MAAA,CAAAC,aAAA,kBAAAD,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAiC;;;;;;IAhB7FT,EAAA,CAAAC,cAAA,cAAkD;IAGjBD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAW,UAAA,IAAAC,gDAAA,kBAEM;IACRZ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA2B;IAA3Bb,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,IAAuB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAC,cAAA,eAA2B;IACND,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAW,UAAA,KAAAG,6CAAA,mBAA8F;IAC9Fd,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAI5DH,EAAA,CAAAC,cAAA,eAA2B;IACLD,EAAA,CAAAI,MAAA,IAA8C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACzEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAqD;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGxFH,EAAA,CAAAC,cAAA,eAA4B;IACID,EAAA,CAAAe,UAAA,mBAAAC,+DAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACpDtB,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiK;IACnKF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,+BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAa,eAAA,EAAqD;IAArDb,EAAA,CAAAC,cAAA,kBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAQ,+DAAA;MAAAvB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAM,OAAA,GAAAxB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAG,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAClDzB,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAwE;IAC1EF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IAnCoBH,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAoB,IAAA,CAAyB;IAK5C5B,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAA6B,UAAA,UAAAF,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAsB,UAAA,kBAA+C;IAI5B9B,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAuB,EAAA,CAAuB;IAG/B/B,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAwB,aAAA,CAAkC;IAC9ChC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAA6B,UAAA,SAAAF,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAAC,UAAA,CAA+B;IACbT,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA0B,iBAAA,CAAAC,MAAA,CAAAM,gBAAA,GAAwB;IAK/BjC,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAM,kBAAA,KAAAqB,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAA0B,YAAA,uBAA8C;IACxClC,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAM,kBAAA,qCAAAqB,MAAA,CAAAnB,aAAA,kBAAAmB,MAAA,CAAAnB,aAAA,CAAA2B,UAAA,KAAqD;;;;;;IA0D/EnC,EAAA,CAAAC,cAAA,gBAA0E;IAGjED,EAAA,CAAAe,UAAA,oBAAAqB,iEAAA;MAAA,MAAAC,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAqB,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAoB,aAAA;MAAA,OAAUpB,EAAA,CAAAqB,WAAA,CAAAoB,OAAA,CAAAC,mBAAA,CAAAH,WAAA,CAAAI,IAAA,CAAiC;IAAA,EAAC;IAFnD3C,EAAA,CAAAG,YAAA,EAG2D;IAC3DH,EAAA,CAAAE,SAAA,eAA+B;IAC/BF,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAJ7CH,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,UAAAU,WAAA,CAAAI,IAAA,CAAsB,YAAAC,MAAA,CAAAC,OAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAR,WAAA,CAAAI,IAAA;IAIF3C,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAA0B,iBAAA,CAAAa,WAAA,CAAAS,IAAA,CAAkB;;;;;;IAgFzChD,EAAA,CAAAE,SAAA,eAAoF;;;;;;IACpFF,EAAA,CAAAE,SAAA,eAAoF;;;;;IAY9FF,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,oCAA6B;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IAItCH,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAiI;IACnIF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAa,eAAA,EAAI;IAAJb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,kCAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,GAAW;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IAClBH,EAAA,CAAAC,cAAA,iBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAkC,+DAAA;MAAAjD,EAAA,CAAAiB,aAAA,CAAAiC,IAAA;MAAA,MAAAC,OAAA,GAAAnD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAA8B,OAAA,CAAA1B,YAAA,EAAc;IAAA,EAAC;IAACzB,EAAA,CAAAI,MAAA,iCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;IADhFH,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAA0B,iBAAA,CAAA0B,MAAA,CAAAC,KAAA,CAAW;;;;;;IAMdrD,EAAA,CAAAC,cAAA,cAA6D;IAEzDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,eAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAa,eAAA,EAAI;IAAJb,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,4BAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,yEAA6D;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACpEH,EAAA,CAAAC,cAAA,iBAAqD;IAAzBD,EAAA,CAAAe,UAAA,mBAAAuC,qEAAA;MAAAtD,EAAA,CAAAiB,aAAA,CAAAsC,IAAA;MAAA,MAAAC,OAAA,GAAAxD,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAmC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAACzD,EAAA,CAAAI,MAAA,0BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAU3EH,EAAA,CAAAC,cAAA,eAKiC;IAD5BD,EAAA,CAAAe,UAAA,mBAAA2C,wEAAA;MAAA1D,EAAA,CAAAiB,aAAA,CAAA0C,IAAA;MAAA,MAAAC,UAAA,GAAA5D,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAAqB,OAAA,GAAA7D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAwC,OAAA,CAAAC,WAAA,CAAAF,UAAA,CAAmB;IAAA,EAAC,kBAAAG,uEAAA;MAAA/D,EAAA,CAAAiB,aAAA,CAAA0C,IAAA;MAAA,MAAAC,UAAA,GAAA5D,EAAA,CAAAoB,aAAA,GAAAoB,SAAA;MAAA,MAAAwB,OAAA,GAAAhE,EAAA,CAAAoB,aAAA;MAAA,OACrBpB,EAAA,CAAAqB,WAAA,CAAA2C,OAAA,CAAAC,UAAA,CAAAL,UAAA,CAAkB;IAAA,EADG;IAJlC5D,EAAA,CAAAG,YAAA,EAKiC;;;;IAJ5BH,EAAA,CAAA6B,UAAA,QAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,EAAAnE,EAAA,CAAAoE,aAAA,CAA2B,QAAAR,UAAA,CAAAM,OAAA,CAAAlB,IAAA;;;;;IAKhChD,EAAA,CAAAC,cAAA,eAA0F;IACxFD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAAsD,UAAA,CAAAM,OAAA,CAAAvB,IAAA,MACF;;;;;IAUF3C,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAA0D;IAC5DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAAyD;IACvDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiF;IAEnFF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,gBAA+D;IAAAD,EAAA,CAAAI,MAAA,sBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAChFH,EAAA,CAAAC,cAAA,gBAAiE;IAAAD,EAAA,CAAAI,MAAA,eAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAChFH,EAAA,CAAAC,cAAA,gBAA2D;IAAAD,EAAA,CAAAI,MAAA,oBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;IAgBtEH,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAqE,kBAAA,MAAAT,UAAA,CAAAU,KAAA,aAAAV,UAAA,CAAAU,KAAA,qBACF;;;;;IAsDJtE,EAAA,CAAAC,cAAA,gBAAmD;IACjDD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,eAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAiF;IAEnFF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,qBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;;;IAtHbH,EAAA,CAAAC,cAAA,cAEoC;IAA/BD,EAAA,CAAAe,UAAA,mBAAAwD,kEAAA;MAAA,MAAAlC,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAuD,IAAA;MAAA,MAAAZ,UAAA,GAAAvB,WAAA,CAAAG,SAAA;MAAA,MAAAiC,OAAA,GAAAzE,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAoD,OAAA,CAAAC,YAAA,CAAAd,UAAA,CAAoB;IAAA,EAAC;IAEjC5D,EAAA,CAAAC,cAAA,cAA2B;IAGrBD,EAAA,CAAAW,UAAA,IAAAgE,kDAAA,kBAKiC;IACjC3E,EAAA,CAAAW,UAAA,IAAAiE,kDAAA,kBAEM;IACR5E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA6B;IACAD,EAAA,CAAAI,MAAA,GAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC3DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAI,MAAA,IAAiE;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IACpGH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAI,MAAA,sBAAc;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAAkE,oDAAA,mBAKO;IACP7E,EAAA,CAAAW,UAAA,KAAAmE,oDAAA,mBAMO;IACP9E,EAAA,CAAAW,UAAA,KAAAoE,oDAAA,mBAAgF;IAChF/E,EAAA,CAAAW,UAAA,KAAAqE,oDAAA,mBAAgF;IAChFhF,EAAA,CAAAW,UAAA,KAAAsE,oDAAA,oBAA0E;IAC5EjF,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,gBAA4B;IAGJD,EAAA,CAAAI,MAAA,IAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAI,MAAA,IAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAGrDH,EAAA,CAAAC,cAAA,gBAAyB;IACDD,EAAA,CAAAI,MAAA,IAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAE,SAAA,gBAAwB;IACxBF,EAAA,CAAAW,UAAA,KAAAuE,mDAAA,mBAEM;IACNlF,EAAA,CAAAC,cAAA,gBAAwB;IACtBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAkI;IACpIF,EAAA,CAAAG,YAAA,EAAM;IAKZH,EAAA,CAAAa,eAAA,EAAqB;IAArBb,EAAA,CAAAC,cAAA,gBAAqB;IACDD,EAAA,CAAAI,MAAA,IAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,gBAAqB;IAAAD,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAI,MAAA,IAAyB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAIrDH,EAAA,CAAAC,cAAA,gBAA0B;IAEID,EAAA,CAAAI,MAAA,IAA4B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAI,MAAA,WAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEpCH,EAAA,CAAAC,cAAA,gBAA4B;IACED,EAAA,CAAAe,UAAA,mBAAAoE,sEAAAC,MAAA;MAAA,MAAA/C,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAuD,IAAA;MAAA,MAAAZ,UAAA,GAAAvB,WAAA,CAAAG,SAAA;MAAA,MAAA6C,OAAA,GAAArF,EAAA,CAAAoB,aAAA;MAASgE,MAAA,CAAAE,eAAA,EAAwB;MAAA,OAAEtF,EAAA,CAAAqB,WAAA,CAAAgE,OAAA,CAAAE,iBAAA,CAAA3B,UAAA,CAAyB;IAAA,EAAC;IACvF5D,EAAA,CAAAI,MAAA,sBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAI,MAAA,yBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAKtDH,EAAA,CAAAC,cAAA,gBAA4B;IAGtBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAAkD;IACpDF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA6B;IAA7Bb,EAAA,CAAAC,cAAA,gBAA6B;IACAD,EAAA,CAAAI,MAAA,qBAAa;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC/CH,EAAA,CAAAC,cAAA,iBAAgC;IAAAD,EAAA,CAAAI,MAAA,IAA0C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAGrFH,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,cAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAAmI;IACrIF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,eAAA,EAA6B;IAA7Bb,EAAA,CAAAC,cAAA,gBAA6B;IACAD,EAAA,CAAAI,MAAA,oBAAY;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,iBAAgC;IAAAD,EAAA,CAAAI,MAAA,IAA2C;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAKxFH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAW,UAAA,KAAA6E,oDAAA,oBAMO;IACPxF,EAAA,CAAAC,cAAA,iBAAyB;IACvBD,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAA2K;IAC7KF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,oBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;;;;IArHCH,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,KAAAP,UAAA,CAAAM,OAAA,CAAAuB,SAAA,CAAsD;IAMtDzF,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAA6B,UAAA,UAAA+B,UAAA,CAAAM,OAAA,CAAAC,IAAA,IAAAP,UAAA,CAAAM,OAAA,CAAAuB,SAAA,CAAsD;IAKjCzF,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAM,OAAA,CAAAlB,IAAA,CAAyB;IACxBhD,EAAA,CAAAK,SAAA,GAAiE;IAAjEL,EAAA,CAAAqE,kBAAA,KAAAT,UAAA,CAAAM,OAAA,CAAAvB,IAAA,OAAAiB,UAAA,CAAA8B,EAAA,CAAAC,KAAA,uBAAiE;IAMxF3F,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAU,KAAA,OAAwB;IAMxBtE,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAgC,UAAA,CAAuB;IAOD5F,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAiC,KAAA,eAAgC;IAC/B7F,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAiC,KAAA,gBAAiC;IACpC7F,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAiC,KAAA,aAA8B;IAOrC7F,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAkC,SAAA,CAAAC,IAAA,CAA2B;IACxB/F,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAkC,SAAA,CAAAE,OAAA,CAA8B;IACjChG,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAkC,SAAA,CAAAG,IAAA,CAA2B;IAIvBjG,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAsC,QAAA,CAAqB;IAGnClG,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAU,KAAA,KAAsB;IAYZtE,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAuC,OAAA,CAAAJ,IAAA,CAAyB;IACtB/F,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAuC,OAAA,CAAAH,OAAA,CAA4B;IAC/BhG,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAuC,OAAA,CAAAF,IAAA,CAAyB;IAMjBjG,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA0B,iBAAA,CAAAkC,UAAA,CAAAwC,KAAA,CAAAC,SAAA,CAA4B;IAqBpBrG,EAAA,CAAAK,SAAA,IAA0C;IAA1CL,EAAA,CAAA0B,iBAAA,EAAAkC,UAAA,CAAA0C,OAAA,kBAAA1C,UAAA,CAAA0C,OAAA,CAAAC,OAAA,eAA0C;IAS1CvG,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAA0B,iBAAA,EAAAkC,UAAA,CAAA0C,OAAA,kBAAA1C,UAAA,CAAA0C,OAAA,CAAAE,OAAA,gBAA2C;IAMrDxG,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA6B,UAAA,SAAA+B,UAAA,CAAAgC,UAAA,CAAuB;;;;;IA5HzD5F,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAW,UAAA,IAAA8F,4CAAA,mBASM;IAENzG,EAAA,CAAAW,UAAA,IAAA+F,4CAAA,oBA+HM;IACR1G,EAAA,CAAAG,YAAA,EAAM;;;;IA5IoDH,EAAA,CAAA2G,WAAA,cAAAC,MAAA,CAAAC,QAAA,YAAuC;IACzF7G,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA6B,UAAA,SAAA+E,MAAA,CAAAE,eAAA,CAAAC,MAAA,OAAkC;IAWhB/G,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAA6B,UAAA,YAAA+E,MAAA,CAAAI,mBAAA,GAA0B,iBAAAJ,MAAA,CAAAK,eAAA;;;;;;IA8IhDjH,EAAA,CAAAC,cAAA,kBAGmC;IAA3BD,EAAA,CAAAe,UAAA,mBAAAmG,wEAAA;MAAA,MAAA7E,WAAA,GAAArC,EAAA,CAAAiB,aAAA,CAAAkG,IAAA;MAAA,MAAAC,QAAA,GAAA/E,WAAA,CAAAG,SAAA;MAAA,MAAA6E,OAAA,GAAArH,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAgG,OAAA,CAAAC,UAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAChCpH,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAHDH,EAAA,CAAA2G,WAAA,WAAAS,QAAA,KAAAG,OAAA,CAAAC,WAAA,CAAqC;IAE3CxH,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAM,kBAAA,MAAA8G,QAAA,MACF;;;;;;IAhBJpH,EAAA,CAAAC,cAAA,eAA+C;IAGrCD,EAAA,CAAAe,UAAA,mBAAA0G,+DAAA;MAAAzH,EAAA,CAAAiB,aAAA,CAAAyG,IAAA;MAAA,MAAAC,OAAA,GAAA3H,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsG,OAAA,CAAAL,UAAA,CAAAK,OAAA,CAAAH,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC3CxH,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,gBAAyD;IAC3DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,MAAA,4BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAa,eAAA,EAA0B;IAA1Bb,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAW,UAAA,IAAAiH,+CAAA,sBAKS;IACX5H,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,kBAE8C;IAAtCD,EAAA,CAAAe,UAAA,mBAAA8G,+DAAA;MAAA7H,EAAA,CAAAiB,aAAA,CAAAyG,IAAA;MAAA,MAAAI,OAAA,GAAA9H,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAyG,OAAA,CAAAR,UAAA,CAAAQ,OAAA,CAAAN,WAAA,GAAyB,CAAC,CAAC;IAAA,EAAC;IAC3CxH,EAAA,CAAAI,MAAA,gBACA;IAAAJ,EAAA,CAAAU,cAAA,EAA6C;IAA7CV,EAAA,CAAAC,cAAA,aAA6C;IAC3CD,EAAA,CAAAE,SAAA,iBAA0D;IAC5DF,EAAA,CAAAG,YAAA,EAAM;;;;IAvBAH,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA6B,UAAA,aAAAkG,MAAA,CAAAP,WAAA,OAA8B;IASXxH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAA6B,UAAA,YAAAkG,MAAA,CAAAC,cAAA,GAAmB;IAStChI,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAA6B,UAAA,aAAAkG,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAE,UAAA,CAAuC;;;AD7SvD,OAAM,MAAOC,sBAAsB;EAmCjCC,YACUC,mBAAwC,EACxCC,MAAc,EACdC,KAAqB,EACrBC,kBAAsC;IAHtC,KAAAH,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAtCpB,KAAAC,QAAQ,GAAG,IAAI1I,OAAO,EAAQ;IAEtC;IACA,KAAA2I,OAAO,GAAG,KAAK;IACf,KAAApF,KAAK,GAAkB,IAAI;IAE3B;IACA,KAAA7C,aAAa,GAAyB,IAAI;IAC1C,KAAAkI,OAAO,GAAmB,EAAE;IAC5B,KAAA5B,eAAe,GAAmB,EAAE;IAEpC;IACA,KAAAjE,OAAO,GAAG;MACR8F,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC;MACXC,YAAY,EAAE,CAAC;MACf/F,QAAQ,EAAE,EAAc;MACxBwB,KAAK,EAAE,EAAc;MACrBwE,aAAa,EAAE;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAE;MAClC9C,QAAQ,EAAE;QAAE6C,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE;KAC5B;IAED,KAAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAClB,KAAAC,SAAS,GAAG,KAAK,CAAC,CAAC;IAEnB;IACA,KAAArC,QAAQ,GAAG,MAAM,CAAC,CAAC;IACnB,KAAAsC,WAAW,GAAG,IAAI;IAElB;IACA,KAAA3B,WAAW,GAAG,CAAC;IACf,KAAA4B,YAAY,GAAG,EAAE;IACjB,KAAAnB,UAAU,GAAG,CAAC;EAOX;EAEHoB,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChB,QAAQ,CAACiB,IAAI,EAAE;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,EAAE;EAC1B;EAEA;;;EAGQJ,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAChB,KAAK,CAACqB,WAAW,CAACC,IAAI,CAAC7J,SAAS,CAAC,IAAI,CAACyI,QAAQ,CAAC,CAAC,CAACqB,SAAS,CAACC,MAAM,IAAG;MACvE,IAAIA,MAAM,CAAC,YAAY,CAAC,EAAE;QACxB,IAAI;UACF,MAAMC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACC,kBAAkB,CAACJ,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;UACvE,IAAI,CAACK,aAAa,CAACJ,UAAU,CAAC;SAC/B,CAAC,OAAO1G,KAAK,EAAE;UACd+G,OAAO,CAAC/G,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACgF,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;OAE3C,MAAM;QACL;QACA,IAAI,CAAChC,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC;EACJ;EAEA;;;EAGQF,aAAaA,CAACJ,UAAe;IACnC,IAAI,CAACtB,OAAO,GAAG,IAAI;IACnB,IAAI,CAACpF,KAAK,GAAG,IAAI;IAEjB;IACAiH,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACC,mBAAmB,EAAE,EAAET,UAAU,CAAC;MACjE,IAAI,CAACtB,OAAO,GAAG,KAAK;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQ8B,oBAAoBA,CAACE,QAAa,EAAEV,UAAe;IACzD;IACA,IAAI,CAACvJ,aAAa,GAAG;MACnBsB,UAAU,EAAEiI,UAAU,CAACW,IAAI;MAC3B9I,IAAI,EAAEmI,UAAU,CAACD,MAAM,CAACa,iBAAiB;MACzC5I,EAAE,EAAEgI,UAAU,CAACD,MAAM,CAACc,eAAe;MACrC5I,aAAa,EAAE+H,UAAU,CAACD,MAAM,CAAC9H,aAAa;MAC9CvB,UAAU,EAAEsJ,UAAU,CAACD,MAAM,CAACrJ,UAAU;MACxCoK,UAAU,EAAEd,UAAU,CAACD,MAAM,CAACe,UAAU;MACxC3I,YAAY,EAAE,CAAC;MACfC,UAAU,EAAE,IAAI2I,IAAI,EAAE,CAACC,kBAAkB;KAC1C;IAED;IACA,IAAI,CAACrC,OAAO,GAAG,IAAI,CAACsC,cAAc,CAACP,QAAQ,CAAC;IAC5C,IAAI,CAACjK,aAAa,CAAC0B,YAAY,GAAG,IAAI,CAACwG,OAAO,CAAC3B,MAAM;IAErD;IACA,IAAI,CAACkE,YAAY,EAAE;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQF,cAAcA,CAACP,QAAa;IAClC;IACA,MAAM/B,OAAO,GAAmB,EAAE;IAElC;IACA,IAAI+B,QAAQ,CAACU,IAAI,EAAEzC,OAAO,EAAE;MAC1B+B,QAAQ,CAACU,IAAI,CAACzC,OAAO,CAAC0C,OAAO,CAAC,CAACC,MAAW,EAAEC,KAAa,KAAI;QAC3D5C,OAAO,CAAC6C,IAAI,CAAC;UACX7F,EAAE,EAAE2F,MAAM,CAAC3F,EAAE,IAAI,UAAU4F,KAAK,EAAE;UAClCpH,OAAO,EAAE;YACPvB,IAAI,EAAE0I,MAAM,CAACnH,OAAO,EAAEvB,IAAI,IAAI,IAAI;YAClCK,IAAI,EAAEqI,MAAM,CAACnH,OAAO,EAAElB,IAAI,IAAI,oBAAoB;YAClDmB,IAAI,EAAEkH,MAAM,CAACnH,OAAO,EAAEC,IAAI,IAAI,IAAI,CAACoE,kBAAkB,CAACiD,cAAc,CAACH,MAAM,CAACnH,OAAO,EAAEvB,IAAI,IAAI,EAAE;WAChG;UACDmD,SAAS,EAAE;YACTE,OAAO,EAAEqF,MAAM,CAACvF,SAAS,EAAEE,OAAO,IAAI,EAAE;YACxCC,IAAI,EAAEoF,MAAM,CAACvF,SAAS,EAAEG,IAAI,IAAI,EAAE;YAClCF,IAAI,EAAEsF,MAAM,CAACvF,SAAS,EAAEC,IAAI,IAAI,EAAE;YAClC0F,IAAI,EAAEJ,MAAM,CAACvF,SAAS,EAAE2F,IAAI,IAAI;WACjC;UACDtF,OAAO,EAAE;YACPH,OAAO,EAAEqF,MAAM,CAAClF,OAAO,EAAEH,OAAO,IAAI,EAAE;YACtCC,IAAI,EAAEoF,MAAM,CAAClF,OAAO,EAAEF,IAAI,IAAI,EAAE;YAChCF,IAAI,EAAEsF,MAAM,CAAClF,OAAO,EAAEJ,IAAI,IAAI,EAAE;YAChC0F,IAAI,EAAEJ,MAAM,CAAClF,OAAO,EAAEsF,IAAI,IAAI;WAC/B;UACDvF,QAAQ,EAAEmF,MAAM,CAACnF,QAAQ,IAAI,QAAQ;UACrC5B,KAAK,EAAE+G,MAAM,CAAC/G,KAAK,IAAI,CAAC;UACxB8B,KAAK,EAAE;YACLsF,MAAM,EAAEL,MAAM,CAACjF,KAAK,EAAEsF,MAAM,IAAI,CAAC;YACjCC,QAAQ,EAAEN,MAAM,CAACjF,KAAK,EAAEuF,QAAQ,IAAI,KAAK;YACzCtF,SAAS,EAAE,IAAI,CAACuF,WAAW,CAACP,MAAM,CAACjF,KAAK,EAAEsF,MAAM,IAAI,CAAC;WACtD;UACD7F,KAAK,EAAEwF,MAAM,CAACxF,KAAK,IAAI,SAAS;UAChCD,UAAU,EAAEyF,MAAM,CAACzF,UAAU,IAAI,KAAK;UACtCU,OAAO,EAAE+E,MAAM,CAAC/E;SACjB,CAAC;MACJ,CAAC,CAAC;;IAGJ,OAAOoC,OAAO;EAChB;EAEA;;;EAGQa,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACb,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAM8E,MAAM,GAAG,IAAI,CAACnD,OAAO,CAACoD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3F,KAAK,CAACsF,MAAM,CAAC;MACpD,IAAI,CAAC7I,OAAO,CAAC8F,QAAQ,GAAGqD,IAAI,CAACjD,GAAG,CAAC,GAAG8C,MAAM,CAAC;MAC3C,IAAI,CAAChJ,OAAO,CAAC+F,QAAQ,GAAGoD,IAAI,CAAChD,GAAG,CAAC,GAAG6C,MAAM,CAAC;MAC3C,IAAI,CAAChJ,OAAO,CAACgG,YAAY,GAAG,IAAI,CAAChG,OAAO,CAAC+F,QAAQ,CAAC,CAAC;MAEnD;MACA,IAAI,CAAC/F,OAAO,CAACC,QAAQ,GAAG,EAAE;MAE1B;MACA,IAAI,CAACD,OAAO,CAACyB,KAAK,GAAG,EAAE;KACxB,MAAM;MACL;MACA,IAAI,CAACzB,OAAO,CAAC8F,QAAQ,GAAG,CAAC;MACzB,IAAI,CAAC9F,OAAO,CAAC+F,QAAQ,GAAG,IAAI;MAC5B,IAAI,CAAC/F,OAAO,CAACgG,YAAY,GAAG,IAAI;;EAEpC;EAEA;;;EAGAoC,YAAYA,CAAA;IACV,IAAI,CAACnE,eAAe,GAAG,IAAI,CAAC4B,OAAO,CAACuD,MAAM,CAACZ,MAAM,IAAG;MAClD;MACA,IAAIA,MAAM,CAACjF,KAAK,CAACsF,MAAM,GAAG,IAAI,CAAC7I,OAAO,CAACgG,YAAY,EAAE,OAAO,KAAK;MAEjE;MACA,IAAI,IAAI,CAAChG,OAAO,CAACC,QAAQ,CAACiE,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAClE,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAACsI,MAAM,CAACnH,OAAO,CAACvB,IAAI,CAAC,EAAE;QAC5F,OAAO,KAAK;;MAGd;MACA,IAAI,IAAI,CAACE,OAAO,CAACyB,KAAK,CAACyC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAClE,OAAO,CAACyB,KAAK,CAACvB,QAAQ,CAACsI,MAAM,CAAC/G,KAAK,CAAC,EAAE;QAC/E,OAAO,KAAK;;MAGd,OAAO,IAAI;IACb,CAAC,CAAC;IAEF,IAAI,CAAC4H,WAAW,EAAE;IAClB,IAAI,CAAChB,gBAAgB,EAAE;EACzB;EAEA;;;EAGAgB,WAAWA,CAAA;IACT,IAAI,CAACpF,eAAe,CAACqF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC,IAAIC,UAAU,GAAG,CAAC;MAElB,QAAQ,IAAI,CAACrD,MAAM;QACjB,KAAK,OAAO;UACVqD,UAAU,GAAGF,CAAC,CAAChG,KAAK,CAACsF,MAAM,GAAGW,CAAC,CAACjG,KAAK,CAACsF,MAAM;UAC5C;QACF,KAAK,UAAU;UACbY,UAAU,GAAG,IAAI,CAACC,aAAa,CAACH,CAAC,CAAClG,QAAQ,CAAC,GAAG,IAAI,CAACqG,aAAa,CAACF,CAAC,CAACnG,QAAQ,CAAC;UAC5E;QACF,KAAK,WAAW;UACdoG,UAAU,GAAGF,CAAC,CAACtG,SAAS,CAACC,IAAI,CAACyG,aAAa,CAACH,CAAC,CAACvG,SAAS,CAACC,IAAI,CAAC;UAC7D;;MAGJ,OAAO,IAAI,CAACmD,SAAS,KAAK,KAAK,GAAGoD,UAAU,GAAG,CAACA,UAAU;IAC5D,CAAC,CAAC;EACJ;EAEA;;;EAGApB,gBAAgBA,CAAA;IACd,IAAI,CAACjD,UAAU,GAAG+D,IAAI,CAACS,IAAI,CAAC,IAAI,CAAC3F,eAAe,CAACC,MAAM,GAAG,IAAI,CAACqC,YAAY,CAAC;IAC5E,IAAI,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACS,UAAU,EAAE;MACtC,IAAI,CAACT,WAAW,GAAG,CAAC;;EAExB;EAEA;;;EAGAR,mBAAmBA,CAAA;IACjB,MAAM0F,UAAU,GAAG,CAAC,IAAI,CAAClF,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC4B,YAAY;IAC7D,MAAMuD,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACtD,YAAY;IAC/C,OAAO,IAAI,CAACtC,eAAe,CAAC8F,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACzD;EAEA;;;EAGArF,UAAUA,CAACuF,IAAY;IACrB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAC5E,UAAU,EAAE;MACxC,IAAI,CAACT,WAAW,GAAGqF,IAAI;;EAE3B;EAEA;;;EAGAnI,YAAYA,CAAC2G,MAAoB;IAC/B;IACAjB,OAAO,CAAC0C,GAAG,CAAC,kBAAkB,EAAEzB,MAAM,CAAC;IACvC;EACF;EAEA;;;EAGA9F,iBAAiBA,CAAC8F,MAAoB;IACpCjB,OAAO,CAAC0C,GAAG,CAAC,+BAA+B,EAAEzB,MAAM,CAAC;IACpD;IACA;EACF;EAEA;;;EAGAvH,WAAWA,CAACuH,MAAoB;IAC9BA,MAAM,CAACnH,OAAO,CAACuB,SAAS,GAAG,IAAI;IAC/B2E,OAAO,CAAC0C,GAAG,CAAC,qCAAqCzB,MAAM,CAACnH,OAAO,CAACvB,IAAI,EAAE,CAAC;EACzE;EAEA;;;EAGAsB,UAAUA,CAACoH,MAAoB;IAC7BA,MAAM,CAACnH,OAAO,CAACuB,SAAS,GAAG,KAAK;EAClC;EAEA;;;EAGAhE,YAAYA,CAAA;IACV,IAAI,CAAC4G,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEA;;;EAGA/I,YAAYA,CAAA;IACV;IACA,IAAI,CAAC+G,MAAM,CAACgC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,EAAE;MACvCV,WAAW,EAAE;QAAEoD,MAAM,EAAE,IAAI;QAAEhD,UAAU,EAAEC,IAAI,CAACgD,SAAS,CAAC,IAAI,CAACxM,aAAa;MAAC;KAC5E,CAAC;EACJ;EAEA;;;EAGQoL,WAAWA,CAACF,MAAc;IAChC,OAAO,IAAIuB,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBxB,QAAQ,EAAE;KACX,CAAC,CAACyB,MAAM,CAAC1B,MAAM,CAAC;EACnB;EAEA;;;EAGQa,aAAaA,CAACrG,QAAgB;IACpC,MAAMmH,KAAK,GAAGnH,QAAQ,CAACmH,KAAK,CAAC,iBAAiB,CAAC;IAC/C,IAAIA,KAAK,EAAE;MACT,OAAOC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGC,QAAQ,CAACD,KAAK,CAAC,CAAC,CAAC,CAAC;;IAErD,OAAO,CAAC;EACV;EAEA;;;EAGApL,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzB,aAAa,EAAE,OAAO,EAAE;IAElC,MAAM+M,KAAK,GAAa,EAAE;IAC1B,MAAMC,CAAC,GAAG,IAAI,CAAChN,aAAa,CAACqK,UAAU;IAEvC,IAAI2C,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAChBF,KAAK,CAAChC,IAAI,CAAC,GAAGiC,CAAC,CAACC,MAAM,UAAUD,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAE5D,IAAID,CAAC,CAACE,QAAQ,GAAG,CAAC,EAAE;MAClBH,KAAK,CAAChC,IAAI,CAAC,GAAGiC,CAAC,CAACE,QAAQ,UAAUF,CAAC,CAACE,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAEhE,IAAIF,CAAC,CAACG,OAAO,GAAG,CAAC,EAAE;MACjBJ,KAAK,CAAChC,IAAI,CAAC,GAAGiC,CAAC,CAACG,OAAO,QAAQH,CAAC,CAACG,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAG5D,OAAOJ,KAAK,CAACK,IAAI,CAAC,IAAI,CAAC;EACzB;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAM/K,QAAQ,GAAG,IAAIgL,GAAG,EAAkB;IAC1C,IAAI,CAACpF,OAAO,CAAC0C,OAAO,CAACC,MAAM,IAAG;MAC5BvI,QAAQ,CAACiL,GAAG,CAAC1C,MAAM,CAACnH,OAAO,CAACvB,IAAI,EAAE0I,MAAM,CAACnH,OAAO,CAAClB,IAAI,CAAC;IACxD,CAAC,CAAC;IAEF,OAAOgL,KAAK,CAACpM,IAAI,CAACkB,QAAQ,CAACmL,OAAO,EAAE,CAAC,CAACnC,GAAG,CAAC,CAAC,CAACnJ,IAAI,EAAEK,IAAI,CAAC,MAAM;MAAEL,IAAI;MAAEK;IAAI,CAAE,CAAC,CAAC;EAC/E;EAEA;;;EAGAN,mBAAmBA,CAACwL,WAAmB;IACrC,MAAM5C,KAAK,GAAG,IAAI,CAACzI,OAAO,CAACC,QAAQ,CAACqL,OAAO,CAACD,WAAW,CAAC;IACxD,IAAI5C,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACzI,OAAO,CAACC,QAAQ,CAACsL,MAAM,CAAC9C,KAAK,EAAE,CAAC,CAAC;KACvC,MAAM;MACL,IAAI,CAACzI,OAAO,CAACC,QAAQ,CAACyI,IAAI,CAAC2C,WAAW,CAAC;;IAEzC,IAAI,CAACjD,YAAY,EAAE;EACrB;EAEA;;;EAGAoD,iBAAiBA,CAAC/J,KAAa;IAC7B,MAAMgH,KAAK,GAAG,IAAI,CAACzI,OAAO,CAACyB,KAAK,CAAC6J,OAAO,CAAC7J,KAAK,CAAC;IAC/C,IAAIgH,KAAK,GAAG,CAAC,CAAC,EAAE;MACd,IAAI,CAACzI,OAAO,CAACyB,KAAK,CAAC8J,MAAM,CAAC9C,KAAK,EAAE,CAAC,CAAC;KACpC,MAAM;MACL,IAAI,CAACzI,OAAO,CAACyB,KAAK,CAACiH,IAAI,CAACjH,KAAK,CAAC;;IAEhC,IAAI,CAAC2G,YAAY,EAAE;EACrB;EAEA;;;EAGAxH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACiF,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC3B,MAAM8E,MAAM,GAAG,IAAI,CAACnD,OAAO,CAACoD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3F,KAAK,CAACsF,MAAM,CAAC;MACpD,IAAI,CAAC7I,OAAO,CAAC8F,QAAQ,GAAGqD,IAAI,CAACjD,GAAG,CAAC,GAAG8C,MAAM,CAAC;MAC3C,IAAI,CAAChJ,OAAO,CAAC+F,QAAQ,GAAGoD,IAAI,CAAChD,GAAG,CAAC,GAAG6C,MAAM,CAAC;MAC3C,IAAI,CAAChJ,OAAO,CAACgG,YAAY,GAAG,IAAI,CAAChG,OAAO,CAAC+F,QAAQ;KAClD,MAAM;MACL,IAAI,CAAC/F,OAAO,CAAC8F,QAAQ,GAAG,CAAC;MACzB,IAAI,CAAC9F,OAAO,CAAC+F,QAAQ,GAAG,IAAI;MAC5B,IAAI,CAAC/F,OAAO,CAACgG,YAAY,GAAG,IAAI;;IAGlC,IAAI,CAAChG,OAAO,CAACC,QAAQ,GAAG,EAAE;IAC1B,IAAI,CAACD,OAAO,CAACyB,KAAK,GAAG,EAAE;IACvB,IAAI,CAACzB,OAAO,CAACiG,aAAa,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAE;IAChD,IAAI,CAACnG,OAAO,CAACqD,QAAQ,GAAG;MAAE6C,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAE;IAE3C,IAAI,CAACiC,YAAY,EAAE;EACrB;EAEA;;;EAGAqD,eAAeA,CAAA;IACb,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACA,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IAC1D,IAAI,CAACgD,WAAW,EAAE;EACpB;EAEA;;;EAGAlE,cAAcA,CAAA;IACZ,MAAMuG,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAIC,KAAK,GAAGzC,IAAI,CAAChD,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxB,WAAW,GAAGwE,IAAI,CAAC0C,KAAK,CAACF,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIG,GAAG,GAAG3C,IAAI,CAACjD,GAAG,CAAC,IAAI,CAACd,UAAU,EAAEwG,KAAK,GAAGD,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIG,GAAG,GAAGF,KAAK,GAAG,CAAC,GAAGD,UAAU,EAAE;MAChCC,KAAK,GAAGzC,IAAI,CAAChD,GAAG,CAAC,CAAC,EAAE2F,GAAG,GAAGH,UAAU,GAAG,CAAC,CAAC;;IAG3C,KAAK,IAAII,CAAC,GAAGH,KAAK,EAAEG,CAAC,IAAID,GAAG,EAAEC,CAAC,EAAE,EAAE;MACjCL,KAAK,CAAChD,IAAI,CAACqD,CAAC,CAAC;;IAGf,OAAOL,KAAK;EACd;EAEA;;;EAGAtH,eAAeA,CAACqE,KAAa,EAAED,MAAoB;IACjD,OAAOA,MAAM,CAAC3F,EAAE;EAClB;EAEA;;;EAGAmJ,WAAWA,CAAA;IACT,IAAI,IAAI,CAACnG,OAAO,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IACvC,OAAOiF,IAAI,CAACjD,GAAG,CAAC,GAAG,IAAI,CAACL,OAAO,CAACoD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3F,KAAK,CAACsF,MAAM,CAAC,CAAC;EAC3D;EAEA;;;EAGAoD,WAAWA,CAAA;IACT,IAAI,IAAI,CAACpG,OAAO,CAAC3B,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAC1C,OAAOiF,IAAI,CAAChD,GAAG,CAAC,GAAG,IAAI,CAACN,OAAO,CAACoD,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC3F,KAAK,CAACsF,MAAM,CAAC,CAAC;EAC3D;EAEA;;;EAGAqD,kBAAkBA,CAAC3I,KAAa;IAC9B,IAAI,CAACvD,OAAO,CAACgG,YAAY,GAAGzC,KAAK;IACjC,IAAI,CAAC6E,YAAY,EAAE;EACrB;EAEA;;;EAGQT,mBAAmBA,CAAA;IACzB,OAAO;MACLW,IAAI,EAAE;QACJzC,OAAO,EAAE,CACP;UACEhD,EAAE,EAAE,UAAU;UACdxB,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAkB,CAAE;UACjD8C,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAClFtF,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAC7EvF,QAAQ,EAAE,QAAQ;UAClB5B,KAAK,EAAE,CAAC;UACR8B,KAAK,EAAE;YAAEsF,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvC9F,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,IAAI;UAChBU,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACEd,EAAE,EAAE,UAAU;UACdxB,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAU,CAAE;UACzC8C,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAClFtF,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAC7EvF,QAAQ,EAAE,QAAQ;UAClB5B,KAAK,EAAE,CAAC;UACR8B,KAAK,EAAE;YAAEsF,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvC9F,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,KAAK;UACjBU,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACEd,EAAE,EAAE,UAAU;UACdxB,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAY,CAAE;UAC3C8C,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAClFtF,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAC7EvF,QAAQ,EAAE,QAAQ;UAClB5B,KAAK,EAAE,CAAC;UACR8B,KAAK,EAAE;YAAEsF,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvC9F,KAAK,EAAE,SAAS;UAChBD,UAAU,EAAE,IAAI;UAChBU,OAAO,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAM;SAC5C,EACD;UACEd,EAAE,EAAE,UAAU;UACdxB,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAW,CAAE;UAC1C8C,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAClFtF,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAC7EvF,QAAQ,EAAE,QAAQ;UAClB5B,KAAK,EAAE,CAAC;UACR8B,KAAK,EAAE;YAAEsF,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvC9F,KAAK,EAAE,UAAU;UACjBD,UAAU,EAAE,IAAI;UAChBU,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C,EACD;UACEd,EAAE,EAAE,UAAU;UACdxB,OAAO,EAAE;YAAEvB,IAAI,EAAE,IAAI;YAAEK,IAAI,EAAE;UAAU,CAAE;UACzC8C,SAAS,EAAE;YAAEE,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,UAAU;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAClFtF,OAAO,EAAE;YAAEH,OAAO,EAAE,KAAK;YAAEC,IAAI,EAAE,OAAO;YAAEF,IAAI,EAAE,OAAO;YAAE0F,IAAI,EAAE;UAAY,CAAE;UAC7EvF,QAAQ,EAAE,SAAS;UACnB5B,KAAK,EAAE,CAAC;UACR8B,KAAK,EAAE;YAAEsF,MAAM,EAAE,GAAG;YAAEC,QAAQ,EAAE;UAAK,CAAE;UACvC9F,KAAK,EAAE,UAAU;UACjBD,UAAU,EAAE,IAAI;UAChBU,OAAO,EAAE;YAAEC,OAAO,EAAE,KAAK;YAAEC,OAAO,EAAE;UAAM;SAC3C;;KAGN;EACH;;;uBA5hBW0B,sBAAsB,EAAAlI,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAArP,EAAA,CAAAgP,iBAAA,CAAAM,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAtBrH,sBAAsB;MAAAsH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChEnC9P,EAAA,CAAAC,cAAA,aAAsC;UAIhCD,EAAA,CAAAW,UAAA,IAAAqP,qCAAA,kBAwCM;UACRhQ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,aAA6B;UAInBD,EAAA,CAAAI,MAAA,cAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,gBAAgE;UAArCD,EAAA,CAAAe,UAAA,mBAAAkP,wDAAA;YAAA,OAAAF,GAAA,CAAA5G,WAAA,IAAA4G,GAAA,CAAA5G,WAAA;UAAA,EAAoC;UAC7DnJ,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,eAAiH;UACnHF,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAa,eAAA,EAA4B;UAA5Bb,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,eAA0B;UAEED,EAAA,CAAAI,MAAA,IAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACnDH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAI,MAAA,IAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAErDH,EAAA,CAAAC,cAAA,iBAM4B;UALrBD,EAAA,CAAAe,UAAA,2BAAAmP,gEAAA9K,MAAA;YAAA,OAAA2K,GAAA,CAAAlN,OAAA,CAAAgG,YAAA,GAAAzD,MAAA;UAAA,EAAkC,mBAAA+K,wDAAA/K,MAAA;YAAA,OAIzB2K,GAAA,CAAAhB,kBAAA,CAAA3J,MAAA,CAAAgL,MAAA,CAAAC,KAAA,CAA6C;UAAA,EAJpB;UADzCrQ,EAAA,CAAAG,YAAA,EAM4B;UAC5BH,EAAA,CAAAC,cAAA,eAA2B;UACCD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAC9CH,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAI,MAAA,IAA2B;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKlEH,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,iCAAoB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAW,UAAA,KAAA2P,wCAAA,oBAOQ;UACVtQ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA4B;UACtBD,EAAA,CAAAI,MAAA,wBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,eAA2B;UAIhBD,EAAA,CAAAe,UAAA,oBAAAwP,yDAAA;YAAA,OAAUR,GAAA,CAAA1B,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtCrO,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAEzBH,EAAA,CAAAC,cAAA,iBAA8B;UAGrBD,EAAA,CAAAe,UAAA,oBAAAyP,yDAAA;YAAA,OAAUT,GAAA,CAAA1B,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtCrO,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAEvBH,EAAA,CAAAC,cAAA,iBAA8B;UAGrBD,EAAA,CAAAe,UAAA,oBAAA0P,yDAAA;YAAA,OAAUV,GAAA,CAAA1B,iBAAA,CAAkB,CAAC,CAAC;UAAA,EAAC;UAFtCrO,EAAA,CAAAG,YAAA,EAG6C;UAC7CH,EAAA,CAAAE,SAAA,gBAA+B;UAC/BF,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAK7BH,EAAA,CAAAC,cAAA,eAA4B;UACAD,EAAA,CAAAe,UAAA,mBAAA2P,yDAAA;YAAA,OAASX,GAAA,CAAAtM,YAAA,EAAc;UAAA,EAAC;UAACzD,EAAA,CAAAI,MAAA,2BAAmB;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAKnFH,EAAA,CAAAC,cAAA,gBAA2B;UAIaD,EAAA,CAAAe,UAAA,mBAAA4P,yDAAA;YAAA,OAAAZ,GAAA,CAAA5G,WAAA,IAAA4G,GAAA,CAAA5G,WAAA;UAAA,EAAoC;UACpEnJ,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAoG;UACtGF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAa,eAAA,EAAyB;UAAzBb,EAAA,CAAAC,cAAA,eAAyB;UAGfD,EAAA,CAAAe,UAAA,mBAAA6P,yDAAA;YAAA,OAAAb,GAAA,CAAAlJ,QAAA,GAAoB,MAAM;UAAA,EAAC;UACjC7G,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAkG;UACpGF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAa,eAAA,EAEoC;UAFpCb,EAAA,CAAAC,cAAA,kBAEoC;UAA5BD,EAAA,CAAAe,UAAA,mBAAA8P,yDAAA;YAAA,OAAAd,GAAA,CAAAlJ,QAAA,GAAoB,MAAM;UAAA,EAAC;UACjC7G,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAE,SAAA,gBAAmG;UACrGF,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAa,eAAA,EAA2B;UAA3Bb,EAAA,CAAAC,cAAA,eAA2B;UAEhBD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACzBH,EAAA,CAAAC,cAAA,kBAAsE;UAA9DD,EAAA,CAAAe,UAAA,2BAAA+P,iEAAA1L,MAAA;YAAA,OAAA2K,GAAA,CAAA9G,MAAA,GAAA7D,MAAA;UAAA,EAAoB,oBAAA2L,0DAAA;YAAWhB,GAAA,CAAA7D,WAAA,EAAa;YAAA,OAAE6D,GAAA,CAAA9E,YAAA,EAAc;UAAA,EAAxC;UAC1BjL,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,kBAAyB;UAAAD,EAAA,CAAAI,MAAA,kBAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACvCH,EAAA,CAAAC,cAAA,kBAA0B;UAAAD,EAAA,CAAAI,MAAA,4BAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAEpDH,EAAA,CAAAC,cAAA,kBAA2D;UAA5BD,EAAA,CAAAe,UAAA,mBAAAiQ,yDAAA;YAAA,OAASjB,GAAA,CAAAzB,eAAA,EAAiB;UAAA,EAAC;UACxDtO,EAAA,CAAAU,cAAA,EAA6C;UAA7CV,EAAA,CAAAC,cAAA,cAA6C;UAC3CD,EAAA,CAAAW,UAAA,KAAAsQ,4CAAA,mBAAoF;UACpFjR,EAAA,CAAAW,UAAA,KAAAuQ,4CAAA,mBAAoF;UACtFlR,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAa,eAAA,EAA2B;UAA3Bb,EAAA,CAAAC,cAAA,gBAA2B;UACzBD,EAAA,CAAAI,MAAA,IACF;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKXH,EAAA,CAAAW,UAAA,KAAAwQ,sCAAA,kBAGM;UAGNnR,EAAA,CAAAW,UAAA,KAAAyQ,sCAAA,mBASM;UAGNpR,EAAA,CAAAW,UAAA,KAAA0Q,sCAAA,kBA4IM;UAGNrR,EAAA,CAAAW,UAAA,KAAA2Q,sCAAA,mBA2BM;UACRtR,EAAA,CAAAG,YAAA,EAAO;;;UAjXwBH,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAAvP,aAAA,CAAmB;UA+CnBR,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAA2G,WAAA,YAAAoJ,GAAA,CAAA5G,WAAA,CAA6B;UAc5BnJ,EAAA,CAAAK,SAAA,IAAoB;UAApBL,EAAA,CAAAM,kBAAA,KAAAyP,GAAA,CAAAlB,WAAA,aAAoB;UACpB7O,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAM,kBAAA,KAAAyP,GAAA,CAAAjB,WAAA,aAAoB;UAGvC9O,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAAlN,OAAA,CAAAgG,YAAA,CAAkC,QAAAkH,GAAA,CAAAlB,WAAA,WAAAkB,GAAA,CAAAjB,WAAA;UAQb9O,EAAA,CAAAK,SAAA,GAA2B;UAA3BL,EAAA,CAAAM,kBAAA,KAAAyP,GAAA,CAAAlN,OAAA,CAAAgG,YAAA,WAA2B;UAQ5B7I,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAAlC,iBAAA,GAAsB;UAkBxC7N,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAAlN,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UAQrC/C,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAAlN,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UAQrC/C,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAAlN,OAAA,CAAAyB,KAAA,CAAAvB,QAAA,IAAqC;UA0BpC/C,EAAA,CAAAK,SAAA,IAAoC;UAApCL,EAAA,CAAA2G,WAAA,WAAAoJ,GAAA,CAAAlJ,QAAA,YAAoC;UAOpC7G,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAA2G,WAAA,WAAAoJ,GAAA,CAAAlJ,QAAA,YAAoC;UAYpC7G,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAA6B,UAAA,YAAAkO,GAAA,CAAA9G,MAAA,CAAoB;UAOjBjJ,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAA7G,SAAA,WAAyB;UACzBlJ,EAAA,CAAAK,SAAA,GAA0B;UAA1BL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAA7G,SAAA,YAA0B;UAMrClJ,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAM,kBAAA,MAAAyP,GAAA,CAAAjJ,eAAA,CAAAC,MAAA,qBACF;UAKE/G,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAAtH,OAAA,CAAa;UAMbzI,EAAA,CAAAK,SAAA,GAAW;UAAXL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAA1M,KAAA,CAAW;UAYXrD,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA6B,UAAA,UAAAkO,GAAA,CAAAtH,OAAA,KAAAsH,GAAA,CAAA1M,KAAA,CAAwB;UA+IxBrD,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAA6B,UAAA,SAAAkO,GAAA,CAAA9H,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}