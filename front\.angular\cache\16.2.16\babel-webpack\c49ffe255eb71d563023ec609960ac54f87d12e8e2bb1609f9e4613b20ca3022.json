{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080',\n  authEndpoint: '/auth/login',\n  flightSearchEndpoint: '/api/flights/search'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "authEndpoint", "flightSearchEndpoint"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\n\nexport const environment = {\n  production: false,\n  apiUrl: 'http://localhost:8080', // URL de votre backend Spring Boot en développement\n  authEndpoint: '/auth/login',\n  flightSearchEndpoint: '/api/flights/search'\n};\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,uBAAuB;EAC/BC,YAAY,EAAE,aAAa;EAC3BC,oBAAoB,EAAE;CACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}