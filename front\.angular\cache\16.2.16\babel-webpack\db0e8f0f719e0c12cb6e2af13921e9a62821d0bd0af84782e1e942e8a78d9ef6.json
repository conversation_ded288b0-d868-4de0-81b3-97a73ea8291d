{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { CommonModule } from '@angular/common';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { SearchFlightComponent } from './components/search-flight/search-flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, LoginComponent, DashboardComponent, SearchFlightComponent, FlightResultsComponent],\n  imports: [BrowserModule, CommonModule, AppRoutingModule, ReactiveFormsModule, FormsModule, HttpClientModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: HttpRequestInterceptor,\n    multi: true\n  }],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "ReactiveFormsModule", "FormsModule", "HttpClientModule", "HTTP_INTERCEPTORS", "CommonModule", "AppRoutingModule", "AppComponent", "LoginComponent", "DashboardComponent", "SearchFlightComponent", "FlightResultsComponent", "HttpRequestInterceptor", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { CommonModule } from '@angular/common';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { LoginComponent } from './components/login/login.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { SearchFlightComponent } from './components/search-flight/search-flight.component';\nimport { FlightResultsComponent } from './components/flight-results/flight-results.component';\nimport { HttpRequestInterceptor } from './interceptors/http.interceptor';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    LoginComponent,\n    DashboardComponent,\n    SearchFlightComponent,\n    FlightResultsComponent\n  ],\n  imports: [\n    BrowserModule,\n    CommonModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    FormsModule,\n    HttpClientModule\n  ],\n  providers: [\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: HttpRequestInterceptor,\n      multi: true\n    }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AACjE,SAASC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC1E,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,sBAAsB,QAAQ,sDAAsD;AAC7F,SAASC,sBAAsB,QAAQ,iCAAiC;AA2BjE,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAzBrBf,QAAQ,CAAC;EACRgB,YAAY,EAAE,CACZR,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,qBAAqB,EACrBC,sBAAsB,CACvB;EACDK,OAAO,EAAE,CACPhB,aAAa,EACbK,YAAY,EACZC,gBAAgB,EAChBL,mBAAmB,EACnBC,WAAW,EACXC,gBAAgB,CACjB;EACDc,SAAS,EAAE,CACT;IACEC,OAAO,EAAEd,iBAAiB;IAC1Be,QAAQ,EAAEP,sBAAsB;IAChCQ,KAAK,EAAE;GACR,CACF;EACDC,SAAS,EAAE,CAACd,YAAY;CACzB,CAAC,C,EACWM,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}