{"ast": null, "code": "/**\n * Interface TypeScript correspondant exactement à la classe Java RoundTripRequest\n * Générée à partir du modèle backend com.paximum.demo.models.RoundTripRequest\n */\n// ===== TYPES D'ÉNUMÉRATION =====\n/**\n * Types de passagers selon le backend\n */\nexport var PassengerType;\n(function (PassengerType) {\n  PassengerType[PassengerType[\"ADULT\"] = 1] = \"ADULT\";\n  PassengerType[PassengerType[\"CHILD\"] = 2] = \"CHILD\";\n  PassengerType[PassengerType[\"INFANT\"] = 3] = \"INFANT\";\n})(PassengerType || (PassengerType = {}));\n/**\n * Types de localisation selon le backend\n */\nexport var LocationType;\n(function (LocationType) {\n  LocationType[LocationType[\"AIRPORT\"] = 1] = \"AIRPORT\";\n  LocationType[LocationType[\"CITY\"] = 2] = \"CITY\";\n  LocationType[LocationType[\"COUNTRY\"] = 3] = \"COUNTRY\";\n})(LocationType || (LocationType = {}));\n/**\n * Classes de vol selon le backend\n */\nexport var FlightClass;\n(function (FlightClass) {\n  FlightClass[FlightClass[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClass[FlightClass[\"PREMIUM_ECONOMY\"] = 2] = \"PREMIUM_ECONOMY\";\n  FlightClass[FlightClass[\"BUSINESS\"] = 3] = \"BUSINESS\";\n  FlightClass[FlightClass[\"FIRST\"] = 4] = \"FIRST\";\n})(FlightClass || (FlightClass = {}));\n/**\n * Types de produit selon le backend\n */\nexport var ProductType;\n(function (ProductType) {\n  ProductType[ProductType[\"FLIGHT\"] = 2] = \"FLIGHT\";\n})(ProductType || (ProductType = {}));\n/**\n * Types de réponse de vol supportés\n */\nexport var FlightResponseListType;\n(function (FlightResponseListType) {\n  FlightResponseListType[FlightResponseListType[\"STANDARD\"] = 1] = \"STANDARD\";\n  FlightResponseListType[FlightResponseListType[\"DETAILED\"] = 2] = \"DETAILED\";\n})(FlightResponseListType || (FlightResponseListType = {}));\n// ===== FONCTIONS UTILITAIRES =====\n/**\n * Crée une Location à partir de paramètres simplifiés\n */\nexport function createLocation(params) {\n  return {\n    id: params.id.toUpperCase(),\n    type: params.type || LocationType.AIRPORT,\n    provider: params.provider || 1\n  };\n}\n/**\n * Crée un Passenger à partir de paramètres simplifiés\n */\nexport function createPassenger(params) {\n  return {\n    type: params.type,\n    count: params.count\n  };\n}\n/**\n * Crée une liste de passagers à partir de paramètres simplifiés\n */\nexport function createPassengerList(passengers) {\n  const passengerList = [];\n  if (passengers.adults > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.ADULT,\n      count: passengers.adults\n    }));\n  }\n  if (passengers.children && passengers.children > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.CHILD,\n      count: passengers.children\n    }));\n  }\n  if (passengers.infants && passengers.infants > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.INFANT,\n      count: passengers.infants\n    }));\n  }\n  return passengerList;\n}\n/**\n * Calcule le nombre de nuits entre deux dates\n */\nexport function calculateNights(checkInDate, returnDate) {\n  const checkIn = new Date(checkInDate);\n  const checkOut = new Date(returnDate);\n  const timeDiff = checkOut.getTime() - checkIn.getTime();\n  return Math.ceil(timeDiff / (1000 * 3600 * 24));\n}\n/**\n * Valeurs par défaut pour une requête RoundTrip\n */\nexport const DEFAULT_ROUNDTRIP_REQUEST = {\n  ProductType: ProductType.FLIGHT,\n  ServiceTypes: ['Flight'],\n  acceptPendingProviders: true,\n  forceFlightBundlePackage: false,\n  disablePackageOfferTotalPrice: false,\n  supportedFlightReponseListTypes: [FlightResponseListType.STANDARD, FlightResponseListType.DETAILED],\n  showOnlyNonStopFlight: false,\n  calculateFlightFees: true,\n  Culture: 'fr-FR',\n  Currency: 'EUR'\n};\n/**\n * Crée une requête RoundTrip complète à partir de paramètres simplifiés\n */\nexport function createRoundTripRequest(params) {\n  const nights = calculateNights(params.departureDate, params.returnDate);\n  return {\n    ...DEFAULT_ROUNDTRIP_REQUEST,\n    DepartureLocations: [createLocation({\n      id: params.departureLocation\n    })],\n    ArrivalLocations: [createLocation({\n      id: params.arrivalLocation\n    })],\n    CheckIn: params.departureDate,\n    Night: nights,\n    Passengers: createPassengerList(params.passengers),\n    showOnlyNonStopFlight: params.directFlightsOnly || false,\n    Culture: params.culture || 'fr-FR',\n    Currency: params.currency || 'EUR'\n  };\n}\n/**\n * Valide une requête RoundTrip\n */\nexport function validateRoundTripRequest(request) {\n  const errors = [];\n  if (!request.CheckIn) {\n    errors.push('CheckIn date is required');\n  }\n  if (request.Night <= 0) {\n    errors.push('Night must be greater than 0');\n  }\n  if (!request.DepartureLocations || request.DepartureLocations.length === 0) {\n    errors.push('At least one departure location is required');\n  }\n  if (!request.ArrivalLocations || request.ArrivalLocations.length === 0) {\n    errors.push('At least one arrival location is required');\n  }\n  if (!request.Passengers || request.Passengers.length === 0) {\n    errors.push('At least one passenger is required');\n  }\n  if (!request.Culture) {\n    errors.push('Culture is required');\n  }\n  if (!request.Currency) {\n    errors.push('Currency is required');\n  }\n  return errors;\n}", "map": {"version": 3, "names": ["PassengerType", "LocationType", "FlightClass", "ProductType", "FlightResponseListType", "createLocation", "params", "id", "toUpperCase", "type", "AIRPORT", "provider", "create<PERSON><PERSON>eng<PERSON>", "count", "createPassengerList", "passengers", "passengerList", "adults", "push", "ADULT", "children", "CHILD", "infants", "INFANT", "calculateNights", "checkInDate", "returnDate", "checkIn", "Date", "checkOut", "timeDiff", "getTime", "Math", "ceil", "DEFAULT_ROUNDTRIP_REQUEST", "FLIGHT", "ServiceTypes", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "supportedFlightReponseListTypes", "STANDARD", "DETAILED", "showOnlyNonStopFlight", "calculateFlightFees", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "createRoundTripRequest", "nights", "departureDate", "DepartureLocations", "departureLocation", "ArrivalLocations", "arrivalLocation", "CheckIn", "Night", "Passengers", "directFlightsOnly", "culture", "currency", "validateRoundTripRequest", "request", "errors", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\models\\roundtrip-request.interface.ts"], "sourcesContent": ["/**\n * Interface TypeScript correspondant exactement à la classe Java RoundTripRequest\n * Générée à partir du modèle backend com.paximum.demo.models.RoundTripRequest\n */\n\n// ===== INTERFACES COMMUNES (réutilisées depuis OneWay) =====\n\n/**\n * Interface correspondant à RoundTripRequest.Location\n */\nexport interface Location {\n  id: string;\n  type: number;\n  provider?: number;\n}\n\n/**\n * Interface correspondant à RoundTripRequest.Passenger\n */\nexport interface Passenger {\n  type: number;\n  count: number;\n}\n\n/**\n * Interface correspondant à RoundTripRequest.CorporateRule\n */\nexport interface CorporateRule {\n  Airline: string;\n  Supplier: string;\n}\n\n/**\n * Interface correspondant à RoundTripRequest.CorporateCode\n */\nexport interface CorporateCode {\n  Code: string;\n  Rule: CorporateRule;\n}\n\n/**\n * Interface correspondant à RoundTripRequest.GetOptionsParameters\n */\nexport interface GetOptionsParameters {\n  flightBaggageGetOption: number;\n}\n\n/**\n * Interface correspondant à RoundTripRequest.AdditionalParameters\n */\nexport interface AdditionalParameters {\n  getOptionsParameters?: GetOptionsParameters;\n  CorporateCodes?: CorporateCode[];\n}\n\n// ===== ROUND TRIP REQUEST =====\n\n/**\n * Interface correspondant exactement à la classe Java RoundTripRequest\n * Tous les noms de propriétés correspondent aux @JsonProperty du backend\n * \n * @example\n * ```typescript\n * const roundTripRequest: RoundTripRequest = {\n *   ProductType: 2,\n *   ServiceTypes: ['Flight'],\n *   DepartureLocations: [{ id: 'IST', type: 1, provider: 1 }],\n *   ArrivalLocations: [{ id: 'TUN', type: 1, provider: 1 }],\n *   CheckIn: '2024-06-15',\n *   Night: 7,\n *   Passengers: [{ type: 1, count: 1 }],\n *   acceptPendingProviders: true,\n *   forceFlightBundlePackage: false,\n *   disablePackageOfferTotalPrice: false,\n *   supportedFlightReponseListTypes: [1, 2],\n *   showOnlyNonStopFlight: false,\n *   calculateFlightFees: true,\n *   Culture: 'fr-FR',\n *   Currency: 'EUR'\n * };\n * ```\n */\nexport interface RoundTripRequest {\n  /**\n   * Type de produit (généralement 2 pour les vols)\n   */\n  ProductType: number;\n\n  /**\n   * Types de services (ex: ['Flight'])\n   */\n  ServiceTypes: string[];\n\n  /**\n   * Liste des lieux de départ\n   */\n  DepartureLocations: Location[];\n\n  /**\n   * Liste des lieux d'arrivée\n   */\n  ArrivalLocations: Location[];\n\n  /**\n   * Date de départ au format ISO (YYYY-MM-DD)\n   */\n  CheckIn: string;\n\n  /**\n   * Nombre de nuits du séjour\n   */\n  Night: number;\n\n  /**\n   * Liste des passagers avec leur type et nombre\n   */\n  Passengers: Passenger[];\n\n  /**\n   * Accepter les fournisseurs en attente\n   */\n  acceptPendingProviders: boolean;\n\n  /**\n   * Forcer le package de vol groupé\n   */\n  forceFlightBundlePackage: boolean;\n\n  /**\n   * Désactiver le prix total de l'offre package\n   */\n  disablePackageOfferTotalPrice: boolean;\n\n  /**\n   * Types de réponse de vol supportés\n   */\n  supportedFlightReponseListTypes: number[];\n\n  /**\n   * Afficher uniquement les vols sans escale\n   */\n  showOnlyNonStopFlight: boolean;\n\n  /**\n   * Paramètres additionnels (obligatoire selon le backend)\n   */\n  additionalParameters: AdditionalParameters;\n\n  /**\n   * Calculer les frais de vol\n   */\n  calculateFlightFees: boolean;\n\n  /**\n   * Culture/Langue (ex: 'fr-FR', 'en-US')\n   */\n  Culture: string;\n\n  /**\n   * Devise (ex: 'EUR', 'USD')\n   */\n  Currency: string;\n}\n\n// ===== TYPES D'ÉNUMÉRATION =====\n\n/**\n * Types de passagers selon le backend\n */\nexport enum PassengerType {\n  ADULT = 1,\n  CHILD = 2,\n  INFANT = 3\n}\n\n/**\n * Types de localisation selon le backend\n */\nexport enum LocationType {\n  AIRPORT = 1,\n  CITY = 2,\n  COUNTRY = 3\n}\n\n/**\n * Classes de vol selon le backend\n */\nexport enum FlightClass {\n  ECONOMY = 1,\n  PREMIUM_ECONOMY = 2,\n  BUSINESS = 3,\n  FIRST = 4\n}\n\n/**\n * Types de produit selon le backend\n */\nexport enum ProductType {\n  FLIGHT = 2\n}\n\n/**\n * Types de réponse de vol supportés\n */\nexport enum FlightResponseListType {\n  STANDARD = 1,\n  DETAILED = 2\n}\n\n// ===== INTERFACES UTILITAIRES =====\n\n/**\n * Interface pour créer facilement une Location\n */\nexport interface LocationBuilder {\n  id: string;\n  type?: LocationType;\n  provider?: number;\n}\n\n/**\n * Interface pour créer facilement un Passenger\n */\nexport interface PassengerBuilder {\n  type: PassengerType;\n  count: number;\n}\n\n/**\n * Interface pour les paramètres de base d'une requête RoundTrip\n */\nexport interface RoundTripRequestParams {\n  departureLocation: string;\n  arrivalLocation: string;\n  departureDate: string;\n  returnDate: string;\n  passengers: {\n    adults: number;\n    children?: number;\n    infants?: number;\n  };\n  flightClass?: FlightClass;\n  directFlightsOnly?: boolean;\n  culture?: string;\n  currency?: string;\n}\n\n// ===== FONCTIONS UTILITAIRES =====\n\n/**\n * Crée une Location à partir de paramètres simplifiés\n */\nexport function createLocation(params: LocationBuilder): Location {\n  return {\n    id: params.id.toUpperCase(), // Convertir en majuscules pour les codes IATA\n    type: params.type || LocationType.AIRPORT,\n    provider: params.provider || 1\n  };\n}\n\n/**\n * Crée un Passenger à partir de paramètres simplifiés\n */\nexport function createPassenger(params: PassengerBuilder): Passenger {\n  return {\n    type: params.type,\n    count: params.count\n  };\n}\n\n/**\n * Crée une liste de passagers à partir de paramètres simplifiés\n */\nexport function createPassengerList(passengers: { adults: number; children?: number; infants?: number }): Passenger[] {\n  const passengerList: Passenger[] = [];\n  \n  if (passengers.adults > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.ADULT, count: passengers.adults }));\n  }\n  \n  if (passengers.children && passengers.children > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.CHILD, count: passengers.children }));\n  }\n  \n  if (passengers.infants && passengers.infants > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.INFANT, count: passengers.infants }));\n  }\n  \n  return passengerList;\n}\n\n/**\n * Calcule le nombre de nuits entre deux dates\n */\nexport function calculateNights(checkInDate: string, returnDate: string): number {\n  const checkIn = new Date(checkInDate);\n  const checkOut = new Date(returnDate);\n  const timeDiff = checkOut.getTime() - checkIn.getTime();\n  return Math.ceil(timeDiff / (1000 * 3600 * 24));\n}\n\n/**\n * Valeurs par défaut pour une requête RoundTrip\n */\nexport const DEFAULT_ROUNDTRIP_REQUEST: Partial<RoundTripRequest> = {\n  ProductType: ProductType.FLIGHT,\n  ServiceTypes: ['Flight'],\n  acceptPendingProviders: true,\n  forceFlightBundlePackage: false,\n  disablePackageOfferTotalPrice: false,\n  supportedFlightReponseListTypes: [FlightResponseListType.STANDARD, FlightResponseListType.DETAILED],\n  showOnlyNonStopFlight: false,\n  calculateFlightFees: true,\n  Culture: 'fr-FR',\n  Currency: 'EUR'\n};\n\n/**\n * Crée une requête RoundTrip complète à partir de paramètres simplifiés\n */\nexport function createRoundTripRequest(params: RoundTripRequestParams): RoundTripRequest {\n  const nights = calculateNights(params.departureDate, params.returnDate);\n  \n  return {\n    ...DEFAULT_ROUNDTRIP_REQUEST,\n    DepartureLocations: [createLocation({ id: params.departureLocation })],\n    ArrivalLocations: [createLocation({ id: params.arrivalLocation })],\n    CheckIn: params.departureDate,\n    Night: nights,\n    Passengers: createPassengerList(params.passengers),\n    showOnlyNonStopFlight: params.directFlightsOnly || false,\n    Culture: params.culture || 'fr-FR',\n    Currency: params.currency || 'EUR'\n  } as RoundTripRequest;\n}\n\n/**\n * Valide une requête RoundTrip\n */\nexport function validateRoundTripRequest(request: RoundTripRequest): string[] {\n  const errors: string[] = [];\n  \n  if (!request.CheckIn) {\n    errors.push('CheckIn date is required');\n  }\n  \n  if (request.Night <= 0) {\n    errors.push('Night must be greater than 0');\n  }\n  \n  if (!request.DepartureLocations || request.DepartureLocations.length === 0) {\n    errors.push('At least one departure location is required');\n  }\n  \n  if (!request.ArrivalLocations || request.ArrivalLocations.length === 0) {\n    errors.push('At least one arrival location is required');\n  }\n  \n  if (!request.Passengers || request.Passengers.length === 0) {\n    errors.push('At least one passenger is required');\n  }\n  \n  if (!request.Culture) {\n    errors.push('Culture is required');\n  }\n  \n  if (!request.Currency) {\n    errors.push('Currency is required');\n  }\n  \n  return errors;\n}\n"], "mappings": "AAAA;;;;AAoKA;AAEA;;;AAGA,WAAYA,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EAJWA,aAAa,KAAbA,aAAa;AAMzB;;;AAGA,WAAYC,YAIX;AAJD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EAJWA,YAAY,KAAZA,YAAY;AAMxB;;;AAGA,WAAYC,WAKX;AALD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,4CAAmB;EACnBA,WAAA,CAAAA,WAAA,8BAAY;EACZA,WAAA,CAAAA,WAAA,wBAAS;AACX,CAAC,EALWA,WAAW,KAAXA,WAAW;AAOvB;;;AAGA,WAAYC,WAEX;AAFD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,0BAAU;AACZ,CAAC,EAFWA,WAAW,KAAXA,WAAW;AAIvB;;;AAGA,WAAYC,sBAGX;AAHD,WAAYA,sBAAsB;EAChCA,sBAAA,CAAAA,sBAAA,8BAAY;EACZA,sBAAA,CAAAA,sBAAA,8BAAY;AACd,CAAC,EAHWA,sBAAsB,KAAtBA,sBAAsB;AA2ClC;AAEA;;;AAGA,OAAM,SAAUC,cAAcA,CAACC,MAAuB;EACpD,OAAO;IACLC,EAAE,EAAED,MAAM,CAACC,EAAE,CAACC,WAAW,EAAE;IAC3BC,IAAI,EAAEH,MAAM,CAACG,IAAI,IAAIR,YAAY,CAACS,OAAO;IACzCC,QAAQ,EAAEL,MAAM,CAACK,QAAQ,IAAI;GAC9B;AACH;AAEA;;;AAGA,OAAM,SAAUC,eAAeA,CAACN,MAAwB;EACtD,OAAO;IACLG,IAAI,EAAEH,MAAM,CAACG,IAAI;IACjBI,KAAK,EAAEP,MAAM,CAACO;GACf;AACH;AAEA;;;AAGA,OAAM,SAAUC,mBAAmBA,CAACC,UAAmE;EACrG,MAAMC,aAAa,GAAgB,EAAE;EAErC,IAAID,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACzBD,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAET,aAAa,CAACmB,KAAK;MAAEN,KAAK,EAAEE,UAAU,CAACE;IAAM,CAAE,CAAC,CAAC;;EAG9F,IAAIF,UAAU,CAACK,QAAQ,IAAIL,UAAU,CAACK,QAAQ,GAAG,CAAC,EAAE;IAClDJ,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAET,aAAa,CAACqB,KAAK;MAAER,KAAK,EAAEE,UAAU,CAACK;IAAQ,CAAE,CAAC,CAAC;;EAGhG,IAAIL,UAAU,CAACO,OAAO,IAAIP,UAAU,CAACO,OAAO,GAAG,CAAC,EAAE;IAChDN,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAET,aAAa,CAACuB,MAAM;MAAEV,KAAK,EAAEE,UAAU,CAACO;IAAO,CAAE,CAAC,CAAC;;EAGhG,OAAON,aAAa;AACtB;AAEA;;;AAGA,OAAM,SAAUQ,eAAeA,CAACC,WAAmB,EAAEC,UAAkB;EACrE,MAAMC,OAAO,GAAG,IAAIC,IAAI,CAACH,WAAW,CAAC;EACrC,MAAMI,QAAQ,GAAG,IAAID,IAAI,CAACF,UAAU,CAAC;EACrC,MAAMI,QAAQ,GAAGD,QAAQ,CAACE,OAAO,EAAE,GAAGJ,OAAO,CAACI,OAAO,EAAE;EACvD,OAAOC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC,CAAC;AACjD;AAEA;;;AAGA,OAAO,MAAMI,yBAAyB,GAA8B;EAClE/B,WAAW,EAAEA,WAAW,CAACgC,MAAM;EAC/BC,YAAY,EAAE,CAAC,QAAQ,CAAC;EACxBC,sBAAsB,EAAE,IAAI;EAC5BC,wBAAwB,EAAE,KAAK;EAC/BC,6BAA6B,EAAE,KAAK;EACpCC,+BAA+B,EAAE,CAACpC,sBAAsB,CAACqC,QAAQ,EAAErC,sBAAsB,CAACsC,QAAQ,CAAC;EACnGC,qBAAqB,EAAE,KAAK;EAC5BC,mBAAmB,EAAE,IAAI;EACzBC,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;CACX;AAED;;;AAGA,OAAM,SAAUC,sBAAsBA,CAACzC,MAA8B;EACnE,MAAM0C,MAAM,GAAGxB,eAAe,CAAClB,MAAM,CAAC2C,aAAa,EAAE3C,MAAM,CAACoB,UAAU,CAAC;EAEvE,OAAO;IACL,GAAGQ,yBAAyB;IAC5BgB,kBAAkB,EAAE,CAAC7C,cAAc,CAAC;MAAEE,EAAE,EAAED,MAAM,CAAC6C;IAAiB,CAAE,CAAC,CAAC;IACtEC,gBAAgB,EAAE,CAAC/C,cAAc,CAAC;MAAEE,EAAE,EAAED,MAAM,CAAC+C;IAAe,CAAE,CAAC,CAAC;IAClEC,OAAO,EAAEhD,MAAM,CAAC2C,aAAa;IAC7BM,KAAK,EAAEP,MAAM;IACbQ,UAAU,EAAE1C,mBAAmB,CAACR,MAAM,CAACS,UAAU,CAAC;IAClD4B,qBAAqB,EAAErC,MAAM,CAACmD,iBAAiB,IAAI,KAAK;IACxDZ,OAAO,EAAEvC,MAAM,CAACoD,OAAO,IAAI,OAAO;IAClCZ,QAAQ,EAAExC,MAAM,CAACqD,QAAQ,IAAI;GACV;AACvB;AAEA;;;AAGA,OAAM,SAAUC,wBAAwBA,CAACC,OAAyB;EAChE,MAAMC,MAAM,GAAa,EAAE;EAE3B,IAAI,CAACD,OAAO,CAACP,OAAO,EAAE;IACpBQ,MAAM,CAAC5C,IAAI,CAAC,0BAA0B,CAAC;;EAGzC,IAAI2C,OAAO,CAACN,KAAK,IAAI,CAAC,EAAE;IACtBO,MAAM,CAAC5C,IAAI,CAAC,8BAA8B,CAAC;;EAG7C,IAAI,CAAC2C,OAAO,CAACX,kBAAkB,IAAIW,OAAO,CAACX,kBAAkB,CAACa,MAAM,KAAK,CAAC,EAAE;IAC1ED,MAAM,CAAC5C,IAAI,CAAC,6CAA6C,CAAC;;EAG5D,IAAI,CAAC2C,OAAO,CAACT,gBAAgB,IAAIS,OAAO,CAACT,gBAAgB,CAACW,MAAM,KAAK,CAAC,EAAE;IACtED,MAAM,CAAC5C,IAAI,CAAC,2CAA2C,CAAC;;EAG1D,IAAI,CAAC2C,OAAO,CAACL,UAAU,IAAIK,OAAO,CAACL,UAAU,CAACO,MAAM,KAAK,CAAC,EAAE;IAC1DD,MAAM,CAAC5C,IAAI,CAAC,oCAAoC,CAAC;;EAGnD,IAAI,CAAC2C,OAAO,CAAChB,OAAO,EAAE;IACpBiB,MAAM,CAAC5C,IAAI,CAAC,qBAAqB,CAAC;;EAGpC,IAAI,CAAC2C,OAAO,CAACf,QAAQ,EAAE;IACrBgB,MAAM,CAAC5C,IAAI,CAAC,sBAAsB,CAAC;;EAGrC,OAAO4C,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}