{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AirlineLogoService {\n  constructor() {\n    this.logoMap = {\n      'TK': 'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png',\n      'TU': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/1200px-Tunisair_Logo.svg.png',\n      'AF': 'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png',\n      'LH': 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png',\n      'EK': 'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png',\n      'QR': 'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png',\n      'BA': 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png',\n      'KL': 'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png',\n      'IB': 'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png',\n      'AZ': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/1200px-Alitalia_logo_2015.svg.png'\n    };\n  }\n  /**\n   * Obtient le logo d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode) {\n    return this.logoMap[airlineCode] || null;\n  }\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode) {\n    return airlineCode in this.logoMap;\n  }\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines() {\n    return Object.keys(this.logoMap);\n  }\n  static {\n    this.ɵfac = function AirlineLogoService_Factory(t) {\n      return new (t || AirlineLogoService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AirlineLogoService,\n      factory: AirlineLogoService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["AirlineLogoService", "constructor", "logoMap", "getAirlineLogo", "airlineCode", "<PERSON><PERSON><PERSON>", "getAvailableAirlines", "Object", "keys", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\airline-logo.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AirlineLogoService {\n  private logoMap: { [key: string]: string } = {\n    'TK': 'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png',\n    'TU': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/1200px-Tunisair_Logo.svg.png',\n    'AF': 'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png',\n    'LH': 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png',\n    'EK': 'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png',\n    'QR': 'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png',\n    'BA': 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png',\n    'KL': 'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png',\n    'IB': 'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png',\n    'AZ': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/1200px-Alitalia_logo_2015.svg.png'\n  };\n\n  constructor() { }\n\n  /**\n   * Obtient le logo d'une compagnie aérienne\n   */\n  getAirlineLogo(airlineCode: string): string | null {\n    return this.logoMap[airlineCode] || null;\n  }\n\n  /**\n   * Vérifie si un logo existe pour une compagnie\n   */\n  hasLogo(airlineCode: string): boolean {\n    return airlineCode in this.logoMap;\n  }\n\n  /**\n   * Obtient tous les codes de compagnies avec logos\n   */\n  getAvailableAirlines(): string[] {\n    return Object.keys(this.logoMap);\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,kBAAkB;EAc7BC,YAAA;IAbQ,KAAAC,OAAO,GAA8B;MAC3C,IAAI,EAAE,8EAA8E;MACpF,IAAI,EAAE,0GAA0G;MAChH,IAAI,EAAE,wEAAwE;MAC9E,IAAI,EAAE,uEAAuE;MAC7E,IAAI,EAAE,sEAAsE;MAC5E,IAAI,EAAE,2EAA2E;MACjF,IAAI,EAAE,6EAA6E;MACnF,IAAI,EAAE,iEAAiE;MACvE,IAAI,EAAE,oEAAoE;MAC1E,IAAI,EAAE;KACP;EAEe;EAEhB;;;EAGAC,cAAcA,CAACC,WAAmB;IAChC,OAAO,IAAI,CAACF,OAAO,CAACE,WAAW,CAAC,IAAI,IAAI;EAC1C;EAEA;;;EAGAC,OAAOA,CAACD,WAAmB;IACzB,OAAOA,WAAW,IAAI,IAAI,CAACF,OAAO;EACpC;EAEA;;;EAGAI,oBAAoBA,CAAA;IAClB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO,CAAC;EAClC;;;uBAnCWF,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAS,OAAA,EAAlBT,kBAAkB,CAAAU,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}