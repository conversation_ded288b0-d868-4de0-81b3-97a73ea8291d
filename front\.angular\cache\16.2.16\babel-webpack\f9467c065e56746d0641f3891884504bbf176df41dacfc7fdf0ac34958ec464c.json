{"ast": null, "code": "/**\n * Interface TypeScript correspondant exactement à la classe Java OneWayRequest\n * Générée à partir du modèle backend com.paximum.demo.models.OneWayRequest\n */\n// ===== TYPES D'ÉNUMÉRATION =====\n/**\n * Types de passagers selon le backend\n */\nexport var PassengerType;\n(function (PassengerType) {\n  PassengerType[PassengerType[\"ADULT\"] = 1] = \"ADULT\";\n  PassengerType[PassengerType[\"CHILD\"] = 2] = \"CHILD\";\n  PassengerType[PassengerType[\"INFANT\"] = 3] = \"INFANT\";\n})(PassengerType || (PassengerType = {}));\n/**\n * Types de localisation selon le backend\n */\nexport var LocationType;\n(function (LocationType) {\n  LocationType[LocationType[\"AIRPORT\"] = 1] = \"AIRPORT\";\n  LocationType[LocationType[\"CITY\"] = 2] = \"CITY\";\n  LocationType[LocationType[\"COUNTRY\"] = 3] = \"COUNTRY\";\n})(LocationType || (LocationType = {}));\n/**\n * Classes de vol selon le backend\n */\nexport var FlightClass;\n(function (FlightClass) {\n  FlightClass[FlightClass[\"ECONOMY\"] = 1] = \"ECONOMY\";\n  FlightClass[FlightClass[\"PREMIUM_ECONOMY\"] = 2] = \"PREMIUM_ECONOMY\";\n  FlightClass[FlightClass[\"BUSINESS\"] = 3] = \"BUSINESS\";\n  FlightClass[FlightClass[\"FIRST\"] = 4] = \"FIRST\";\n})(FlightClass || (FlightClass = {}));\n/**\n * Types de produit selon le backend\n */\nexport var ProductType;\n(function (ProductType) {\n  ProductType[ProductType[\"FLIGHT\"] = 2] = \"FLIGHT\";\n})(ProductType || (ProductType = {}));\n// ===== FONCTIONS UTILITAIRES =====\n/**\n * Crée une Location à partir de paramètres simplifiés\n */\nexport function createLocation(params) {\n  return {\n    id: params.id,\n    type: params.type || LocationType.AIRPORT,\n    provider: params.provider || 1\n  };\n}\n/**\n * Crée un Passenger à partir de paramètres simplifiés\n */\nexport function createPassenger(params) {\n  return {\n    type: params.type,\n    count: params.count\n  };\n}\n/**\n * Crée une liste de passagers à partir de paramètres simplifiés\n */\nexport function createPassengerList(passengers) {\n  const passengerList = [];\n  if (passengers.adults > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.ADULT,\n      count: passengers.adults\n    }));\n  }\n  if (passengers.children && passengers.children > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.CHILD,\n      count: passengers.children\n    }));\n  }\n  if (passengers.infants && passengers.infants > 0) {\n    passengerList.push(createPassenger({\n      type: PassengerType.INFANT,\n      count: passengers.infants\n    }));\n  }\n  return passengerList;\n}\n/**\n * Valeurs par défaut pour une requête OneWay\n */\nexport const DEFAULT_ONEWAY_REQUEST = {\n  ProductType: ProductType.FLIGHT,\n  ServiceTypes: ['Flight'],\n  showOnlyNonStopFlight: false,\n  acceptPendingProviders: true,\n  forceFlightBundlePackage: false,\n  disablePackageOfferTotalPrice: false,\n  calculateFlightFees: true,\n  flightClasses: [FlightClass.ECONOMY],\n  Culture: 'fr-FR',\n  Currency: 'EUR'\n};\n/**\n * Crée une requête OneWay complète à partir de paramètres simplifiés\n */\nexport function createOneWayRequest(params) {\n  return {\n    ...DEFAULT_ONEWAY_REQUEST,\n    CheckIn: params.departureDate,\n    DepartureLocations: [createLocation({\n      id: params.departureLocation\n    })],\n    ArrivalLocations: [createLocation({\n      id: params.arrivalLocation\n    })],\n    Passengers: createPassengerList(params.passengers),\n    showOnlyNonStopFlight: params.directFlightsOnly || false,\n    flightClasses: params.flightClass ? [params.flightClass] : [FlightClass.ECONOMY],\n    Culture: params.culture || 'fr-FR',\n    Currency: params.currency || 'EUR'\n  };\n}", "map": {"version": 3, "names": ["PassengerType", "LocationType", "FlightClass", "ProductType", "createLocation", "params", "id", "type", "AIRPORT", "provider", "create<PERSON><PERSON>eng<PERSON>", "count", "createPassengerList", "passengers", "passengerList", "adults", "push", "ADULT", "children", "CHILD", "infants", "INFANT", "DEFAULT_ONEWAY_REQUEST", "FLIGHT", "ServiceTypes", "showOnlyNonStopFlight", "acceptPendingProviders", "forceFlightBundlePackage", "disablePackageOfferTotalPrice", "calculateFlightFees", "flightClasses", "ECONOMY", "Culture", "<PERSON><PERSON><PERSON><PERSON>", "createOneWayRequest", "CheckIn", "departureDate", "DepartureLocations", "departureLocation", "ArrivalLocations", "arrivalLocation", "Passengers", "directFlightsOnly", "flightClass", "culture", "currency"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\models\\oneway-request.interface.ts"], "sourcesContent": ["/**\n * Interface TypeScript correspondant exactement à la classe Java OneWayRequest\n * Générée à partir du modèle backend com.paximum.demo.models.OneWayRequest\n */\n\n// ===== INTERFACES COMMUNES =====\n\n/**\n * Interface correspondant à OneWayRequest.Location\n */\nexport interface Location {\n  id: string;\n  type: number;\n  provider?: number;\n}\n\n/**\n * Interface correspondant à OneWayRequest.Passenger\n */\nexport interface Passenger {\n  type: number;\n  count: number;\n}\n\n/**\n * Interface correspondant à OneWayRequest.CorporateRule\n */\nexport interface CorporateRule {\n  Airline: string;\n  Supplier: string;\n}\n\n/**\n * Interface correspondant à OneWayRequest.CorporateCode\n */\nexport interface CorporateCode {\n  Code: string;\n  Rule: CorporateRule;\n}\n\n/**\n * Interface correspondant à OneWayRequest.GetOptionsParameters\n */\nexport interface GetOptionsParameters {\n  flightBaggageGetOption: number;\n}\n\n/**\n * Interface correspondant à OneWayRequest.AdditionalParameters\n */\nexport interface AdditionalParameters {\n  getOptionsParameters?: GetOptionsParameters;\n  CorporateCodes?: CorporateCode[];\n}\n\n// ===== ONE WAY REQUEST =====\n\n/**\n * Interface correspondant exactement à la classe Java OneWayRequest\n * Tous les noms de propriétés correspondent aux @JsonProperty du backend\n * \n * @example\n * ```typescript\n * const oneWayRequest: OneWayRequest = {\n *   ProductType: 2,\n *   ServiceTypes: ['Flight'],\n *   CheckIn: '2024-06-15',\n *   DepartureLocations: [{ id: 'IST', type: 1, provider: 1 }],\n *   ArrivalLocations: [{ id: 'TUN', type: 1, provider: 1 }],\n *   Passengers: [{ type: 1, count: 1 }],\n *   showOnlyNonStopFlight: false,\n *   acceptPendingProviders: true,\n *   forceFlightBundlePackage: false,\n *   disablePackageOfferTotalPrice: false,\n *   calculateFlightFees: true,\n *   flightClasses: [1],\n *   Culture: 'fr-FR',\n *   Currency: 'EUR'\n * };\n * ```\n */\nexport interface OneWayRequest {\n  /**\n   * Type de produit (généralement 2 pour les vols)\n   */\n  ProductType: number;\n\n  /**\n   * Types de services (ex: ['Flight'])\n   */\n  ServiceTypes: string[];\n\n  /**\n   * Date de départ au format ISO (YYYY-MM-DD)\n   */\n  CheckIn: string;\n\n  /**\n   * Liste des lieux de départ\n   */\n  DepartureLocations: Location[];\n\n  /**\n   * Liste des lieux d'arrivée\n   */\n  ArrivalLocations: Location[];\n\n  /**\n   * Liste des passagers avec leur type et nombre\n   */\n  Passengers: Passenger[];\n\n  /**\n   * Afficher uniquement les vols sans escale\n   */\n  showOnlyNonStopFlight: boolean;\n\n  /**\n   * Paramètres additionnels optionnels\n   */\n  additionalParameters?: AdditionalParameters;\n\n  /**\n   * Accepter les fournisseurs en attente\n   */\n  acceptPendingProviders: boolean;\n\n  /**\n   * Forcer le package de vol groupé\n   */\n  forceFlightBundlePackage: boolean;\n\n  /**\n   * Désactiver le prix total de l'offre package\n   */\n  disablePackageOfferTotalPrice: boolean;\n\n  /**\n   * Calculer les frais de vol\n   */\n  calculateFlightFees: boolean;\n\n  /**\n   * Classes de vol (1: Economy, 2: Premium Economy, 3: Business, 4: First)\n   */\n  flightClasses: number[];\n\n  /**\n   * Culture/Langue (ex: 'fr-FR', 'en-US')\n   */\n  Culture: string;\n\n  /**\n   * Devise (ex: 'EUR', 'USD')\n   */\n  Currency: string;\n}\n\n// ===== TYPES D'ÉNUMÉRATION =====\n\n/**\n * Types de passagers selon le backend\n */\nexport enum PassengerType {\n  ADULT = 1,\n  CHILD = 2,\n  INFANT = 3\n}\n\n/**\n * Types de localisation selon le backend\n */\nexport enum LocationType {\n  AIRPORT = 1,\n  CITY = 2,\n  COUNTRY = 3\n}\n\n/**\n * Classes de vol selon le backend\n */\nexport enum FlightClass {\n  ECONOMY = 1,\n  PREMIUM_ECONOMY = 2,\n  BUSINESS = 3,\n  FIRST = 4\n}\n\n/**\n * Types de produit selon le backend\n */\nexport enum ProductType {\n  FLIGHT = 2\n}\n\n// ===== INTERFACES UTILITAIRES =====\n\n/**\n * Interface pour créer facilement une Location\n */\nexport interface LocationBuilder {\n  id: string;\n  type?: LocationType;\n  provider?: number;\n}\n\n/**\n * Interface pour créer facilement un Passenger\n */\nexport interface PassengerBuilder {\n  type: PassengerType;\n  count: number;\n}\n\n/**\n * Interface pour les paramètres de base d'une requête OneWay\n */\nexport interface OneWayRequestParams {\n  departureLocation: string;\n  arrivalLocation: string;\n  departureDate: string;\n  passengers: {\n    adults: number;\n    children?: number;\n    infants?: number;\n  };\n  flightClass?: FlightClass;\n  directFlightsOnly?: boolean;\n  culture?: string;\n  currency?: string;\n}\n\n// ===== FONCTIONS UTILITAIRES =====\n\n/**\n * Crée une Location à partir de paramètres simplifiés\n */\nexport function createLocation(params: LocationBuilder): Location {\n  return {\n    id: params.id,\n    type: params.type || LocationType.AIRPORT,\n    provider: params.provider || 1\n  };\n}\n\n/**\n * Crée un Passenger à partir de paramètres simplifiés\n */\nexport function createPassenger(params: PassengerBuilder): Passenger {\n  return {\n    type: params.type,\n    count: params.count\n  };\n}\n\n/**\n * Crée une liste de passagers à partir de paramètres simplifiés\n */\nexport function createPassengerList(passengers: { adults: number; children?: number; infants?: number }): Passenger[] {\n  const passengerList: Passenger[] = [];\n  \n  if (passengers.adults > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.ADULT, count: passengers.adults }));\n  }\n  \n  if (passengers.children && passengers.children > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.CHILD, count: passengers.children }));\n  }\n  \n  if (passengers.infants && passengers.infants > 0) {\n    passengerList.push(createPassenger({ type: PassengerType.INFANT, count: passengers.infants }));\n  }\n  \n  return passengerList;\n}\n\n/**\n * Valeurs par défaut pour une requête OneWay\n */\nexport const DEFAULT_ONEWAY_REQUEST: Partial<OneWayRequest> = {\n  ProductType: ProductType.FLIGHT,\n  ServiceTypes: ['Flight'],\n  showOnlyNonStopFlight: false,\n  acceptPendingProviders: true,\n  forceFlightBundlePackage: false,\n  disablePackageOfferTotalPrice: false,\n  calculateFlightFees: true,\n  flightClasses: [FlightClass.ECONOMY],\n  Culture: 'fr-FR',\n  Currency: 'EUR'\n};\n\n/**\n * Crée une requête OneWay complète à partir de paramètres simplifiés\n */\nexport function createOneWayRequest(params: OneWayRequestParams): OneWayRequest {\n  return {\n    ...DEFAULT_ONEWAY_REQUEST,\n    CheckIn: params.departureDate,\n    DepartureLocations: [createLocation({ id: params.departureLocation })],\n    ArrivalLocations: [createLocation({ id: params.arrivalLocation })],\n    Passengers: createPassengerList(params.passengers),\n    showOnlyNonStopFlight: params.directFlightsOnly || false,\n    flightClasses: params.flightClass ? [params.flightClass] : [FlightClass.ECONOMY],\n    Culture: params.culture || 'fr-FR',\n    Currency: params.currency || 'EUR'\n  } as OneWayRequest;\n}\n"], "mappings": "AAAA;;;;AA8JA;AAEA;;;AAGA,WAAYA,aAIX;AAJD,WAAYA,aAAa;EACvBA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,wBAAS;EACTA,aAAA,CAAAA,aAAA,0BAAU;AACZ,CAAC,EAJWA,aAAa,KAAbA,aAAa;AAMzB;;;AAGA,WAAYC,YAIX;AAJD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EAJWA,YAAY,KAAZA,YAAY;AAMxB;;;AAGA,WAAYC,WAKX;AALD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,4BAAW;EACXA,WAAA,CAAAA,WAAA,4CAAmB;EACnBA,WAAA,CAAAA,WAAA,8BAAY;EACZA,WAAA,CAAAA,WAAA,wBAAS;AACX,CAAC,EALWA,WAAW,KAAXA,WAAW;AAOvB;;;AAGA,WAAYC,WAEX;AAFD,WAAYA,WAAW;EACrBA,WAAA,CAAAA,WAAA,0BAAU;AACZ,CAAC,EAFWA,WAAW,KAAXA,WAAW;AAyCvB;AAEA;;;AAGA,OAAM,SAAUC,cAAcA,CAACC,MAAuB;EACpD,OAAO;IACLC,EAAE,EAAED,MAAM,CAACC,EAAE;IACbC,IAAI,EAAEF,MAAM,CAACE,IAAI,IAAIN,YAAY,CAACO,OAAO;IACzCC,QAAQ,EAAEJ,MAAM,CAACI,QAAQ,IAAI;GAC9B;AACH;AAEA;;;AAGA,OAAM,SAAUC,eAAeA,CAACL,MAAwB;EACtD,OAAO;IACLE,IAAI,EAAEF,MAAM,CAACE,IAAI;IACjBI,KAAK,EAAEN,MAAM,CAACM;GACf;AACH;AAEA;;;AAGA,OAAM,SAAUC,mBAAmBA,CAACC,UAAmE;EACrG,MAAMC,aAAa,GAAgB,EAAE;EAErC,IAAID,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IACzBD,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAEP,aAAa,CAACiB,KAAK;MAAEN,KAAK,EAAEE,UAAU,CAACE;IAAM,CAAE,CAAC,CAAC;;EAG9F,IAAIF,UAAU,CAACK,QAAQ,IAAIL,UAAU,CAACK,QAAQ,GAAG,CAAC,EAAE;IAClDJ,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAEP,aAAa,CAACmB,KAAK;MAAER,KAAK,EAAEE,UAAU,CAACK;IAAQ,CAAE,CAAC,CAAC;;EAGhG,IAAIL,UAAU,CAACO,OAAO,IAAIP,UAAU,CAACO,OAAO,GAAG,CAAC,EAAE;IAChDN,aAAa,CAACE,IAAI,CAACN,eAAe,CAAC;MAAEH,IAAI,EAAEP,aAAa,CAACqB,MAAM;MAAEV,KAAK,EAAEE,UAAU,CAACO;IAAO,CAAE,CAAC,CAAC;;EAGhG,OAAON,aAAa;AACtB;AAEA;;;AAGA,OAAO,MAAMQ,sBAAsB,GAA2B;EAC5DnB,WAAW,EAAEA,WAAW,CAACoB,MAAM;EAC/BC,YAAY,EAAE,CAAC,QAAQ,CAAC;EACxBC,qBAAqB,EAAE,KAAK;EAC5BC,sBAAsB,EAAE,IAAI;EAC5BC,wBAAwB,EAAE,KAAK;EAC/BC,6BAA6B,EAAE,KAAK;EACpCC,mBAAmB,EAAE,IAAI;EACzBC,aAAa,EAAE,CAAC5B,WAAW,CAAC6B,OAAO,CAAC;EACpCC,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;CACX;AAED;;;AAGA,OAAM,SAAUC,mBAAmBA,CAAC7B,MAA2B;EAC7D,OAAO;IACL,GAAGiB,sBAAsB;IACzBa,OAAO,EAAE9B,MAAM,CAAC+B,aAAa;IAC7BC,kBAAkB,EAAE,CAACjC,cAAc,CAAC;MAAEE,EAAE,EAAED,MAAM,CAACiC;IAAiB,CAAE,CAAC,CAAC;IACtEC,gBAAgB,EAAE,CAACnC,cAAc,CAAC;MAAEE,EAAE,EAAED,MAAM,CAACmC;IAAe,CAAE,CAAC,CAAC;IAClEC,UAAU,EAAE7B,mBAAmB,CAACP,MAAM,CAACQ,UAAU,CAAC;IAClDY,qBAAqB,EAAEpB,MAAM,CAACqC,iBAAiB,IAAI,KAAK;IACxDZ,aAAa,EAAEzB,MAAM,CAACsC,WAAW,GAAG,CAACtC,MAAM,CAACsC,WAAW,CAAC,GAAG,CAACzC,WAAW,CAAC6B,OAAO,CAAC;IAChFC,OAAO,EAAE3B,MAAM,CAACuC,OAAO,IAAI,OAAO;IAClCX,QAAQ,EAAE5B,MAAM,CAACwC,QAAQ,IAAI;GACb;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}