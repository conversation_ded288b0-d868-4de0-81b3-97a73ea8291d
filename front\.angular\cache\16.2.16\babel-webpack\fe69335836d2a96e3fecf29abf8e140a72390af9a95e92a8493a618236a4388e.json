{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n// Import des interfaces de requête\nimport { createOneWayRequest } from '../models/oneway-request.interface';\nimport { createRoundTripRequest } from '../models/roundtrip-request.interface';\nimport { createMulticityRequest } from '../models/multicity-request.interface';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\n/**\n * Service pour la recherche de vols correspondant exactement aux endpoints backend\n * Endpoints backend:\n * - POST /product/search/oneway\n * - POST /product/search/roundtrip\n * - POST /product/search/multicity\n * - POST /product/getoffers\n */\nexport class FlightSearchService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = environment.apiUrl;\n  }\n  // ===== RECHERCHES DE VOLS =====\n  /**\n   * Recherche de vols aller simple\n   * Correspond à: POST /product/search/oneway\n   */\n  searchOneWay(request) {\n    const headers = this.getHeaders();\n    // Log de la requête pour debug\n    console.log('=== ONE WAY REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/oneway`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('=====================');\n    return this.http.post(`${this.apiUrl}/product/search/oneway`, request, {\n      headers\n    }).pipe(catchError(this.handleError('Erreur lors de la recherche de vols aller simple')));\n  }\n  /**\n   * Recherche de vols aller-retour\n   * Correspond à: POST /product/search/roundtrip\n   */\n  searchRoundTrip(request) {\n    const headers = this.getHeaders();\n    // Validation de la requête\n    const validationErrors = this.validateRoundTripRequest(request);\n    if (validationErrors.length > 0) {\n      console.error('Erreurs de validation RoundTrip:', validationErrors);\n      return throwError(() => new Error(`Requête invalide: ${validationErrors.join(', ')}`));\n    }\n    // Log de la requête pour debug\n    console.log('=== ROUND TRIP REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/roundtrip`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('Validation: PASSED');\n    console.log('========================');\n    return this.http.post(`${this.apiUrl}/product/search/roundtrip`, request, {\n      headers\n    }).pipe(catchError(this.handleError('Erreur lors de la recherche de vols aller-retour')));\n  }\n  /**\n   * Recherche de vols multi-destinations\n   * Correspond à: POST /product/search/multicity\n   */\n  searchMultiCity(request) {\n    const headers = this.getHeaders();\n    // Log de la requête pour debug\n    console.log('=== MULTICITY REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/multicity`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('========================');\n    return this.http.post(`${this.apiUrl}/product/search/multicity`, request, {\n      headers\n    }).pipe(catchError(this.handleError('Erreur lors de la recherche de vols multi-destinations')));\n  }\n  /**\n   * Récupération des détails d'offres\n   * Correspond à: POST /product/getoffers\n   */\n  getOffers(request) {\n    const headers = this.getHeaders();\n    return this.http.post(`${this.apiUrl}/product/getoffers`, request, {\n      headers\n    }).pipe(catchError(this.handleError('Erreur lors de la récupération des détails des offres')));\n  }\n  // ===== MÉTHODES UTILITAIRES POUR LE FORMULAIRE =====\n  /**\n   * Crée une requête OneWay à partir de paramètres simplifiés\n   */\n  createOneWaySearch(params) {\n    return createOneWayRequest({\n      departureLocation: params.departureLocation,\n      arrivalLocation: params.arrivalLocation,\n      departureDate: params.departureDate,\n      passengers: params.passengers,\n      flightClass: params.flightClass,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n  /**\n   * Crée une requête RoundTrip à partir de paramètres simplifiés\n   */\n  createRoundTripSearch(params) {\n    return createRoundTripRequest({\n      departureLocation: params.departureLocation,\n      arrivalLocation: params.arrivalLocation,\n      departureDate: params.departureDate,\n      returnDate: params.returnDate,\n      passengers: params.passengers,\n      flightClass: params.flightClass,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n  /**\n   * Crée une requête MultiCity à partir de paramètres simplifiés\n   */\n  createMultiCitySearch(params) {\n    return createMulticityRequest({\n      segments: params.segments,\n      passengers: params.passengers,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n  // ===== MÉTHODES DE RECHERCHE UNIFIÉES =====\n  /**\n   * Méthode unifiée pour rechercher des vols selon le type\n   */\n  searchFlights(searchType, params) {\n    switch (searchType) {\n      case 'oneway':\n        const oneWayRequest = this.createOneWaySearch(params);\n        return this.searchOneWay(oneWayRequest);\n      case 'roundtrip':\n        const roundTripRequest = this.createRoundTripSearch(params);\n        return this.searchRoundTrip(roundTripRequest);\n      case 'multicity':\n        const multicityRequest = this.createMultiCitySearch(params);\n        return this.searchMultiCity(multicityRequest);\n      default:\n        return throwError(() => new Error('Type de recherche non supporté'));\n    }\n  }\n  /**\n   * Recherche rapide avec paramètres simplifiés\n   */\n  quickSearch(params) {\n    const searchParams = {\n      departureLocation: params.from,\n      arrivalLocation: params.to,\n      departureDate: params.departureDate,\n      returnDate: params.returnDate,\n      segments: params.segments,\n      passengers: {\n        adults: params.adults || 1,\n        children: params.children || 0,\n        infants: params.infants || 0\n      },\n      directFlightsOnly: params.directOnly || false,\n      culture: 'fr-FR',\n      currency: 'EUR'\n    };\n    return this.searchFlights(params.type, searchParams);\n  }\n  // ===== MÉTHODES UTILITAIRES =====\n  /**\n   * Vérifie si le token d'authentification est valide\n   */\n  isAuthenticated() {\n    const token = localStorage.getItem('auth_token');\n    return !!token;\n  }\n  /**\n   * Formate les paramètres de passagers pour l'affichage\n   */\n  formatPassengerText(passengers) {\n    const parts = [];\n    if (passengers.adults > 0) {\n      parts.push(`${passengers.adults} adulte${passengers.adults > 1 ? 's' : ''}`);\n    }\n    if (passengers.children && passengers.children > 0) {\n      parts.push(`${passengers.children} enfant${passengers.children > 1 ? 's' : ''}`);\n    }\n    if (passengers.infants && passengers.infants > 0) {\n      parts.push(`${passengers.infants} bébé${passengers.infants > 1 ? 's' : ''}`);\n    }\n    return parts.join(', ');\n  }\n  /**\n   * Valide les paramètres de recherche de base\n   */\n  validateSearchParams(params) {\n    const errors = [];\n    if (!params.departureLocation) {\n      errors.push('Lieu de départ requis');\n    }\n    if (!params.arrivalLocation) {\n      errors.push('Lieu d\\'arrivée requis');\n    }\n    if (!params.departureDate) {\n      errors.push('Date de départ requise');\n    }\n    if (params.passengers?.adults < 1) {\n      errors.push('Au moins un adulte requis');\n    }\n    return errors;\n  }\n  // ===== MÉTHODES PRIVÉES =====\n  /**\n   * Génère les en-têtes HTTP avec le token d'authentification\n   * Le backend attend: Authorization: Bearer <token>\n   */\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    if (!token) {\n      throw new Error('Token d\\'authentification manquant');\n    }\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n  }\n  /**\n   * Gestion des erreurs avec message personnalisé\n   */\n  handleError(defaultMessage) {\n    return error => {\n      console.error('=== ERREUR API ===');\n      console.error('Status:', error.status);\n      console.error('Status Text:', error.statusText);\n      console.error('URL:', error.url);\n      console.error('Error Object:', error);\n      console.error('Error Body:', error.error);\n      console.error('================');\n      let errorMessage = defaultMessage;\n      // Gestion des erreurs spécifiques du backend\n      if (error.error?.message) {\n        errorMessage = error.error.message;\n      } else if (error.error?.header?.messages?.length > 0) {\n        // Gestion des messages d'erreur du format backend\n        errorMessage = error.error.header.messages[0].message;\n      } else if (error.status === 401) {\n        errorMessage = 'Token d\\'authentification invalide ou expiré';\n      } else if (error.status === 403) {\n        errorMessage = 'Accès non autorisé';\n      } else if (error.status === 404) {\n        errorMessage = 'Service non trouvé';\n      } else if (error.status === 500) {\n        errorMessage = `Erreur interne du serveur (500). Vérifiez les logs du backend.`;\n        // Ajouter plus de détails pour le debug\n        if (error.error) {\n          console.error('Détails de l\\'erreur 500:', error.error);\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      return throwError(() => new Error(errorMessage));\n    };\n  }\n  static {\n    this.ɵfac = function FlightSearchService_Factory(t) {\n      return new (t || FlightSearchService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: FlightSearchService,\n      factory: FlightSearchService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "throwError", "catchError", "environment", "createOneWayRequest", "createRoundTripRequest", "createMulticityRequest", "FlightSearchService", "constructor", "http", "apiUrl", "searchOneWay", "request", "headers", "getHeaders", "console", "log", "JSON", "stringify", "post", "pipe", "handleError", "searchRoundTrip", "validationErrors", "validateRoundTripRequest", "length", "error", "Error", "join", "searchMultiCity", "getOffers", "createOneWaySearch", "params", "departureLocation", "arrivalLocation", "departureDate", "passengers", "flightClass", "directFlightsOnly", "culture", "currency", "createRoundTripSearch", "returnDate", "createMultiCitySearch", "segments", "searchFlights", "searchType", "oneWayRequest", "roundTripRequest", "multicityRequest", "quickSearch", "searchParams", "from", "to", "adults", "children", "infants", "directOnly", "type", "isAuthenticated", "token", "localStorage", "getItem", "formatPassengerText", "parts", "push", "validateSearchParams", "errors", "defaultMessage", "status", "statusText", "url", "errorMessage", "message", "header", "messages", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\angular\\front\\src\\app\\services\\flight-search.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\n\n// Import des interfaces de requête\nimport { OneWayRequest, createOneWayRequest } from '../models/oneway-request.interface';\nimport { RoundTripRequest, createRoundTripRequest } from '../models/roundtrip-request.interface';\nimport { MulticityRequest, createMulticityRequest } from '../models/multicity-request.interface';\n\n// Import des interfaces de réponse\nimport { OneWayResponse } from '../models/oneway-response.interface';\nimport { RoundTripResponse } from '../models/roundtrip-response.interface';\nimport { MulticityResponse } from '../models/multicity-response.interface';\n\n/**\n * Service pour la recherche de vols correspondant exactement aux endpoints backend\n * Endpoints backend:\n * - POST /product/search/oneway\n * - POST /product/search/roundtrip  \n * - POST /product/search/multicity\n * - POST /product/getoffers\n */\n@Injectable({\n  providedIn: 'root'\n})\nexport class FlightSearchService {\n  private readonly apiUrl = environment.apiUrl;\n\n  constructor(private http: HttpClient) {}\n\n  // ===== RECHERCHES DE VOLS =====\n\n  /**\n   * Recherche de vols aller simple\n   * Correspond à: POST /product/search/oneway\n   */\n  searchOneWay(request: OneWayRequest): Observable<OneWayResponse> {\n    const headers = this.getHeaders();\n\n    // Log de la requête pour debug\n    console.log('=== ONE WAY REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/oneway`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('=====================');\n\n    return this.http.post<OneWayResponse>(\n      `${this.apiUrl}/product/search/oneway`,\n      request,\n      { headers }\n    ).pipe(\n      catchError(this.handleError('Erreur lors de la recherche de vols aller simple'))\n    );\n  }\n\n  /**\n   * Recherche de vols aller-retour\n   * Correspond à: POST /product/search/roundtrip\n   */\n  searchRoundTrip(request: RoundTripRequest): Observable<RoundTripResponse> {\n    const headers = this.getHeaders();\n\n    // Validation de la requête\n    const validationErrors = this.validateRoundTripRequest(request);\n    if (validationErrors.length > 0) {\n      console.error('Erreurs de validation RoundTrip:', validationErrors);\n      return throwError(() => new Error(`Requête invalide: ${validationErrors.join(', ')}`));\n    }\n\n    // Log de la requête pour debug\n    console.log('=== ROUND TRIP REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/roundtrip`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('Validation: PASSED');\n    console.log('========================');\n\n    return this.http.post<RoundTripResponse>(\n      `${this.apiUrl}/product/search/roundtrip`,\n      request,\n      { headers }\n    ).pipe(\n      catchError(this.handleError('Erreur lors de la recherche de vols aller-retour'))\n    );\n  }\n\n  /**\n   * Recherche de vols multi-destinations\n   * Correspond à: POST /product/search/multicity\n   */\n  searchMultiCity(request: MulticityRequest): Observable<MulticityResponse> {\n    const headers = this.getHeaders();\n\n    // Log de la requête pour debug\n    console.log('=== MULTICITY REQUEST ===');\n    console.log('URL:', `${this.apiUrl}/product/search/multicity`);\n    console.log('Headers:', headers);\n    console.log('Request Body:', JSON.stringify(request, null, 2));\n    console.log('========================');\n\n    return this.http.post<MulticityResponse>(\n      `${this.apiUrl}/product/search/multicity`,\n      request,\n      { headers }\n    ).pipe(\n      catchError(this.handleError('Erreur lors de la recherche de vols multi-destinations'))\n    );\n  }\n\n  /**\n   * Récupération des détails d'offres\n   * Correspond à: POST /product/getoffers\n   */\n  getOffers(request: any): Observable<any> {\n    const headers = this.getHeaders();\n    \n    return this.http.post<any>(\n      `${this.apiUrl}/product/getoffers`,\n      request,\n      { headers }\n    ).pipe(\n      catchError(this.handleError('Erreur lors de la récupération des détails des offres'))\n    );\n  }\n\n  // ===== MÉTHODES UTILITAIRES POUR LE FORMULAIRE =====\n\n  /**\n   * Crée une requête OneWay à partir de paramètres simplifiés\n   */\n  createOneWaySearch(params: {\n    departureLocation: string;\n    arrivalLocation: string;\n    departureDate: string;\n    passengers: { adults: number; children?: number; infants?: number };\n    flightClass?: number;\n    directFlightsOnly?: boolean;\n    culture?: string;\n    currency?: string;\n  }): OneWayRequest {\n    return createOneWayRequest({\n      departureLocation: params.departureLocation,\n      arrivalLocation: params.arrivalLocation,\n      departureDate: params.departureDate,\n      passengers: params.passengers,\n      flightClass: params.flightClass,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n\n  /**\n   * Crée une requête RoundTrip à partir de paramètres simplifiés\n   */\n  createRoundTripSearch(params: {\n    departureLocation: string;\n    arrivalLocation: string;\n    departureDate: string;\n    returnDate: string;\n    passengers: { adults: number; children?: number; infants?: number };\n    flightClass?: number;\n    directFlightsOnly?: boolean;\n    culture?: string;\n    currency?: string;\n  }): RoundTripRequest {\n    return createRoundTripRequest({\n      departureLocation: params.departureLocation,\n      arrivalLocation: params.arrivalLocation,\n      departureDate: params.departureDate,\n      returnDate: params.returnDate,\n      passengers: params.passengers,\n      flightClass: params.flightClass,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n\n  /**\n   * Crée une requête MultiCity à partir de paramètres simplifiés\n   */\n  createMultiCitySearch(params: {\n    segments: Array<{ from: string; to: string; date: string }>;\n    passengers: { adults: number; children?: number; infants?: number };\n    directFlightsOnly?: boolean;\n    culture?: string;\n    currency?: string;\n  }): MulticityRequest {\n    return createMulticityRequest({\n      segments: params.segments,\n      passengers: params.passengers,\n      directFlightsOnly: params.directFlightsOnly,\n      culture: params.culture,\n      currency: params.currency\n    });\n  }\n\n  // ===== MÉTHODES DE RECHERCHE UNIFIÉES =====\n\n  /**\n   * Méthode unifiée pour rechercher des vols selon le type\n   */\n  searchFlights(searchType: 'oneway' | 'roundtrip' | 'multicity', params: any): Observable<any> {\n    switch (searchType) {\n      case 'oneway':\n        const oneWayRequest = this.createOneWaySearch(params);\n        return this.searchOneWay(oneWayRequest);\n\n      case 'roundtrip':\n        const roundTripRequest = this.createRoundTripSearch(params);\n        return this.searchRoundTrip(roundTripRequest);\n\n      case 'multicity':\n        const multicityRequest = this.createMultiCitySearch(params);\n        return this.searchMultiCity(multicityRequest);\n\n      default:\n        return throwError(() => new Error('Type de recherche non supporté'));\n    }\n  }\n\n  /**\n   * Recherche rapide avec paramètres simplifiés\n   */\n  quickSearch(params: {\n    type: 'oneway' | 'roundtrip' | 'multicity';\n    from: string;\n    to: string;\n    departureDate: string;\n    returnDate?: string;\n    segments?: Array<{ from: string; to: string; date: string }>;\n    adults?: number;\n    children?: number;\n    infants?: number;\n    directOnly?: boolean;\n  }): Observable<any> {\n    const searchParams = {\n      departureLocation: params.from,\n      arrivalLocation: params.to,\n      departureDate: params.departureDate,\n      returnDate: params.returnDate,\n      segments: params.segments,\n      passengers: {\n        adults: params.adults || 1,\n        children: params.children || 0,\n        infants: params.infants || 0\n      },\n      directFlightsOnly: params.directOnly || false,\n      culture: 'fr-FR',\n      currency: 'EUR'\n    };\n\n    return this.searchFlights(params.type, searchParams);\n  }\n\n  // ===== MÉTHODES UTILITAIRES =====\n\n  /**\n   * Vérifie si le token d'authentification est valide\n   */\n  isAuthenticated(): boolean {\n    const token = localStorage.getItem('auth_token');\n    return !!token;\n  }\n\n  /**\n   * Formate les paramètres de passagers pour l'affichage\n   */\n  formatPassengerText(passengers: { adults: number; children?: number; infants?: number }): string {\n    const parts: string[] = [];\n\n    if (passengers.adults > 0) {\n      parts.push(`${passengers.adults} adulte${passengers.adults > 1 ? 's' : ''}`);\n    }\n\n    if (passengers.children && passengers.children > 0) {\n      parts.push(`${passengers.children} enfant${passengers.children > 1 ? 's' : ''}`);\n    }\n\n    if (passengers.infants && passengers.infants > 0) {\n      parts.push(`${passengers.infants} bébé${passengers.infants > 1 ? 's' : ''}`);\n    }\n\n    return parts.join(', ');\n  }\n\n  /**\n   * Valide les paramètres de recherche de base\n   */\n  validateSearchParams(params: any): string[] {\n    const errors: string[] = [];\n\n    if (!params.departureLocation) {\n      errors.push('Lieu de départ requis');\n    }\n\n    if (!params.arrivalLocation) {\n      errors.push('Lieu d\\'arrivée requis');\n    }\n\n    if (!params.departureDate) {\n      errors.push('Date de départ requise');\n    }\n\n    if (params.passengers?.adults < 1) {\n      errors.push('Au moins un adulte requis');\n    }\n\n    return errors;\n  }\n\n  // ===== MÉTHODES PRIVÉES =====\n\n  /**\n   * Génère les en-têtes HTTP avec le token d'authentification\n   * Le backend attend: Authorization: Bearer <token>\n   */\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n\n    if (!token) {\n      throw new Error('Token d\\'authentification manquant');\n    }\n\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${token}`\n    });\n  }\n\n  /**\n   * Gestion des erreurs avec message personnalisé\n   */\n  private handleError(defaultMessage: string) {\n    return (error: any): Observable<never> => {\n      console.error('=== ERREUR API ===');\n      console.error('Status:', error.status);\n      console.error('Status Text:', error.statusText);\n      console.error('URL:', error.url);\n      console.error('Error Object:', error);\n      console.error('Error Body:', error.error);\n      console.error('================');\n\n      let errorMessage = defaultMessage;\n\n      // Gestion des erreurs spécifiques du backend\n      if (error.error?.message) {\n        errorMessage = error.error.message;\n      } else if (error.error?.header?.messages?.length > 0) {\n        // Gestion des messages d'erreur du format backend\n        errorMessage = error.error.header.messages[0].message;\n      } else if (error.status === 401) {\n        errorMessage = 'Token d\\'authentification invalide ou expiré';\n      } else if (error.status === 403) {\n        errorMessage = 'Accès non autorisé';\n      } else if (error.status === 404) {\n        errorMessage = 'Service non trouvé';\n      } else if (error.status === 500) {\n        errorMessage = `Erreur interne du serveur (500). Vérifiez les logs du backend.`;\n        // Ajouter plus de détails pour le debug\n        if (error.error) {\n          console.error('Détails de l\\'erreur 500:', error.error);\n        }\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      return throwError(() => new Error(errorMessage));\n    };\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,QAAa,gBAAgB;AAChD,SAASC,WAAW,QAAQ,gCAAgC;AAE5D;AACA,SAAwBC,mBAAmB,QAAQ,oCAAoC;AACvF,SAA2BC,sBAAsB,QAAQ,uCAAuC;AAChG,SAA2BC,sBAAsB,QAAQ,uCAAuC;;;AAOhG;;;;;;;;AAWA,OAAM,MAAOC,mBAAmB;EAG9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAFP,KAAAC,MAAM,GAAGP,WAAW,CAACO,MAAM;EAEL;EAEvC;EAEA;;;;EAIAC,YAAYA,CAACC,OAAsB;IACjC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IAEjC;IACAC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,IAAI,CAACN,MAAM,wBAAwB,CAAC;IAC3DK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,OAAO,CAAC;IAChCE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACN,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9DG,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IAEpC,OAAO,IAAI,CAACP,IAAI,CAACU,IAAI,CACnB,GAAG,IAAI,CAACT,MAAM,wBAAwB,EACtCE,OAAO,EACP;MAAEC;IAAO,CAAE,CACZ,CAACO,IAAI,CACJlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,kDAAkD,CAAC,CAAC,CACjF;EACH;EAEA;;;;EAIAC,eAAeA,CAACV,OAAyB;IACvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IAEjC;IACA,MAAMS,gBAAgB,GAAG,IAAI,CAACC,wBAAwB,CAACZ,OAAO,CAAC;IAC/D,IAAIW,gBAAgB,CAACE,MAAM,GAAG,CAAC,EAAE;MAC/BV,OAAO,CAACW,KAAK,CAAC,kCAAkC,EAAEH,gBAAgB,CAAC;MACnE,OAAOtB,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAAC,qBAAqBJ,gBAAgB,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;IAGxF;IACAb,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,IAAI,CAACN,MAAM,2BAA2B,CAAC;IAC9DK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,OAAO,CAAC;IAChCE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACN,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9DG,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjCD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,OAAO,IAAI,CAACP,IAAI,CAACU,IAAI,CACnB,GAAG,IAAI,CAACT,MAAM,2BAA2B,EACzCE,OAAO,EACP;MAAEC;IAAO,CAAE,CACZ,CAACO,IAAI,CACJlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,kDAAkD,CAAC,CAAC,CACjF;EACH;EAEA;;;;EAIAQ,eAAeA,CAACjB,OAAyB;IACvC,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IAEjC;IACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IACxCD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,GAAG,IAAI,CAACN,MAAM,2BAA2B,CAAC;IAC9DK,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,OAAO,CAAC;IAChCE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACN,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9DG,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,OAAO,IAAI,CAACP,IAAI,CAACU,IAAI,CACnB,GAAG,IAAI,CAACT,MAAM,2BAA2B,EACzCE,OAAO,EACP;MAAEC;IAAO,CAAE,CACZ,CAACO,IAAI,CACJlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,wDAAwD,CAAC,CAAC,CACvF;EACH;EAEA;;;;EAIAS,SAASA,CAAClB,OAAY;IACpB,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;IAEjC,OAAO,IAAI,CAACL,IAAI,CAACU,IAAI,CACnB,GAAG,IAAI,CAACT,MAAM,oBAAoB,EAClCE,OAAO,EACP;MAAEC;IAAO,CAAE,CACZ,CAACO,IAAI,CACJlB,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,uDAAuD,CAAC,CAAC,CACtF;EACH;EAEA;EAEA;;;EAGAU,kBAAkBA,CAACC,MASlB;IACC,OAAO5B,mBAAmB,CAAC;MACzB6B,iBAAiB,EAAED,MAAM,CAACC,iBAAiB;MAC3CC,eAAe,EAAEF,MAAM,CAACE,eAAe;MACvCC,aAAa,EAAEH,MAAM,CAACG,aAAa;MACnCC,UAAU,EAAEJ,MAAM,CAACI,UAAU;MAC7BC,WAAW,EAAEL,MAAM,CAACK,WAAW;MAC/BC,iBAAiB,EAAEN,MAAM,CAACM,iBAAiB;MAC3CC,OAAO,EAAEP,MAAM,CAACO,OAAO;MACvBC,QAAQ,EAAER,MAAM,CAACQ;KAClB,CAAC;EACJ;EAEA;;;EAGAC,qBAAqBA,CAACT,MAUrB;IACC,OAAO3B,sBAAsB,CAAC;MAC5B4B,iBAAiB,EAAED,MAAM,CAACC,iBAAiB;MAC3CC,eAAe,EAAEF,MAAM,CAACE,eAAe;MACvCC,aAAa,EAAEH,MAAM,CAACG,aAAa;MACnCO,UAAU,EAAEV,MAAM,CAACU,UAAU;MAC7BN,UAAU,EAAEJ,MAAM,CAACI,UAAU;MAC7BC,WAAW,EAAEL,MAAM,CAACK,WAAW;MAC/BC,iBAAiB,EAAEN,MAAM,CAACM,iBAAiB;MAC3CC,OAAO,EAAEP,MAAM,CAACO,OAAO;MACvBC,QAAQ,EAAER,MAAM,CAACQ;KAClB,CAAC;EACJ;EAEA;;;EAGAG,qBAAqBA,CAACX,MAMrB;IACC,OAAO1B,sBAAsB,CAAC;MAC5BsC,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;MACzBR,UAAU,EAAEJ,MAAM,CAACI,UAAU;MAC7BE,iBAAiB,EAAEN,MAAM,CAACM,iBAAiB;MAC3CC,OAAO,EAAEP,MAAM,CAACO,OAAO;MACvBC,QAAQ,EAAER,MAAM,CAACQ;KAClB,CAAC;EACJ;EAEA;EAEA;;;EAGAK,aAAaA,CAACC,UAAgD,EAAEd,MAAW;IACzE,QAAQc,UAAU;MAChB,KAAK,QAAQ;QACX,MAAMC,aAAa,GAAG,IAAI,CAAChB,kBAAkB,CAACC,MAAM,CAAC;QACrD,OAAO,IAAI,CAACrB,YAAY,CAACoC,aAAa,CAAC;MAEzC,KAAK,WAAW;QACd,MAAMC,gBAAgB,GAAG,IAAI,CAACP,qBAAqB,CAACT,MAAM,CAAC;QAC3D,OAAO,IAAI,CAACV,eAAe,CAAC0B,gBAAgB,CAAC;MAE/C,KAAK,WAAW;QACd,MAAMC,gBAAgB,GAAG,IAAI,CAACN,qBAAqB,CAACX,MAAM,CAAC;QAC3D,OAAO,IAAI,CAACH,eAAe,CAACoB,gBAAgB,CAAC;MAE/C;QACE,OAAOhD,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAAC,gCAAgC,CAAC,CAAC;;EAE1E;EAEA;;;EAGAuB,WAAWA,CAAClB,MAWX;IACC,MAAMmB,YAAY,GAAG;MACnBlB,iBAAiB,EAAED,MAAM,CAACoB,IAAI;MAC9BlB,eAAe,EAAEF,MAAM,CAACqB,EAAE;MAC1BlB,aAAa,EAAEH,MAAM,CAACG,aAAa;MACnCO,UAAU,EAAEV,MAAM,CAACU,UAAU;MAC7BE,QAAQ,EAAEZ,MAAM,CAACY,QAAQ;MACzBR,UAAU,EAAE;QACVkB,MAAM,EAAEtB,MAAM,CAACsB,MAAM,IAAI,CAAC;QAC1BC,QAAQ,EAAEvB,MAAM,CAACuB,QAAQ,IAAI,CAAC;QAC9BC,OAAO,EAAExB,MAAM,CAACwB,OAAO,IAAI;OAC5B;MACDlB,iBAAiB,EAAEN,MAAM,CAACyB,UAAU,IAAI,KAAK;MAC7ClB,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE;KACX;IAED,OAAO,IAAI,CAACK,aAAa,CAACb,MAAM,CAAC0B,IAAI,EAAEP,YAAY,CAAC;EACtD;EAEA;EAEA;;;EAGAQ,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,CAAC,CAACF,KAAK;EAChB;EAEA;;;EAGAG,mBAAmBA,CAAC3B,UAAmE;IACrF,MAAM4B,KAAK,GAAa,EAAE;IAE1B,IAAI5B,UAAU,CAACkB,MAAM,GAAG,CAAC,EAAE;MACzBU,KAAK,CAACC,IAAI,CAAC,GAAG7B,UAAU,CAACkB,MAAM,UAAUlB,UAAU,CAACkB,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAG9E,IAAIlB,UAAU,CAACmB,QAAQ,IAAInB,UAAU,CAACmB,QAAQ,GAAG,CAAC,EAAE;MAClDS,KAAK,CAACC,IAAI,CAAC,GAAG7B,UAAU,CAACmB,QAAQ,UAAUnB,UAAU,CAACmB,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAGlF,IAAInB,UAAU,CAACoB,OAAO,IAAIpB,UAAU,CAACoB,OAAO,GAAG,CAAC,EAAE;MAChDQ,KAAK,CAACC,IAAI,CAAC,GAAG7B,UAAU,CAACoB,OAAO,QAAQpB,UAAU,CAACoB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE,CAAC;;IAG9E,OAAOQ,KAAK,CAACpC,IAAI,CAAC,IAAI,CAAC;EACzB;EAEA;;;EAGAsC,oBAAoBA,CAAClC,MAAW;IAC9B,MAAMmC,MAAM,GAAa,EAAE;IAE3B,IAAI,CAACnC,MAAM,CAACC,iBAAiB,EAAE;MAC7BkC,MAAM,CAACF,IAAI,CAAC,uBAAuB,CAAC;;IAGtC,IAAI,CAACjC,MAAM,CAACE,eAAe,EAAE;MAC3BiC,MAAM,CAACF,IAAI,CAAC,wBAAwB,CAAC;;IAGvC,IAAI,CAACjC,MAAM,CAACG,aAAa,EAAE;MACzBgC,MAAM,CAACF,IAAI,CAAC,wBAAwB,CAAC;;IAGvC,IAAIjC,MAAM,CAACI,UAAU,EAAEkB,MAAM,GAAG,CAAC,EAAE;MACjCa,MAAM,CAACF,IAAI,CAAC,2BAA2B,CAAC;;IAG1C,OAAOE,MAAM;EACf;EAEA;EAEA;;;;EAIQrD,UAAUA,CAAA;IAChB,MAAM8C,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAEhD,IAAI,CAACF,KAAK,EAAE;MACV,MAAM,IAAIjC,KAAK,CAAC,oCAAoC,CAAC;;IAGvD,OAAO,IAAI3B,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,eAAe,EAAE,UAAU4D,KAAK;KACjC,CAAC;EACJ;EAEA;;;EAGQvC,WAAWA,CAAC+C,cAAsB;IACxC,OAAQ1C,KAAU,IAAuB;MACvCX,OAAO,CAACW,KAAK,CAAC,oBAAoB,CAAC;MACnCX,OAAO,CAACW,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC2C,MAAM,CAAC;MACtCtD,OAAO,CAACW,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC4C,UAAU,CAAC;MAC/CvD,OAAO,CAACW,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC6C,GAAG,CAAC;MAChCxD,OAAO,CAACW,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCX,OAAO,CAACW,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MACzCX,OAAO,CAACW,KAAK,CAAC,kBAAkB,CAAC;MAEjC,IAAI8C,YAAY,GAAGJ,cAAc;MAEjC;MACA,IAAI1C,KAAK,CAACA,KAAK,EAAE+C,OAAO,EAAE;QACxBD,YAAY,GAAG9C,KAAK,CAACA,KAAK,CAAC+C,OAAO;OACnC,MAAM,IAAI/C,KAAK,CAACA,KAAK,EAAEgD,MAAM,EAAEC,QAAQ,EAAElD,MAAM,GAAG,CAAC,EAAE;QACpD;QACA+C,YAAY,GAAG9C,KAAK,CAACA,KAAK,CAACgD,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,CAACF,OAAO;OACtD,MAAM,IAAI/C,KAAK,CAAC2C,MAAM,KAAK,GAAG,EAAE;QAC/BG,YAAY,GAAG,8CAA8C;OAC9D,MAAM,IAAI9C,KAAK,CAAC2C,MAAM,KAAK,GAAG,EAAE;QAC/BG,YAAY,GAAG,oBAAoB;OACpC,MAAM,IAAI9C,KAAK,CAAC2C,MAAM,KAAK,GAAG,EAAE;QAC/BG,YAAY,GAAG,oBAAoB;OACpC,MAAM,IAAI9C,KAAK,CAAC2C,MAAM,KAAK,GAAG,EAAE;QAC/BG,YAAY,GAAG,gEAAgE;QAC/E;QACA,IAAI9C,KAAK,CAACA,KAAK,EAAE;UACfX,OAAO,CAACW,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACA,KAAK,CAAC;;OAE1D,MAAM,IAAIA,KAAK,CAAC+C,OAAO,EAAE;QACxBD,YAAY,GAAG9C,KAAK,CAAC+C,OAAO;;MAG9B,OAAOxE,UAAU,CAAC,MAAM,IAAI0B,KAAK,CAAC6C,YAAY,CAAC,CAAC;IAClD,CAAC;EACH;;;uBAzVWjE,mBAAmB,EAAAqE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBxE,mBAAmB;MAAAyE,OAAA,EAAnBzE,mBAAmB,CAAA0E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}