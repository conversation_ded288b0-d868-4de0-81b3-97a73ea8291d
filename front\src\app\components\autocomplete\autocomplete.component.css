.autocomplete-container {
  position: relative;
  width: 100%;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.autocomplete-input {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 3.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 500;
  background: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.autocomplete-input:focus {
  border-color: #2c5aa0;
  box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

.autocomplete-input:disabled {
  background-color: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.input-icon {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #718096;
  z-index: 2;
}

.input-icon svg {
  width: 100%;
  height: 100%;
}

.clear-button {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #a0aec0;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 2;
}

.clear-button:hover {
  background: #f7fafc;
  color: #718096;
}

.clear-button svg {
  width: 16px;
  height: 16px;
}

.loading-indicator {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #2c5aa0;
  z-index: 2;
}

.spinner {
  width: 100%;
  height: 100%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: white;
  border-radius: 16px;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  max-height: 300px;
  overflow-y: auto;
}

.suggestions-list {
  padding: 0.5rem 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f7fafc;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-left: 4px solid #2c5aa0;
  padding-left: calc(1.5rem - 4px);
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.suggestion-main {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.suggestion-code {
  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 8px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 40px;
  text-align: center;
}

.suggestion-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.95rem;
}

.suggestion-display {
  font-size: 0.85rem;
  color: #718096;
  font-weight: 400;
}

.suggestion-icon {
  width: 20px;
  height: 20px;
  color: #cbd5e0;
  transition: all 0.2s ease;
}

.suggestion-item:hover .suggestion-icon,
.suggestion-item.selected .suggestion-icon {
  color: #2c5aa0;
  transform: translateX(2px);
}

.suggestion-icon svg {
  width: 100%;
  height: 100%;
}

.no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 2rem 1.5rem;
  color: #a0aec0;
  font-size: 0.9rem;
  font-weight: 500;
}

.no-results-icon {
  width: 24px;
  height: 24px;
  opacity: 0.6;
}

.no-results-icon svg {
  width: 100%;
  height: 100%;
}

/* Mise en évidence des correspondances */
:global(.autocomplete-container mark) {
  background: linear-gradient(135deg, #fef5e7 0%, #fed7aa 100%);
  color: #c05621;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-weight: 600;
}

/* Scrollbar personnalisée */
.suggestions-container::-webkit-scrollbar {
  width: 6px;
}

.suggestions-container::-webkit-scrollbar-track {
  background: #f7fafc;
  border-radius: 3px;
}

.suggestions-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.suggestions-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .autocomplete-input {
    padding: 1rem 1rem 1rem 3rem;
    font-size: 0.95rem;
  }
  
  .input-icon {
    left: 1rem;
    width: 18px;
    height: 18px;
  }
  
  .suggestion-item {
    padding: 0.875rem 1rem;
  }
  
  .suggestion-main {
    gap: 0.5rem;
  }
  
  .suggestion-code {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    min-width: 35px;
  }
  
  .suggestion-name {
    font-size: 0.9rem;
  }
  
  .suggestion-display {
    font-size: 0.8rem;
  }
}

/* Animation d'apparition */
.suggestions-container {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* États de focus et d'erreur */
.autocomplete-input.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.1);
}

.autocomplete-input.success {
  border-color: #38a169;
  box-shadow: 0 0 0 4px rgba(56, 161, 105, 0.1);
}
