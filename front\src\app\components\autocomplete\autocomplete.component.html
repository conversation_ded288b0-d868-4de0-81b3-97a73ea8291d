<div class="autocomplete-container">
  <div class="input-wrapper">
    <!-- I<PERSON><PERSON> -->
    <div class="input-icon" *ngIf="icon">
      <svg viewBox="0 0 24 24" fill="currentColor" [ngSwitch]="icon">
        <!-- I<PERSON><PERSON> d'aéroport/localisation -->
        <path *ngSwitchCase="'location'" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>

        <!-- I<PERSON><PERSON> d'avion -->
        <path *ngSwitchCase="'plane'" d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>

        <!-- I<PERSON><PERSON> de recherche par défaut -->
        <path *ngSwitchDefault d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
      </svg>
    </div>

    <!-- Champ de saisie -->
    <input 
      #inputElement
      type="text"
      [placeholder]="placeholder"
      [value]="value"
      [disabled]="disabled"
      [required]="required"
      (input)="onInput($event)"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (keydown)="onKeyDown($event)"
      class="autocomplete-input"
      autocomplete="off"
      spellcheck="false">

    <!-- Bouton de suppression -->
    <button 
      type="button"
      class="clear-button"
      *ngIf="value && !disabled"
      (click)="clearInput()"
      tabindex="-1">
      <svg viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
      </svg>
    </button>

    <!-- Indicateur de chargement -->
    <div class="loading-indicator" *ngIf="loading">
      <svg class="spinner" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
      </svg>
    </div>
  </div>

  <!-- Liste des suggestions -->
  <div class="suggestions-container" *ngIf="showSuggestions">
    <div class="suggestions-list">
      <div 
        *ngFor="let suggestion of suggestions; let i = index"
        class="suggestion-item"
        [class.selected]="i === selectedIndex"
        (click)="selectItem(suggestion)"
        (mouseenter)="selectedIndex = i">
        
        <div class="suggestion-content">
          <div class="suggestion-main">
            <span class="suggestion-code">{{ suggestion.code }}</span>
            <span class="suggestion-name" [innerHTML]="highlightMatch(suggestion.name, value)"></span>
          </div>
          <div class="suggestion-display" [innerHTML]="highlightMatch(suggestion.displayName, value)"></div>
        </div>
        
        <div class="suggestion-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/>
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Message si aucun résultat -->
    <div class="no-results" *ngIf="suggestions.length === 0 && !loading && value.length >= 2">
      <div class="no-results-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </div>
      <span>Aucun résultat trouvé</span>
    </div>
  </div>
</div>
