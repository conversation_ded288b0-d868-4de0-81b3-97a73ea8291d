import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AutocompleteComponent, AutocompleteItem } from './autocomplete.component';

describe('AutocompleteComponent', () => {
  let component: AutocompleteComponent;
  let fixture: ComponentFixture<AutocompleteComponent>;

  const mockItems: AutocompleteItem[] = [
    { code: 'IST', name: 'Istanbul Airport', displayName: 'IST - Istanbul Airport' },
    { code: 'TUN', name: 'Tunis-Carthage Airport', displayName: 'TUN - Tunis Carthage' },
    { code: 'CDG', name: 'Charles <PERSON> Airport', displayName: 'CDG - Paris Charles de Gaulle' }
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AutocompleteComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AutocompleteComponent);
    component = fixture.componentInstance;
    
    // Mock search function
    component.searchFunction = (query: string) => {
      const filtered = mockItems.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.code.toLowerCase().includes(query.toLowerCase())
      );
      return of(filtered);
    };
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit valueChange when input changes', () => {
    spyOn(component.valueChange, 'emit');
    
    const inputElement = fixture.nativeElement.querySelector('.autocomplete-input');
    inputElement.value = 'test';
    inputElement.dispatchEvent(new Event('input'));
    
    expect(component.valueChange.emit).toHaveBeenCalledWith('test');
  });

  it('should show suggestions when typing', (done) => {
    component.searchTerm$.next('ist');
    
    setTimeout(() => {
      expect(component.suggestions.length).toBeGreaterThan(0);
      expect(component.showSuggestions).toBeTruthy();
      done();
    }, 350);
  });

  it('should select item when clicked', () => {
    spyOn(component.itemSelected, 'emit');
    spyOn(component.valueChange, 'emit');
    
    const testItem = mockItems[0];
    component.selectItem(testItem);
    
    expect(component.itemSelected.emit).toHaveBeenCalledWith(testItem);
    expect(component.valueChange.emit).toHaveBeenCalledWith(testItem.displayName);
    expect(component.showSuggestions).toBeFalsy();
  });

  it('should navigate suggestions with arrow keys', () => {
    component.suggestions = mockItems;
    component.showSuggestions = true;
    component.selectedIndex = -1;
    
    // Arrow down
    const downEvent = new KeyboardEvent('keydown', { key: 'ArrowDown' });
    component.onKeyDown(downEvent);
    expect(component.selectedIndex).toBe(0);
    
    // Arrow down again
    component.onKeyDown(downEvent);
    expect(component.selectedIndex).toBe(1);
    
    // Arrow up
    const upEvent = new KeyboardEvent('keydown', { key: 'ArrowUp' });
    component.onKeyDown(upEvent);
    expect(component.selectedIndex).toBe(0);
  });

  it('should select item with Enter key', () => {
    spyOn(component, 'selectItem');
    
    component.suggestions = mockItems;
    component.showSuggestions = true;
    component.selectedIndex = 1;
    
    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    component.onKeyDown(enterEvent);
    
    expect(component.selectItem).toHaveBeenCalledWith(mockItems[1]);
  });

  it('should clear input when clear button is clicked', () => {
    spyOn(component.valueChange, 'emit');
    
    component.value = 'test';
    component.clearInput();
    
    expect(component.value).toBe('');
    expect(component.valueChange.emit).toHaveBeenCalledWith('');
    expect(component.showSuggestions).toBeFalsy();
  });

  it('should highlight matching text', () => {
    const result = component.highlightMatch('Istanbul Airport', 'ist');
    expect(result).toContain('<mark>ist</mark>');
  });

  it('should hide suggestions on blur', (done) => {
    component.showSuggestions = true;
    component.onBlur();
    
    setTimeout(() => {
      expect(component.showSuggestions).toBeFalsy();
      done();
    }, 250);
  });
});
