import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild } from '@angular/core';
import { Subject, Observable, of } from 'rxjs';
import { debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';

export interface AutocompleteItem {
  code: string;
  name: string;
  displayName: string;
}

@Component({
  selector: 'app-autocomplete',
  templateUrl: './autocomplete.component.html',
  styleUrls: ['./autocomplete.component.css']
})
export class AutocompleteComponent implements OnInit, OnDestroy {
  @Input() placeholder: string = '';
  @Input() value: string = '';
  @Input() searchFunction!: (query: string) => Observable<AutocompleteItem[]>;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() icon: string = '';
  
  @Output() valueChange = new EventEmitter<string>();
  @Output() itemSelected = new EventEmitter<AutocompleteItem>();
  
  @ViewChild('inputElement', { static: true }) inputElement!: ElementRef<HTMLInputElement>;

  searchTerm$ = new Subject<string>();
  suggestions: AutocompleteItem[] = [];
  showSuggestions = false;
  selectedIndex = -1;
  loading = false;
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupSearch(): void {
    this.searchTerm$.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(term => {
        if (!term || term.length < 2) {
          return of([]);
        }
        this.loading = true;
        return this.searchFunction(term);
      }),
      takeUntil(this.destroy$)
    ).subscribe({
      next: (results) => {
        this.suggestions = results;
        this.showSuggestions = results.length > 0;
        this.selectedIndex = -1;
        this.loading = false;
      },
      error: () => {
        this.loading = false;
        this.suggestions = [];
        this.showSuggestions = false;
      }
    });
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;
    this.value = value;
    this.valueChange.emit(value);
    this.searchTerm$.next(value);
  }

  onFocus(): void {
    if (this.suggestions.length > 0) {
      this.showSuggestions = true;
    }
  }

  onBlur(): void {
    // Délai pour permettre le clic sur une suggestion
    setTimeout(() => {
      this.showSuggestions = false;
      this.selectedIndex = -1;
    }, 200);
  }

  onKeyDown(event: KeyboardEvent): void {
    if (!this.showSuggestions) return;

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.selectedIndex = Math.min(this.selectedIndex + 1, this.suggestions.length - 1);
        break;
      
      case 'ArrowUp':
        event.preventDefault();
        this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
        break;
      
      case 'Enter':
        event.preventDefault();
        if (this.selectedIndex >= 0 && this.selectedIndex < this.suggestions.length) {
          this.selectItem(this.suggestions[this.selectedIndex]);
        }
        break;
      
      case 'Escape':
        this.showSuggestions = false;
        this.selectedIndex = -1;
        break;
    }
  }

  selectItem(item: AutocompleteItem): void {
    this.value = item.displayName;
    this.valueChange.emit(this.value);
    this.itemSelected.emit(item);
    this.showSuggestions = false;
    this.selectedIndex = -1;
    this.inputElement.nativeElement.value = item.displayName;
  }

  clearInput(): void {
    this.value = '';
    this.valueChange.emit('');
    this.suggestions = [];
    this.showSuggestions = false;
    this.selectedIndex = -1;
    this.inputElement.nativeElement.value = '';
    this.inputElement.nativeElement.focus();
  }

  highlightMatch(text: string, query: string): string {
    if (!query) return text;
    
    const regex = new RegExp(`(${query})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }
}
