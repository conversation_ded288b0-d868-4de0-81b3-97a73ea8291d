/* Variables CSS pour la cohérence */
:root {
  --primary-blue: #4a90e2;
  --secondary-blue: #7bb3f0;
  --light-blue: #e8f4fd;
  --dark-blue: #2c5aa0;
  --text-dark: #333333;
  --text-light: #666666;
  --text-muted: #999999;
  --background-light: #f8f9fa;
  --white: #ffffff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Container principal */
.dashboard-container {
  min-height: 100vh;
  background: var(--background-light);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.dashboard-header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-light);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: var(--primary-blue);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  letter-spacing: -0.5px;
}

.demo-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.demo-id, .demo-details {
  font-size: 0.875rem;
  color: var(--text-light);
}

/* Navigation */
.nav-links {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: var(--text-dark);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.nav-link:hover {
  background: var(--light-blue);
  color: var(--primary-blue);
}

.nav-link.active {
  background: var(--primary-blue);
  color: var(--white);
}

.nav-link svg {
  width: 16px;
  height: 16px;
}

/* Main content */
.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 2rem;
}

/* Sidebar */
.sidebar {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 1.5rem;
  height: fit-content;
}

.sidebar-section {
  margin-bottom: 2rem;
}

.sidebar-section:last-child {
  margin-bottom: 0;
}

.sidebar-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-item {
  margin-bottom: 0.5rem;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  text-decoration: none;
  color: var(--text-light);
  font-size: 0.875rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.sidebar-link:hover {
  background: var(--light-blue);
  color: var(--primary-blue);
}

.sidebar-link svg {
  width: 16px;
  height: 16px;
}

/* Main content area */
.main-content {
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  padding: 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

/* Service cards */
.service-card {
  padding: 2rem;
  border-radius: var(--border-radius);
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  color: var(--white);
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  opacity: 0;
  transition: var(--transition);
}

.service-card:hover::before {
  opacity: 1;
}

.service-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-medium);
}

.card-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-icon svg {
  width: 32px;
  height: 32px;
  color: var(--white);
}

.service-card h3 {
  margin: 1rem 0 0 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--white);
}

.card-description {
  margin: 0.5rem 0 0 0;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.9;
}

/* User info */
.user-info {
  background: var(--light-blue);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
}

.user-name {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.user-details {
  font-size: 0.875rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .nav-links {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .dashboard-main {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
}
