.flight-results-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header avec résumé de recherche */
.results-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.search-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.route-info {
  flex: 1;
  min-width: 300px;
}

.route-main {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.airport-code {
  font-size: 1.5rem;
  font-weight: 700;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.route-arrow {
  display: flex;
  gap: 0.5rem;
}

.route-arrow svg {
  width: 24px;
  height: 24px;
  opacity: 0.8;
}

.route-details {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  opacity: 0.9;
}

.results-count {
  text-align: center;
}

.count {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.search-time {
  font-size: 0.8rem;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.btn-secondary,
.btn-primary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  font-size: 0.9rem;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-primary {
  background: rgba(255, 255, 255, 0.9);
  color: #2c5aa0;
}

.btn-primary:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-secondary svg,
.btn-primary svg {
  width: 16px;
  height: 16px;
}

/* Contenu principal */
.results-content {
  display: flex;
  max-width: 1400px;
  margin: 0 auto;
  gap: 2rem;
  padding: 2rem;
}

/* Sidebar avec filtres */
.filters-sidebar {
  width: 300px;
  background: white;
  border-radius: 20px;
  padding: 2rem;
  height: fit-content;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filters-sidebar.hidden {
  transform: translateX(-100%);
  opacity: 0;
  pointer-events: none;
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f7fafc;
}

.filters-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 700;
}

.btn-toggle {
  width: 32px;
  height: 32px;
  border: none;
  background: #f7fafc;
  border-radius: 8px;
  cursor: pointer;
  color: #718096;
  transition: all 0.3s ease;
}

.btn-toggle:hover {
  background: #edf2f7;
  color: #2d3748;
}

.filter-section {
  margin-bottom: 2rem;
}

.filter-section h4 {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 600;
}

.price-filter {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-slider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e2e8f0;
  outline: none;
  cursor: pointer;
}

.price-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #2c5aa0;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(44, 90, 160, 0.3);
}

.price-value {
  font-weight: 600;
  color: #2c5aa0;
  text-align: center;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 8px;
}

.airline-filters,
.stops-filters {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.checkbox-label:hover {
  background: #f7fafc;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: #2c5aa0;
  cursor: pointer;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #e2e8f0;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.airline-name {
  font-weight: 500;
  color: #4a5568;
}

.btn-clear {
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: #718096;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-clear:hover {
  border-color: #dc3545;
  color: #dc3545;
}

/* Zone des résultats */
.results-main {
  flex: 1;
}

.results-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.btn-filter-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-filter-toggle:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.btn-filter-toggle svg {
  width: 16px;
  height: 16px;
}

.view-toggle {
  display: flex;
  background: #f7fafc;
  border-radius: 12px;
  padding: 4px;
}

.view-btn {
  padding: 0.5rem;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  color: #718096;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: white;
  color: #2c5aa0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-btn svg {
  width: 18px;
  height: 18px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sort-controls label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

.sort-controls select {
  padding: 0.5rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-weight: 500;
  cursor: pointer;
}

.sort-order-btn {
  padding: 0.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  color: #718096;
  transition: all 0.3s ease;
}

.sort-order-btn:hover {
  background: #f7fafc;
  color: #2c5aa0;
}

.sort-order-btn svg {
  width: 16px;
  height: 16px;
}

.results-info {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.9rem;
}

/* États de chargement et d'erreur */
.loading-state,
.error-state,
.no-results {
  text-align: center;
  padding: 4rem 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #2c5aa0;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.no-results-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem;
  color: #e53e3e;
}

.no-results-icon {
  color: #718096;
}

.error-state h3,
.no-results h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 700;
}

.error-state p,
.no-results p {
  margin: 0 0 2rem 0;
  color: #718096;
  font-size: 1rem;
}

/* Liste des vols */
.flights-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.flights-container.grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 1.5rem;
}

.flight-card {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flight-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
  border-color: #2c5aa0;
}

.flight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f7fafc;
}

.airline-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.airline-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: contain;
}

.airline-placeholder {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.8rem;
}

.airline-name {
  font-weight: 600;
  color: #2d3748;
}

.flight-badges {
  display: flex;
  gap: 0.5rem;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge.direct {
  background: #c6f6d5;
  color: #22543d;
}

.badge.refundable {
  background: #bee3f8;
  color: #2a4365;
}

.badge.class {
  background: #e9d8fd;
  color: #553c9a;
}

.flight-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.flight-route {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.departure,
.arrival {
  text-align: center;
  min-width: 100px;
}

.time {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.airport {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.city {
  font-size: 0.8rem;
  color: #718096;
}

.flight-info {
  flex: 1;
  text-align: center;
}

.duration {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.route-line {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.line {
  height: 2px;
  background: linear-gradient(90deg, #e2e8f0 0%, #2c5aa0 50%, #e2e8f0 100%);
  flex: 1;
  position: relative;
}

.stops {
  position: absolute;
  top: -1.5rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #718096;
  background: white;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.plane-icon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2c5aa0;
  border: 2px solid #e2e8f0;
}

.plane-icon svg {
  width: 14px;
  height: 14px;
}

.flight-price {
  text-align: center;
  min-width: 120px;
}

.price-amount {
  font-size: 1.8rem;
  font-weight: 700;
  color: #2c5aa0;
  margin-bottom: 0.25rem;
}

.price-details {
  font-size: 0.8rem;
  color: #718096;
  margin-bottom: 1rem;
}

.btn-select {
  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
}

.btn-select:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(44, 90, 160, 0.3);
}

.flight-baggage {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #f7fafc;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.baggage-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: #718096;
}

.baggage-item svg {
  width: 16px;
  height: 16px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.page-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #2c5aa0;
  color: #2c5aa0;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn svg {
  width: 16px;
  height: 16px;
}

.page-numbers {
  display: flex;
  gap: 0.5rem;
}

.page-number {
  width: 40px;
  height: 40px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-number.active {
  background: #2c5aa0;
  color: white;
  border-color: #2c5aa0;
}

.page-number:hover:not(.active) {
  background: #f7fafc;
  border-color: #2c5aa0;
  color: #2c5aa0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .results-content {
    flex-direction: column;
  }
  
  .filters-sidebar {
    width: 100%;
    order: 2;
  }
  
  .results-main {
    order: 1;
  }
}

@media (max-width: 768px) {
  .search-summary {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .header-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .btn-secondary,
  .btn-primary {
    justify-content: center;
  }
  
  .results-toolbar {
    flex-direction: column;
    gap: 1rem;
  }
  
  .toolbar-left,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .flight-details {
    flex-direction: column;
    gap: 1rem;
  }
  
  .flight-route {
    flex-direction: column;
    gap: 1rem;
  }
  
  .flights-container.grid-view {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
  
  .page-numbers {
    order: -1;
    width: 100%;
    justify-content: center;
  }
}
