<div class="flight-results-container">
  <!-- Header avec r<PERSON> de recherche -->
  <div class="results-header">
    <div class="header-content">
      <div class="search-summary" *ngIf="searchSummary">
        <div class="route-info">
          <div class="route-main">
            <span class="airport-code">{{ searchSummary?.from }}</span>
            <div class="route-arrow">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
              </svg>
              <svg *ngIf="searchSummary?.searchType === 'roundtrip'" viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" transform="rotate(180 12 12)"/>
              </svg>
            </div>
            <span class="airport-code">{{ searchSummary?.to }}</span>
          </div>
          <div class="route-details">
            <span class="date">{{ searchSummary?.departureDate }}</span>
            <span *ngIf="searchSummary?.returnDate" class="date"> - {{ searchSummary?.returnDate }}</span>
            <span class="passengers">{{ getPassengerText() }}</span>
          </div>
        </div>

        <div class="results-count">
          <span class="count">{{ searchSummary?.totalResults }} vols trouvés</span>
          <span class="search-time">Recherche effectuée à {{ searchSummary?.searchTime }}</span>
        </div>
        
        <div class="header-actions">
          <button class="btn-secondary" (click)="modifySearch()">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            Modifier la recherche
          </button>
          <button class="btn-primary" (click)="backToSearch()">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
            </svg>
            Nouvelle recherche
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="results-content">
    <!-- Sidebar avec filtres -->
    <aside class="filters-sidebar" [class.hidden]="!showFilters">
      <div class="filters-header">
        <h3>Filtres</h3>
        <button class="btn-toggle" (click)="showFilters = !showFilters">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
      
      <div class="filter-section">
        <h4>Prix maximum</h4>
        <div class="price-filter">
          <input type="range"
                 [(ngModel)]="filters.maxPrice"
                 [max]="getMaxPrice()"
                 (input)="applyFilters()"
                 class="price-slider">
          <span class="price-value">{{ filters.maxPrice }}€</span>
        </div>
      </div>
      
      <div class="filter-section">
        <h4>Compagnies aériennes</h4>
        <div class="airline-filters">
          <label *ngFor="let airline of getUniqueAirlines()" class="checkbox-label">
            <input type="checkbox" 
                   [value]="airline.code"
                   (change)="toggleAirlineFilter(airline.code)"
                   [checked]="filters.airlines.includes(airline.code)">
            <span class="checkmark"></span>
            <span class="airline-name">{{ airline.name }}</span>
          </label>
        </div>
      </div>
      
      <div class="filter-section">
        <h4>Nombre d'escales</h4>
        <div class="stops-filters">
          <label class="checkbox-label">
            <input type="checkbox" 
                   value="0"
                   (change)="toggleStopsFilter(0)"
                   [checked]="filters.stops.includes(0)">
            <span class="checkmark"></span>
            <span>Vol direct</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" 
                   value="1"
                   (change)="toggleStopsFilter(1)"
                   [checked]="filters.stops.includes(1)">
            <span class="checkmark"></span>
            <span>1 escale</span>
          </label>
          <label class="checkbox-label">
            <input type="checkbox" 
                   value="2"
                   (change)="toggleStopsFilter(2)"
                   [checked]="filters.stops.includes(2)">
            <span class="checkmark"></span>
            <span>2+ escales</span>
          </label>
        </div>
      </div>
      
      <div class="filter-actions">
        <button class="btn-clear" (click)="clearFilters()">Effacer les filtres</button>
      </div>
    </aside>

    <!-- Zone des résultats -->
    <main class="results-main">
      <!-- Barre d'outils -->
      <div class="results-toolbar">
        <div class="toolbar-left">
          <button class="btn-filter-toggle" (click)="showFilters = !showFilters">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M3 18h6v-2H3v2zM3 6v2h10V6H3zm0 7h8v-2H3v2zm16-1v8h2v-8h-2zm-3-3h2V6h-2v3zm3-3v3h2V6h-2z"/>
            </svg>
            Filtres
          </button>
          
          <div class="view-toggle">
            <button class="view-btn" 
                    [class.active]="viewMode === 'list'"
                    (click)="viewMode = 'list'">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
              </svg>
            </button>
            <button class="view-btn" 
                    [class.active]="viewMode === 'grid'"
                    (click)="viewMode = 'grid'">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M4 11h5V5H4v6zm0 7h5v-6H4v6zm6 0h5v-6h-5v6zm6 0h5v-6h-5v6zm-6-7h5V5h-5v6zm6-6v6h5V5h-5z"/>
              </svg>
            </button>
          </div>
        </div>
        
        <div class="toolbar-right">
          <div class="sort-controls">
            <label>Trier par:</label>
            <select [(ngModel)]="sortBy" (change)="sortFlights(); applyFilters()">
              <option value="price">Prix</option>
              <option value="duration">Durée</option>
              <option value="departure">Heure de départ</option>
            </select>
            <button class="sort-order-btn" (click)="toggleSortOrder()">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path *ngIf="sortOrder === 'asc'" d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/>
                <path *ngIf="sortOrder === 'desc'" d="M3 6h18v2H3V6zm0 7h12v2H3v-2zm0 5h6v2H3v-2z"/>
              </svg>
            </button>
          </div>
          
          <span class="results-info">
            {{ filteredFlights.length }} résultats
          </span>
        </div>
      </div>

      <!-- État de chargement -->
      <div *ngIf="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Recherche de vols en cours...</p>
      </div>

      <!-- État d'erreur -->
      <div *ngIf="error" class="error-state">
        <div class="error-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <h3>Erreur lors de la recherche</h3>
        <p>{{ error }}</p>
        <button class="btn-primary" (click)="backToSearch()">Retour à la recherche</button>
      </div>

      <!-- Liste des vols -->
      <div *ngIf="!loading && !error" class="flights-container" [class.grid-view]="viewMode === 'grid'">
        <div *ngIf="filteredFlights.length === 0" class="no-results">
          <div class="no-results-icon">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
            </svg>
          </div>
          <h3>Aucun vol trouvé</h3>
          <p>Essayez de modifier vos critères de recherche ou vos filtres.</p>
          <button class="btn-primary" (click)="clearFilters()">Effacer les filtres</button>
        </div>

        <div *ngFor="let flight of getPaginatedFlights(); trackBy: trackByFlightId" 
             class="flight-card"
             (click)="selectFlight(flight)">
          
          <div class="flight-header">
            <div class="airline-info">
              <img *ngIf="flight.airline.logo" 
                   [src]="flight.airline.logo" 
                   [alt]="flight.airline.name"
                   class="airline-logo">
              <div *ngIf="!flight.airline.logo" class="airline-placeholder">
                {{ flight.airline.code }}
              </div>
              <span class="airline-name">{{ flight.airline.name }}</span>
            </div>
            
            <div class="flight-badges">
              <span *ngIf="flight.stops === 0" class="badge direct">Direct</span>
              <span *ngIf="flight.refundable" class="badge refundable">Remboursable</span>
              <span class="badge class">{{ flight.class }}</span>
            </div>
          </div>

          <div class="flight-details">
            <div class="flight-route">
              <div class="departure">
                <div class="time">{{ flight.departure.time }}</div>
                <div class="airport">{{ flight.departure.airport }}</div>
                <div class="city">{{ flight.departure.city }}</div>
              </div>
              
              <div class="flight-info">
                <div class="duration">{{ flight.duration }}</div>
                <div class="route-line">
                  <div class="line"></div>
                  <div *ngIf="flight.stops > 0" class="stops">
                    {{ flight.stops }} escale{{ flight.stops > 1 ? 's' : '' }}
                  </div>
                  <div class="plane-icon">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                      <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
                    </svg>
                  </div>
                </div>
              </div>
              
              <div class="arrival">
                <div class="time">{{ flight.arrival.time }}</div>
                <div class="airport">{{ flight.arrival.airport }}</div>
                <div class="city">{{ flight.arrival.city }}</div>
              </div>
            </div>
            
            <div class="flight-price">
              <div class="price-amount">{{ flight.price.formatted }}</div>
              <div class="price-details">par personne</div>
              <button class="btn-select">Sélectionner</button>
            </div>
          </div>

          <div *ngIf="flight.baggage" class="flight-baggage">
            <div class="baggage-item">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM9.5 18H8V9h1.5v9zm3.25 0h-1.5V9h1.5v9zm3.25 0H14V9h1.5v9zm-6-12V4h3v2h-3z"/>
              </svg>
              <span>Bagage cabine: {{ flight.baggage.carryOn }}</span>
            </div>
            <div class="baggage-item">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 6h-2V3c0-.55-.45-1-1-1h-4c-.55 0-1 .45-1 1v3H7c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2z"/>
              </svg>
              <span>Bagage soute: {{ flight.baggage.checked }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="totalPages > 1" class="pagination">
        <button class="page-btn" 
                [disabled]="currentPage === 1"
                (click)="changePage(currentPage - 1)">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
          Précédent
        </button>
        
        <div class="page-numbers">
          <button *ngFor="let page of getPageNumbers()" 
                  class="page-number"
                  [class.active]="page === currentPage"
                  (click)="changePage(page)">
            {{ page }}
          </button>
        </div>
        
        <button class="page-btn" 
                [disabled]="currentPage === totalPages"
                (click)="changePage(currentPage + 1)">
          Suivant
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
          </svg>
        </button>
      </div>
    </main>
  </div>
</div>
