import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { of } from 'rxjs';
import { FlightResultsComponent } from './flight-results.component';
import { FlightSearchService } from '../../services/flight-search.service';

describe('FlightResultsComponent', () => {
  let component: FlightResultsComponent;
  let fixture: ComponentFixture<FlightResultsComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;
  let mockFlightSearchService: jasmine.SpyObj<FlightSearchService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const flightSearchServiceSpy = jasmine.createSpyObj('FlightSearchService', ['search']);
    
    mockActivatedRoute = {
      queryParams: of({
        searchData: encodeURIComponent(JSON.stringify({
          type: 'oneway',
          params: {
            departureLocation: 'IST',
            arrivalLocation: 'TUN',
            departureDate: '2025-06-17',
            passengers: { adults: 1, children: 0, infants: 0 }
          }
        }))
      })
    };

    await TestBed.configureTestingModule({
      declarations: [FlightResultsComponent],
      imports: [FormsModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: FlightSearchService, useValue: flightSearchServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(FlightResultsComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockFlightSearchService = TestBed.inject(FlightSearchService) as jasmine.SpyObj<FlightSearchService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.loading).toBeFalsy();
    expect(component.error).toBeNull();
    expect(component.flights).toEqual([]);
    expect(component.sortBy).toBe('price');
    expect(component.sortOrder).toBe('asc');
    expect(component.viewMode).toBe('list');
    expect(component.currentPage).toBe(1);
    expect(component.itemsPerPage).toBe(10);
  });

  it('should generate passenger text correctly', () => {
    component.searchSummary = {
      searchType: 'oneway',
      from: 'IST',
      to: 'TUN',
      departureDate: '2025-06-17',
      passengers: { adults: 2, children: 1, infants: 0 },
      totalResults: 5,
      searchTime: '10:30:00'
    };

    expect(component.getPassengerText()).toBe('2 adultes, 1 enfant');
  });

  it('should get unique airlines', () => {
    component.flights = [
      {
        id: '1',
        airline: { code: 'TK', name: 'Turkish Airlines' },
        departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },
        arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },
        duration: '3h 15m',
        stops: 0,
        price: { amount: 450, currency: 'EUR', formatted: '450€' },
        class: 'Economy',
        refundable: true
      },
      {
        id: '2',
        airline: { code: 'TU', name: 'Tunisair' },
        departure: { airport: 'IST', city: 'Istanbul', time: '14:20', date: '2025-06-17' },
        arrival: { airport: 'TUN', city: 'Tunis', time: '17:35', date: '2025-06-17' },
        duration: '3h 15m',
        stops: 0,
        price: { amount: 380, currency: 'EUR', formatted: '380€' },
        class: 'Economy',
        refundable: false
      }
    ];

    const airlines = component.getUniqueAirlines();
    expect(airlines.length).toBe(2);
    expect(airlines).toContain({ code: 'TK', name: 'Turkish Airlines' });
    expect(airlines).toContain({ code: 'TU', name: 'Tunisair' });
  });

  it('should toggle airline filter', () => {
    component.filters.airlines = [];
    
    component.toggleAirlineFilter('TK');
    expect(component.filters.airlines).toContain('TK');
    
    component.toggleAirlineFilter('TK');
    expect(component.filters.airlines).not.toContain('TK');
  });

  it('should toggle stops filter', () => {
    component.filters.stops = [];
    
    component.toggleStopsFilter(0);
    expect(component.filters.stops).toContain(0);
    
    component.toggleStopsFilter(0);
    expect(component.filters.stops).not.toContain(0);
  });

  it('should clear filters', () => {
    component.flights = [
      {
        id: '1',
        airline: { code: 'TK', name: 'Turkish Airlines' },
        departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },
        arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },
        duration: '3h 15m',
        stops: 0,
        price: { amount: 450, currency: 'EUR', formatted: '450€' },
        class: 'Economy',
        refundable: true
      }
    ];
    
    component.filters.airlines = ['TK'];
    component.filters.stops = [0];
    
    component.clearFilters();
    
    expect(component.filters.airlines).toEqual([]);
    expect(component.filters.stops).toEqual([]);
    expect(component.filters.maxPrice).toBe(450);
  });

  it('should toggle sort order', () => {
    expect(component.sortOrder).toBe('asc');
    
    component.toggleSortOrder();
    expect(component.sortOrder).toBe('desc');
    
    component.toggleSortOrder();
    expect(component.sortOrder).toBe('asc');
  });

  it('should change page correctly', () => {
    component.totalPages = 5;
    
    component.changePage(3);
    expect(component.currentPage).toBe(3);
    
    // Should not change to invalid page
    component.changePage(10);
    expect(component.currentPage).toBe(3);
    
    component.changePage(0);
    expect(component.currentPage).toBe(3);
  });

  it('should get page numbers correctly', () => {
    component.totalPages = 10;
    component.currentPage = 5;
    
    const pageNumbers = component.getPageNumbers();
    expect(pageNumbers).toEqual([3, 4, 5, 6, 7]);
  });

  it('should track flights by id', () => {
    const flight = {
      id: 'test-flight-1',
      airline: { code: 'TK', name: 'Turkish Airlines' },
      departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },
      arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },
      duration: '3h 15m',
      stops: 0,
      price: { amount: 450, currency: 'EUR', formatted: '450€' },
      class: 'Economy',
      refundable: true
    };
    
    expect(component.trackByFlightId(0, flight)).toBe('test-flight-1');
  });

  it('should navigate back to search', () => {
    component.backToSearch();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/search-flight']);
  });

  it('should navigate to modify search', () => {
    component.searchSummary = {
      searchType: 'oneway',
      from: 'IST',
      to: 'TUN',
      departureDate: '2025-06-17',
      passengers: { adults: 1, children: 0, infants: 0 },
      totalResults: 5,
      searchTime: '10:30:00'
    };
    
    component.modifySearch();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/search-flight'], jasmine.any(Object));
  });
});
