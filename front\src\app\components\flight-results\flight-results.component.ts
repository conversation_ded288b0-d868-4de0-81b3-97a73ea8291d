import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { FlightSearchService } from '../../services/flight-search.service';
import { OneWayResponse } from '../../models/oneway-response.interface';
import { RoundTripResponse } from '../../models/roundtrip-response.interface';
import { MulticityResponse } from '../../models/multicity-response.interface';

export interface FlightResult {
  id: string;
  airline: {
    code: string;
    name: string;
    logo?: string;
  };
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  duration: string;
  stops: number;
  price: {
    amount: number;
    currency: string;
    formatted: string;
  };
  class: string;
  refundable: boolean;
  baggage?: {
    carryOn: string;
    checked: string;
  };
}

export interface SearchSummary {
  searchType: 'oneway' | 'roundtrip' | 'multicity';
  from: string;
  to: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  totalResults: number;
  searchTime: string;
}

@Component({
  selector: 'app-flight-results',
  templateUrl: './flight-results.component.html',
  styleUrls: ['./flight-results.component.css']
})
export class FlightResultsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // État du composant
  loading = false;
  error: string | null = null;
  
  // Données de recherche
  searchSummary: SearchSummary | null = null;
  flights: FlightResult[] = [];
  filteredFlights: FlightResult[] = [];
  
  // Filtres et tri
  filters = {
    maxPrice: 0,
    airlines: [] as string[],
    stops: [] as number[],
    departureTime: { min: 0, max: 24 },
    duration: { min: 0, max: 24 }
  };
  
  sortBy = 'price'; // price, duration, departure
  sortOrder = 'asc'; // asc, desc
  
  // Options d'affichage
  viewMode = 'list'; // list, grid
  showFilters = true;
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  constructor(
    private flightSearchService: FlightSearchService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadSearchResults();
    this.initializeFilters();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Charge les résultats de recherche depuis le service
   */
  private loadSearchResults(): void {
    // Récupérer les paramètres de recherche depuis l'URL ou le service
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['searchData']) {
        try {
          const searchData = JSON.parse(decodeURIComponent(params['searchData']));
          this.performSearch(searchData);
        } catch (error) {
          console.error('Erreur lors du parsing des données de recherche:', error);
          this.router.navigate(['/search-flight']);
        }
      } else {
        // Rediriger vers la page de recherche si aucune donnée
        this.router.navigate(['/search-flight']);
      }
    });
  }

  /**
   * Effectue la recherche de vols
   */
  private performSearch(searchData: any): void {
    this.loading = true;
    this.error = null;
    
    // Simuler une recherche pour le moment
    setTimeout(() => {
      this.processSearchResults(this.generateMockResults(), searchData);
      this.loading = false;
    }, 2000);
  }

  /**
   * Traite les résultats de recherche
   */
  private processSearchResults(response: any, searchData: any): void {
    // Créer le résumé de recherche
    this.searchSummary = {
      searchType: searchData.type,
      from: searchData.params.departureLocation,
      to: searchData.params.arrivalLocation,
      departureDate: searchData.params.departureDate,
      returnDate: searchData.params.returnDate,
      passengers: searchData.params.passengers,
      totalResults: 0,
      searchTime: new Date().toLocaleTimeString()
    };

    // Traiter les vols selon le type de réponse
    this.flights = this.extractFlights(response);
    this.searchSummary.totalResults = this.flights.length;
    
    // Appliquer les filtres initiaux
    this.applyFilters();
    this.updatePagination();
  }

  /**
   * Extrait les vols de la réponse API
   */
  private extractFlights(response: any): FlightResult[] {
    // Cette méthode devra être adaptée selon la structure exacte de la réponse
    const flights: FlightResult[] = [];
    
    // Exemple de traitement - à adapter selon la vraie structure
    if (response.body?.flights) {
      response.body.flights.forEach((flight: any, index: number) => {
        flights.push({
          id: flight.id || `flight-${index}`,
          airline: {
            code: flight.airline?.code || 'XX',
            name: flight.airline?.name || 'Compagnie inconnue',
            logo: flight.airline?.logo
          },
          departure: {
            airport: flight.departure?.airport || '',
            city: flight.departure?.city || '',
            time: flight.departure?.time || '',
            date: flight.departure?.date || ''
          },
          arrival: {
            airport: flight.arrival?.airport || '',
            city: flight.arrival?.city || '',
            time: flight.arrival?.time || '',
            date: flight.arrival?.date || ''
          },
          duration: flight.duration || '0h 00m',
          stops: flight.stops || 0,
          price: {
            amount: flight.price?.amount || 0,
            currency: flight.price?.currency || 'EUR',
            formatted: this.formatPrice(flight.price?.amount || 0)
          },
          class: flight.class || 'Economy',
          refundable: flight.refundable || false,
          baggage: flight.baggage
        });
      });
    }
    
    return flights;
  }

  /**
   * Initialise les filtres avec les valeurs par défaut
   */
  private initializeFilters(): void {
    if (this.flights.length > 0) {
      const prices = this.flights.map(f => f.price.amount);
      this.filters.maxPrice = Math.max(...prices);
      
      const airlines = [...new Set(this.flights.map(f => f.airline.code))];
      this.filters.airlines = airlines;
    }
  }

  /**
   * Applique les filtres aux vols
   */
  applyFilters(): void {
    this.filteredFlights = this.flights.filter(flight => {
      // Filtre par prix
      if (flight.price.amount > this.filters.maxPrice) return false;
      
      // Filtre par compagnie aérienne
      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {
        return false;
      }
      
      // Filtre par nombre d'escales
      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {
        return false;
      }
      
      return true;
    });
    
    this.sortFlights();
    this.updatePagination();
  }

  /**
   * Trie les vols selon les critères sélectionnés
   */
  sortFlights(): void {
    this.filteredFlights.sort((a, b) => {
      let comparison = 0;
      
      switch (this.sortBy) {
        case 'price':
          comparison = a.price.amount - b.price.amount;
          break;
        case 'duration':
          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);
          break;
        case 'departure':
          comparison = a.departure.time.localeCompare(b.departure.time);
          break;
      }
      
      return this.sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Met à jour la pagination
   */
  updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = 1;
    }
  }

  /**
   * Obtient les vols pour la page actuelle
   */
  getPaginatedFlights(): FlightResult[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredFlights.slice(startIndex, endIndex);
  }

  /**
   * Change de page
   */
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Sélectionne un vol
   */
  selectFlight(flight: FlightResult): void {
    // Naviguer vers la page de détails ou de réservation
    console.log('Vol sélectionné:', flight);
    // TODO: Implémenter la navigation vers la page de réservation
  }

  /**
   * Retourne à la recherche
   */
  backToSearch(): void {
    this.router.navigate(['/search-flight']);
  }

  /**
   * Modifie la recherche
   */
  modifySearch(): void {
    // Pré-remplir le formulaire avec les critères actuels
    this.router.navigate(['/search-flight'], {
      queryParams: { modify: true, searchData: JSON.stringify(this.searchSummary) }
    });
  }

  /**
   * Formate le prix
   */
  private formatPrice(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  /**
   * Parse la durée en minutes
   */
  private parseDuration(duration: string): number {
    const match = duration.match(/(\d+)h\s*(\d+)m/);
    if (match) {
      return parseInt(match[1]) * 60 + parseInt(match[2]);
    }
    return 0;
  }

  /**
   * Obtient le texte des passagers
   */
  getPassengerText(): string {
    if (!this.searchSummary) return '';

    const parts: string[] = [];
    const p = this.searchSummary.passengers;

    if (p.adults > 0) {
      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);
    }
    if (p.children > 0) {
      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);
    }
    if (p.infants > 0) {
      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);
    }

    return parts.join(', ');
  }

  /**
   * Obtient les compagnies aériennes uniques
   */
  getUniqueAirlines(): { code: string; name: string }[] {
    const airlines = new Map<string, string>();
    this.flights.forEach(flight => {
      airlines.set(flight.airline.code, flight.airline.name);
    });

    return Array.from(airlines.entries()).map(([code, name]) => ({ code, name }));
  }

  /**
   * Toggle le filtre de compagnie aérienne
   */
  toggleAirlineFilter(airlineCode: string): void {
    const index = this.filters.airlines.indexOf(airlineCode);
    if (index > -1) {
      this.filters.airlines.splice(index, 1);
    } else {
      this.filters.airlines.push(airlineCode);
    }
    this.applyFilters();
  }

  /**
   * Toggle le filtre d'escales
   */
  toggleStopsFilter(stops: number): void {
    const index = this.filters.stops.indexOf(stops);
    if (index > -1) {
      this.filters.stops.splice(index, 1);
    } else {
      this.filters.stops.push(stops);
    }
    this.applyFilters();
  }

  /**
   * Efface tous les filtres
   */
  clearFilters(): void {
    this.filters = {
      maxPrice: this.flights.length > 0 ? Math.max(...this.flights.map(f => f.price.amount)) : 0,
      airlines: [],
      stops: [],
      departureTime: { min: 0, max: 24 },
      duration: { min: 0, max: 24 }
    };
    this.applyFilters();
  }

  /**
   * Toggle l'ordre de tri
   */
  toggleSortOrder(): void {
    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    this.sortFlights();
  }

  /**
   * Obtient les numéros de page pour la pagination
   */
  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisible = 5;
    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(this.totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  /**
   * TrackBy function pour la performance
   */
  trackByFlightId(index: number, flight: FlightResult): string {
    return flight.id;
  }

  /**
   * Obtient le prix maximum pour le slider
   */
  getMaxPrice(): number {
    if (this.flights.length === 0) return 1000;
    return Math.max(...this.flights.map(f => f.price.amount));
  }

  /**
   * Génère des résultats de test
   */
  private generateMockResults(): any {
    return {
      body: {
        flights: [
          {
            id: 'flight-1',
            airline: { code: 'TK', name: 'Turkish Airlines' },
            departure: { airport: 'IST', city: 'Istanbul', time: '08:30', date: '2025-06-17' },
            arrival: { airport: 'TUN', city: 'Tunis', time: '11:45', date: '2025-06-17' },
            duration: '3h 15m',
            stops: 0,
            price: { amount: 450, currency: 'EUR' },
            class: 'Economy',
            refundable: true,
            baggage: { carryOn: '8kg', checked: '23kg' }
          },
          {
            id: 'flight-2',
            airline: { code: 'TU', name: 'Tunisair' },
            departure: { airport: 'IST', city: 'Istanbul', time: '14:20', date: '2025-06-17' },
            arrival: { airport: 'TUN', city: 'Tunis', time: '17:35', date: '2025-06-17' },
            duration: '3h 15m',
            stops: 0,
            price: { amount: 380, currency: 'EUR' },
            class: 'Economy',
            refundable: false,
            baggage: { carryOn: '8kg', checked: '20kg' }
          },
          {
            id: 'flight-3',
            airline: { code: 'AF', name: 'Air France' },
            departure: { airport: 'IST', city: 'Istanbul', time: '10:15', date: '2025-06-17' },
            arrival: { airport: 'TUN', city: 'Tunis', time: '16:30', date: '2025-06-17' },
            duration: '6h 15m',
            stops: 1,
            price: { amount: 520, currency: 'EUR' },
            class: 'Economy',
            refundable: true,
            baggage: { carryOn: '12kg', checked: '23kg' }
          },
          {
            id: 'flight-4',
            airline: { code: 'LH', name: 'Lufthansa' },
            departure: { airport: 'IST', city: 'Istanbul', time: '16:45', date: '2025-06-17' },
            arrival: { airport: 'TUN', city: 'Tunis', time: '22:10', date: '2025-06-17' },
            duration: '5h 25m',
            stops: 1,
            price: { amount: 610, currency: 'EUR' },
            class: 'Business',
            refundable: true,
            baggage: { carryOn: '8kg', checked: '32kg' }
          },
          {
            id: 'flight-5',
            airline: { code: 'EK', name: 'Emirates' },
            departure: { airport: 'IST', city: 'Istanbul', time: '22:30', date: '2025-06-17' },
            arrival: { airport: 'TUN', city: 'Tunis', time: '08:45', date: '2025-06-18' },
            duration: '10h 15m',
            stops: 1,
            price: { amount: 750, currency: 'EUR' },
            class: 'Business',
            refundable: true,
            baggage: { carryOn: '7kg', checked: '30kg' }
          }
        ]
      }
    };
  }
}
