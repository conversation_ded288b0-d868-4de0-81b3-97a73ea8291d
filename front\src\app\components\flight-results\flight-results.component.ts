import { Component, OnIni<PERSON>, On<PERSON>estroy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { FlightSearchService } from '../../services/flight-search.service';
import { AirlineLogoService } from '../../services/airline-logo.service';
import { AuthService } from '../../services/auth.service';
import { OneWayResponse } from '../../models/oneway-response.interface';
import { RoundTripResponse } from '../../models/roundtrip-response.interface';
import { MulticityResponse } from '../../models/multicity-response.interface';

export interface FlightSegment {
  airline: {
    code: string;
    name: string;
    logo?: string;
    logoError?: boolean;
  };
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  duration: string;
  stops: number;
  flightNumber: string;
  aircraft?: string;
  baggage?: {
    carryOn: string;
    checked: string;
  };
}

export interface FlightResult {
  id: string;
  searchType: 'oneway' | 'roundtrip' | 'multicity';
  segments: FlightSegment[];
  totalDuration: string;
  price: {
    amount: number;
    currency: string;
    formatted: string;
  };
  class: string;
  refundable: boolean;
  changeable?: boolean;
  // Pour compatibilité avec le code existant (segment principal)
  airline: {
    code: string;
    name: string;
    logo?: string;
    logoError?: boolean;
  };
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  duration: string;
  stops: number;
  baggage?: {
    carryOn: string;
    checked: string;
  };
}

export interface SearchSummary {
  searchType: 'oneway' | 'roundtrip' | 'multicity';
  from: string;
  to: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  totalResults: number;
  searchTime: string;
}

@Component({
  selector: 'app-flight-results',
  templateUrl: './flight-results.component.html',
  styleUrls: ['./flight-results.component.css']
})
export class FlightResultsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // État du composant
  loading = false;
  error: string | null = null;
  
  // Données de recherche
  searchSummary: SearchSummary | null = null;
  flights: FlightResult[] = [];
  filteredFlights: FlightResult[] = [];
  
  // Filtres et tri
  filters = {
    minPrice: 0,
    maxPrice: 0,
    currentPrice: 0,
    airlines: [] as string[],
    stops: [] as number[],
    departureTime: { min: 0, max: 24 },
    duration: { min: 0, max: 24 }
  };
  
  sortBy = 'price'; // price, duration, departure
  sortOrder = 'asc'; // asc, desc
  
  // Options d'affichage
  viewMode = 'list'; // list, grid
  showFilters = true;
  
  // Pagination
  currentPage = 1;
  itemsPerPage = 10;
  totalPages = 1;

  constructor(
    private flightSearchService: FlightSearchService,
    private router: Router,
    private route: ActivatedRoute,
    private airlineLogoService: AirlineLogoService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadSearchResults();
    this.initializeFilters();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Charge les résultats de recherche depuis le service
   */
  private loadSearchResults(): void {
    // Récupérer les paramètres de recherche depuis l'URL ou le service
    this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe(params => {
      if (params['searchData']) {
        try {
          const searchData = JSON.parse(decodeURIComponent(params['searchData']));
          this.performSearch(searchData);
        } catch (error) {
          console.error('Erreur lors du parsing des données de recherche:', error);
          this.router.navigate(['/search-flight']);
        }
      } else {
        // Rediriger vers la page de recherche si aucune donnée
        this.router.navigate(['/search-flight']);
      }
    });
  }

  /**
   * Effectue la recherche de vols
   */
  private performSearch(searchData: any): void {
    this.loading = true;
    this.error = null;

    // Vérifier l'authentification avant de faire l'appel API
    if (!this.authService.isAuthenticated()) {
      console.log('Utilisateur non authentifié, redirection vers la page de connexion');
      this.router.navigate(['/login'], {
        queryParams: { returnUrl: '/flight-results', searchData: JSON.stringify(searchData) }
      });
      this.loading = false;
      return;
    }

    // Utiliser le vrai service de recherche selon le type
    const searchObservable = this.flightSearchService.searchFlights(
      searchData.type,
      searchData.params
    );

    searchObservable.pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.processSearchResults(response, searchData);
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors de la recherche de vols:', error);

        // Gérer les erreurs d'authentification
        if (error.message.includes('Token d\'authentification manquant') ||
            error.message.includes('Token d\'authentification invalide')) {
          console.log('Token invalide, redirection vers la page de connexion');
          this.router.navigate(['/login'], {
            queryParams: { returnUrl: '/flight-results', searchData: JSON.stringify(searchData) }
          });
        } else {
          this.error = error.message || 'Une erreur est survenue lors de la recherche de vols. Veuillez réessayer.';
        }

        this.loading = false;
      }
    });
  }

  /**
   * Traite les résultats de recherche
   */
  private processSearchResults(response: any, searchData: any): void {
    // Créer le résumé de recherche
    this.searchSummary = {
      searchType: searchData.type,
      from: searchData.params.departureLocation,
      to: searchData.params.arrivalLocation,
      departureDate: searchData.params.departureDate,
      returnDate: searchData.params.returnDate,
      passengers: searchData.params.passengers,
      totalResults: 0,
      searchTime: new Date().toLocaleTimeString()
    };

    // Traiter les vols selon le type de réponse
    this.flights = this.extractFlights(response, searchData.type);
    this.searchSummary.totalResults = this.flights.length;

    // Appliquer les filtres initiaux
    this.applyFilters();
    this.updatePagination();
  }

  /**
   * Extrait les vols de la réponse API selon le type de recherche
   */
  private extractFlights(response: any, searchType: 'oneway' | 'roundtrip' | 'multicity'): FlightResult[] {
    const flights: FlightResult[] = [];

    if (!response.body?.flights) {
      return flights;
    }

    response.body.flights.forEach((flight: any, index: number) => {
      try {
        const flightResult = this.createFlightResult(flight, searchType, index);
        if (flightResult) {
          flights.push(flightResult);
        }
      } catch (error) {
        console.error('Erreur lors du traitement du vol:', error, flight);
      }
    });

    return flights;
  }

  /**
   * Crée un FlightResult à partir des données de l'API
   */
  private createFlightResult(flight: any, searchType: 'oneway' | 'roundtrip' | 'multicity', index: number): FlightResult | null {
    if (!flight.items || flight.items.length === 0) {
      return null;
    }

    // Extraire tous les segments
    const segments: FlightSegment[] = flight.items.map((item: any) => this.createFlightSegment(item));

    // Calculer la durée totale
    const totalDuration = this.calculateTotalDuration(segments);

    // Obtenir le prix depuis les offres
    const price = this.extractPrice(flight.offers, searchType);

    // Segment principal (premier segment pour compatibilité)
    const mainSegment = segments[0];

    // Segment final (dernier segment pour l'arrivée finale)
    const finalSegment = segments[segments.length - 1];

    return {
      id: flight.id || `flight-${index}`,
      searchType,
      segments,
      totalDuration,
      price,
      class: this.extractFlightClass(flight, mainSegment),
      refundable: this.isRefundable(flight),
      changeable: this.isChangeable(flight),

      // Propriétés de compatibilité (basées sur le segment principal)
      airline: mainSegment.airline,
      departure: mainSegment.departure,
      arrival: finalSegment.arrival,
      duration: totalDuration,
      stops: this.calculateTotalStops(segments),
      baggage: mainSegment.baggage
    };
  }

  /**
   * Crée un segment de vol à partir des données de l'API
   */
  private createFlightSegment(item: any): FlightSegment {
    const airline = {
      code: item.airline?.internationalCode || item.airline?.code || 'XX',
      name: item.airline?.name || 'Compagnie inconnue',
      logo: item.airline?.logo || item.airline?.logoFull || this.airlineLogoService.getAirlineLogo(item.airline?.internationalCode || item.airline?.code || ''),
      logoError: false
    };

    return {
      airline,
      departure: {
        airport: item.departure?.airport?.code || item.departure?.code || '',
        city: item.departure?.airport?.city || item.departure?.city || '',
        time: this.formatTime(item.departure?.time || item.flightDate),
        date: this.formatDate(item.departure?.date || item.flightDate)
      },
      arrival: {
        airport: item.arrival?.airport?.code || item.arrival?.code || '',
        city: item.arrival?.airport?.city || item.arrival?.city || '',
        time: this.formatTime(item.arrival?.time),
        date: this.formatDate(item.arrival?.date)
      },
      duration: this.formatDuration(item.duration),
      stops: item.stopCount || 0,
      flightNumber: item.flightNo || '',
      aircraft: item.aircraft || '',
      baggage: this.extractBaggage(item.baggageInformations)
    };
  }

  /**
   * Calcule la durée totale de tous les segments
   */
  private calculateTotalDuration(segments: FlightSegment[]): string {
    if (segments.length === 0) return '0h 00m';

    // Pour un seul segment, retourner sa durée
    if (segments.length === 1) {
      return segments[0].duration;
    }

    // Pour plusieurs segments, calculer le temps total entre le départ du premier et l'arrivée du dernier
    try {
      const firstDeparture = new Date(`${segments[0].departure.date}T${segments[0].departure.time}`);
      const lastArrival = new Date(`${segments[segments.length - 1].arrival.date}T${segments[segments.length - 1].arrival.time}`);

      const totalMinutes = Math.floor((lastArrival.getTime() - firstDeparture.getTime()) / (1000 * 60));
      return this.formatDuration(totalMinutes);
    } catch (error) {
      // En cas d'erreur de parsing des dates, additionner les durées individuelles
      const totalMinutes = segments.reduce((total, segment) => {
        return total + this.parseDuration(segment.duration);
      }, 0);
      return this.formatDuration(totalMinutes);
    }
  }

  /**
   * Calcule le nombre total d'escales
   */
  private calculateTotalStops(segments: FlightSegment[]): number {
    return segments.reduce((total, segment) => total + segment.stops, 0);
  }

  /**
   * Extrait le prix depuis les offres selon le type de réponse
   */
  private extractPrice(offers: any[], searchType: string): { amount: number; currency: string; formatted: string } {
    if (!offers || offers.length === 0) {
      return { amount: 0, currency: 'EUR', formatted: this.formatPrice(0) };
    }

    const offer = offers[0];
    let amount = 0;
    let currency = 'EUR';

    // Gérer les différentes structures selon le modèle
    if (searchType === 'oneway' && offer.fees?.oneWay) {
      // Structure OneWayResponse avec fees
      amount = offer.fees.oneWay.totalPrice || 0;
      currency = offer.fees.oneWay.currency || 'EUR';
    } else if (offer.price) {
      // Structure standard avec objet price
      amount = offer.price.amount || 0;
      currency = offer.price.currency || 'EUR';
    } else if (offer.totalPrice !== undefined) {
      // Structure alternative avec totalPrice direct
      amount = offer.totalPrice;
      currency = offer.currency || 'EUR';
    }

    return {
      amount,
      currency,
      formatted: this.formatPrice(amount)
    };
  }

  /**
   * Extrait la classe de vol
   */
  private extractFlightClass(flight: any, mainSegment: FlightSegment): string {
    return flight.flightClass?.name || mainSegment.airline.name || 'Economy';
  }

  /**
   * Vérifie si le vol est remboursable
   */
  private isRefundable(flight: any): boolean {
    return flight.refundable || false;
  }

  /**
   * Vérifie si le vol est modifiable
   */
  private isChangeable(flight: any): boolean {
    return flight.changeable || false;
  }

  /**
   * Extrait les informations de bagages
   */
  private extractBaggage(baggageInfos: any[]): { carryOn: string; checked: string } | undefined {
    if (!baggageInfos || baggageInfos.length === 0) {
      return { carryOn: '8kg', checked: '23kg' };
    }

    let carryOn = '';
    let checked = '';

    baggageInfos.forEach(baggage => {
      const weight = baggage.weight || baggage.piece || 0;
      const unit = baggage.unitType === 0 ? 'kg' : ' pièce(s)';

      if (baggage.baggageType === 0) { // Bagage cabine
        carryOn = `${weight}${unit}`;
      } else if (baggage.baggageType === 1) { // Bagage soute
        checked = `${weight}${unit}`;
      }
    });

    return {
      carryOn: carryOn || '8kg',
      checked: checked || '23kg'
    };
  }

  /**
   * Formate l'heure depuis une date ISO ou un timestamp
   */
  private formatTime(dateTime: string): string {
    if (!dateTime) return '';

    try {
      const date = new Date(dateTime);
      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    } catch {
      return dateTime.substring(11, 16) || '';
    }
  }

  /**
   * Formate la date depuis une date ISO
   */
  private formatDate(dateTime: string): string {
    if (!dateTime) return '';

    try {
      const date = new Date(dateTime);
      return date.toLocaleDateString('fr-FR');
    } catch {
      return dateTime.substring(0, 10) || '';
    }
  }

  /**
   * Formate la durée en minutes vers le format "Xh Ym"
   */
  private formatDuration(minutes: number): string {
    if (!minutes || minutes <= 0) return '0h 00m';

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins.toString().padStart(2, '0')}m`;
  }



  /**
   * Initialise les filtres avec les valeurs par défaut
   */
  private initializeFilters(): void {
    if (this.flights.length > 0) {
      const prices = this.flights.map(f => f.price.amount);
      this.filters.minPrice = Math.min(...prices);
      this.filters.maxPrice = Math.max(...prices);
      this.filters.currentPrice = this.filters.maxPrice; // Commencer avec le prix maximum

      // Initialiser les filtres de compagnies aériennes (tous désélectionnés au début)
      this.filters.airlines = [];

      // Initialiser les filtres d'escales (tous désélectionnés au début)
      this.filters.stops = [];
    } else {
      // Valeurs par défaut si aucun vol
      this.filters.minPrice = 0;
      this.filters.maxPrice = 1000;
      this.filters.currentPrice = 1000;
    }
  }

  /**
   * Applique les filtres aux vols
   */
  applyFilters(): void {
    this.filteredFlights = this.flights.filter(flight => {
      // Filtre par prix (utiliser currentPrice au lieu de maxPrice)
      if (flight.price.amount > this.filters.currentPrice) return false;

      // Filtre par compagnie aérienne (si des compagnies sont sélectionnées)
      if (this.filters.airlines.length > 0 && !this.filters.airlines.includes(flight.airline.code)) {
        return false;
      }

      // Filtre par nombre d'escales (si des escales sont sélectionnées)
      if (this.filters.stops.length > 0 && !this.filters.stops.includes(flight.stops)) {
        return false;
      }

      return true;
    });

    this.sortFlights();
    this.updatePagination();
  }

  /**
   * Trie les vols selon les critères sélectionnés
   */
  sortFlights(): void {
    this.filteredFlights.sort((a, b) => {
      let comparison = 0;
      
      switch (this.sortBy) {
        case 'price':
          comparison = a.price.amount - b.price.amount;
          break;
        case 'duration':
          comparison = this.parseDuration(a.duration) - this.parseDuration(b.duration);
          break;
        case 'departure':
          comparison = a.departure.time.localeCompare(b.departure.time);
          break;
      }
      
      return this.sortOrder === 'asc' ? comparison : -comparison;
    });
  }

  /**
   * Met à jour la pagination
   */
  updatePagination(): void {
    this.totalPages = Math.ceil(this.filteredFlights.length / this.itemsPerPage);
    if (this.currentPage > this.totalPages) {
      this.currentPage = 1;
    }
  }

  /**
   * Obtient les vols pour la page actuelle
   */
  getPaginatedFlights(): FlightResult[] {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredFlights.slice(startIndex, endIndex);
  }

  /**
   * Change de page
   */
  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  }

  /**
   * Sélectionne un vol
   */
  selectFlight(flight: FlightResult): void {
    // Naviguer vers la page de détails ou de réservation
    console.log('Vol sélectionné:', flight);
    // TODO: Implémenter la navigation vers la page de réservation
  }

  /**
   * Affiche les détails d'un vol
   */
  showFlightDetails(flight: FlightResult): void {
    console.log('Affichage des détails du vol:', flight);
    // TODO: Ouvrir un modal avec les détails complets du vol
    // ou naviguer vers une page de détails
  }

  /**
   * Gère l'erreur de chargement du logo
   */
  onLogoError(flight: FlightResult): void {
    if (flight.airline.logo) {
      // Essayer le logo alternatif suivant
      const nextLogo = this.airlineLogoService.getNextLogo(flight.airline.code, flight.airline.logo);
      if (nextLogo) {
        flight.airline.logo = nextLogo;
        console.log(`Tentative avec logo alternatif pour ${flight.airline.code}: ${nextLogo}`);
        return;
      }
    }

    // Aucun logo alternatif disponible
    flight.airline.logoError = true;
    console.log(`Aucun logo disponible pour ${flight.airline.code}`);
  }

  /**
   * Gère le chargement réussi du logo
   */
  onLogoLoad(flight: FlightResult): void {
    flight.airline.logoError = false;
  }

  /**
   * Retourne à la recherche
   */
  backToSearch(): void {
    this.router.navigate(['/search-flight']);
  }

  /**
   * Modifie la recherche
   */
  modifySearch(): void {
    // Pré-remplir le formulaire avec les critères actuels
    this.router.navigate(['/search-flight'], {
      queryParams: { modify: true, searchData: JSON.stringify(this.searchSummary) }
    });
  }

  /**
   * Formate le prix
   */
  private formatPrice(amount: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  /**
   * Parse la durée en minutes
   */
  private parseDuration(duration: string): number {
    const match = duration.match(/(\d+)h\s*(\d+)m/);
    if (match) {
      return parseInt(match[1]) * 60 + parseInt(match[2]);
    }
    return 0;
  }

  /**
   * Obtient le texte des passagers
   */
  getPassengerText(): string {
    if (!this.searchSummary) return '';

    const parts: string[] = [];
    const p = this.searchSummary.passengers;

    if (p.adults > 0) {
      parts.push(`${p.adults} adulte${p.adults > 1 ? 's' : ''}`);
    }
    if (p.children > 0) {
      parts.push(`${p.children} enfant${p.children > 1 ? 's' : ''}`);
    }
    if (p.infants > 0) {
      parts.push(`${p.infants} bébé${p.infants > 1 ? 's' : ''}`);
    }

    return parts.join(', ');
  }

  /**
   * Obtient les compagnies aériennes uniques
   */
  getUniqueAirlines(): { code: string; name: string }[] {
    const airlines = new Map<string, string>();
    this.flights.forEach(flight => {
      airlines.set(flight.airline.code, flight.airline.name);
    });

    return Array.from(airlines.entries()).map(([code, name]) => ({ code, name }));
  }

  /**
   * Toggle le filtre de compagnie aérienne
   */
  toggleAirlineFilter(airlineCode: string): void {
    const index = this.filters.airlines.indexOf(airlineCode);
    if (index > -1) {
      this.filters.airlines.splice(index, 1);
    } else {
      this.filters.airlines.push(airlineCode);
    }
    this.applyFilters();
  }

  /**
   * Toggle le filtre d'escales
   */
  toggleStopsFilter(stops: number): void {
    const index = this.filters.stops.indexOf(stops);
    if (index > -1) {
      this.filters.stops.splice(index, 1);
    } else {
      this.filters.stops.push(stops);
    }
    this.applyFilters();
  }

  /**
   * Efface tous les filtres
   */
  clearFilters(): void {
    if (this.flights.length > 0) {
      const prices = this.flights.map(f => f.price.amount);
      this.filters.minPrice = Math.min(...prices);
      this.filters.maxPrice = Math.max(...prices);
      this.filters.currentPrice = this.filters.maxPrice;
    } else {
      this.filters.minPrice = 0;
      this.filters.maxPrice = 1000;
      this.filters.currentPrice = 1000;
    }

    this.filters.airlines = [];
    this.filters.stops = [];
    this.filters.departureTime = { min: 0, max: 24 };
    this.filters.duration = { min: 0, max: 24 };

    this.applyFilters();
  }

  /**
   * Toggle l'ordre de tri
   */
  toggleSortOrder(): void {
    this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    this.sortFlights();
  }

  /**
   * Obtient les numéros de page pour la pagination
   */
  getPageNumbers(): number[] {
    const pages: number[] = [];
    const maxVisible = 5;
    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));
    let end = Math.min(this.totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  /**
   * TrackBy function pour la performance
   */
  trackByFlightId(index: number, flight: FlightResult): string {
    return flight.id;
  }

  /**
   * Obtient le prix minimum pour le slider
   */
  getMinPrice(): number {
    if (this.flights.length === 0) return 0;
    return Math.min(...this.flights.map(f => f.price.amount));
  }

  /**
   * Obtient le prix maximum pour le slider
   */
  getMaxPrice(): number {
    if (this.flights.length === 0) return 1000;
    return Math.max(...this.flights.map(f => f.price.amount));
  }

  /**
   * Met à jour le prix courant du filtre
   */
  updateCurrentPrice(price: number): void {
    this.filters.currentPrice = price;
    this.applyFilters();
  }

  /**
   * TrackBy function pour les segments
   */
  trackBySegmentIndex(index: number, segment: FlightSegment): number {
    return index;
  }

  /**
   * Gère l'erreur de chargement du logo d'un segment
   */
  onSegmentLogoError(segment: FlightSegment): void {
    if (segment.airline.logo) {
      // Essayer le logo alternatif suivant
      const nextLogo = this.airlineLogoService.getNextLogo(segment.airline.code, segment.airline.logo);
      if (nextLogo) {
        segment.airline.logo = nextLogo;
        console.log(`Tentative avec logo alternatif pour ${segment.airline.code}: ${nextLogo}`);
        return;
      }
    }

    // Aucun logo alternatif disponible
    segment.airline.logoError = true;
    console.log(`Aucun logo disponible pour ${segment.airline.code}`);
  }

  /**
   * Gère le chargement réussi du logo d'un segment
   */
  onSegmentLogoLoad(segment: FlightSegment): void {
    segment.airline.logoError = false;
  }


}
