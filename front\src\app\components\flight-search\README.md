# Composant Flight Search - Block to Book Style

## Description

Composant de recherche de vols moderne avec un design inspiré de "Block to Book". Ce composant permet aux utilisateurs de rechercher des vols en utilisant trois types de recherche : aller simple, aller-retour et multi-destinations, en s'appuyant sur vos modèles backend OneWayRequest, RoundTripRequest et MulticityRequest.

## Fonctionnalités

### 🎨 Design Moderne
- **Interface inspirée de Block to Book** : Design professionnel avec palette de couleurs cohérente
- **Layout responsive** : S'adapte à tous les types d'écrans (mobile, tablette, desktop)
- **Animations fluides** : Hover effects et transitions élégantes
- **Composants interactifs** : Sélecteurs de passagers, échange d'aéroports, etc.

### ✈️ Types de Recherche
- **Aller Simple (One Way)** : Recherche de vols dans une direction
- **Aller-Retour (Round Trip)** : Recherche de vols avec retour
- **Multi-Destinations (Multi-City)** : Recherche de vols avec plusieurs escales

### 🔧 Fonctionnalités Métier

#### Formulaire de Recherche
- **Sélection des destinations** : Champs de départ et d'arrivée avec validation
- **Sélection des dates** : Date picker avec validation des dates
- **Gestion des passagers** : Compteurs pour adultes, enfants et bébés
- **Options de vol** : Classe de voyage, vols directs uniquement
- **Compagnie préférée** : Champ optionnel pour spécifier une compagnie

#### Validation et Erreurs
- **Validation en temps réel** : Feedback immédiat sur les champs
- **Messages d'erreur contextuels** : Affichage des erreurs spécifiques
- **Gestion des erreurs API** : Traitement des réponses d'erreur du backend

## Structure Technique

### Modèles TypeScript

Le composant utilise des interfaces TypeScript qui correspondent exactement à vos modèles backend :

```typescript
// OneWayRequest - Recherche aller simple
interface OneWayRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  // ... autres propriétés
}

// RoundTripRequest - Recherche aller-retour
interface RoundTripRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  Night: number;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  // ... autres propriétés
}

// MulticityRequest - Recherche multi-destinations
interface MulticityRequest {
  serviceTypes: string[];
  productType: number;
  checkIns: string[];
  departureLocations: Location[];
  arrivalLocations: Location[];
  // ... autres propriétés
}
```

### Service de Recherche

Le `FlightSearchService` gère les appels API vers votre backend :

```typescript
@Injectable({
  providedIn: 'root'
})
export class FlightSearchService {
  // Méthodes pour chaque type de recherche
  searchOneWay(request: OneWayRequest): Observable<FlightSearchResponse>
  searchRoundTrip(request: RoundTripRequest): Observable<FlightSearchResponse>
  searchMultiCity(request: MulticityRequest): Observable<FlightSearchResponse>
  
  // Méthode unifiée basée sur le formulaire
  searchFlights(formData: FlightSearchForm): Observable<FlightSearchResponse>
}
```

### Composant Principal

```typescript
@Component({
  selector: 'app-flight-search',
  templateUrl: './flight-search.component.html',
  styleUrls: ['./flight-search.component.css']
})
export class FlightSearchComponent implements OnInit {
  searchForm: FormGroup;
  
  // Gestion des types de recherche
  onSearchTypeChange(searchType: FlightSearchType): void
  
  // Gestion des passagers
  incrementPassenger(type: string): void
  decrementPassenger(type: string): void
  
  // Soumission du formulaire
  onSubmit(): void
}
```

## Configuration

### Variables d'Environnement

Configurez les endpoints dans vos fichiers d'environnement :

```typescript
// environment.ts
export const environment = {
  production: false,
  apiUrl: 'http://localhost:8080',
  authEndpoint: '/auth/login',
  flightSearchEndpoint: '/api/flights/search'
};
```

### Endpoints Backend Attendus

Le service s'attend à ces endpoints sur votre backend :

- `POST /api/flights/search/oneway` - Recherche aller simple
- `POST /api/flights/search/roundtrip` - Recherche aller-retour  
- `POST /api/flights/search/multicity` - Recherche multi-destinations

### Format de Réponse Attendu

```typescript
interface FlightSearchResponse {
  header: {
    requestId: string;
    success: boolean;
    messages: Array<{
      id: number;
      code: string;
      messageType: number;
      message: string;
    }>;
  };
  body: any; // Résultats de recherche selon votre structure
}
```

## Utilisation

### Navigation

Le composant est accessible via la route `/flight-search` et est protégé par l'`AuthGuard`.

### Intégration dans le Dashboard

Une carte "Flights" dans le dashboard permet d'accéder directement au composant de recherche.

### Workflow Utilisateur

1. **Sélection du type de voyage** : One way, Round trip, ou Multi-city
2. **Saisie des destinations** : Départ et arrivée avec possibilité d'échange
3. **Sélection des dates** : Date de départ (et retour si applicable)
4. **Configuration des passagers** : Nombre d'adultes, enfants, bébés
5. **Options de vol** : Classe, vols directs, compagnie préférée
6. **Recherche** : Soumission et traitement de la requête

## Mapping des Données

### Transformation Formulaire → Backend

Le service transforme automatiquement les données du formulaire vers les modèles backend :

```typescript
// Exemple pour OneWayRequest
const oneWayRequest: OneWayRequest = {
  ProductType: 2, // Type produit pour les vols
  ServiceTypes: ['Flight'],
  CheckIn: formData.departureDate,
  DepartureLocations: [{ id: formData.departureLocation, type: 1 }],
  ArrivalLocations: [{ id: formData.arrivalLocation, type: 1 }],
  Passengers: [
    { type: 1, count: formData.passengers.adults },
    { type: 2, count: formData.passengers.children },
    { type: 3, count: formData.passengers.infants }
  ],
  // ... autres propriétés avec valeurs par défaut
};
```

## Responsive Design

### Breakpoints
- **Desktop (>768px)** : Layout en grille avec sidebar des dernières recherches
- **Mobile (≤768px)** : Layout vertical optimisé pour mobile

### Adaptations Mobile
- Formulaire en colonne unique
- Boutons et champs agrandis pour le tactile
- Navigation simplifiée

## Évolutions Futures

### 🔮 Améliorations Possibles
- [ ] Autocomplete pour les aéroports
- [ ] Calendrier de prix
- [ ] Filtres avancés
- [ ] Sauvegarde des recherches favorites
- [ ] Notifications de prix
- [ ] Comparaison de vols

### 📊 Analytics
- [ ] Tracking des recherches populaires
- [ ] Analyse des conversions
- [ ] Optimisation UX basée sur les données

---

**🎉 Le composant Flight Search transforme la recherche de vols en une expérience moderne et intuitive, parfaitement intégrée à votre architecture backend !**
