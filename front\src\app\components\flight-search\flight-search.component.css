/* Variables CSS pour la cohérence */
:root {
  --primary-blue: #4a90e2;
  --secondary-blue: #7bb3f0;
  --light-blue: #e8f4fd;
  --dark-blue: #2c5aa0;
  --success-green: #4caf50;
  --error-red: #f44336;
  --warning-orange: #ff9800;
  --text-dark: #333333;
  --text-light: #666666;
  --text-muted: #999999;
  --background-light: #f8f9fa;
  --white: #ffffff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --border-radius: 8px;
  --transition: all 0.3s ease;
}

/* Container principal */
.flight-search-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Header */
.search-header {
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 0;
  box-shadow: var(--shadow-light);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-icon {
  width: 32px;
  height: 32px;
  color: var(--primary-blue);
}

.logo-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-dark);
  letter-spacing: -0.5px;
}

.agency-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.agency-code, .user-info {
  font-size: 0.875rem;
  color: var(--text-light);
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.header-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-dark);
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition);
}

.header-btn:hover {
  background: var(--light-blue);
  border-color: var(--primary-blue);
  color: var(--primary-blue);
}

.header-btn svg {
  width: 16px;
  height: 16px;
}

.logout-btn:hover {
  background: #ffebee;
  border-color: var(--error-red);
  color: var(--error-red);
}

/* Zone principale */
.search-main {
  padding: 2rem 0;
}

.search-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

/* Section de recherche */
.search-section {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  overflow: hidden;
}

.search-card {
  padding: 2rem;
}

.search-header-title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.title-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-icon svg {
  width: 24px;
  height: 24px;
  color: var(--white);
}

.title-content h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 0.25rem 0;
}

.title-content p {
  font-size: 0.875rem;
  color: var(--text-light);
  margin: 0;
}

/* Formulaire de recherche */
.search-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Onglets de type de voyage */
.trip-type-tabs {
  display: flex;
  background: var(--background-light);
  border-radius: var(--border-radius);
  padding: 0.25rem;
  gap: 0.25rem;
}

.trip-tab {
  flex: 1;
  text-align: center;
  padding: 0.75rem 1rem;
  border-radius: calc(var(--border-radius) - 2px);
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-light);
}

.trip-tab input {
  display: none;
}

.trip-tab.active {
  background: var(--primary-blue);
  color: var(--white);
  box-shadow: var(--shadow-light);
}

/* Ligne de destinations */
.destination-row {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 1rem;
  align-items: end;
}

.destination-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.destination-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-dark);
}

.label-icon {
  width: 16px;
  height: 16px;
  color: var(--primary-blue);
}

.destination-input {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.destination-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.destination-input.error {
  border-color: var(--error-red);
}

.swap-button {
  width: 40px;
  height: 40px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  margin-bottom: 0.5rem;
}

.swap-button:hover {
  border-color: var(--primary-blue);
  background: var(--light-blue);
}

.swap-button svg {
  width: 20px;
  height: 20px;
  color: var(--primary-blue);
}

/* Ligne de dates */
.date-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.date-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-dark);
}

.date-input {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition);
  background: var(--white);
}

.date-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.date-input.error {
  border-color: var(--error-red);
}

/* Options de voyage */
.options-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1rem;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.option-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-dark);
}

.passengers-group {
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  background: var(--white);
}

.passengers-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.passenger-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.passenger-icon {
  font-size: 1.25rem;
}

.passenger-count {
  font-weight: 600;
  color: var(--text-dark);
  min-width: 20px;
  text-align: center;
}

.passenger-controls {
  display: flex;
  gap: 0.25rem;
}

.passenger-btn {
  width: 24px;
  height: 24px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 600;
  transition: var(--transition);
}

.passenger-btn:hover:not(:disabled) {
  background: var(--primary-blue);
  color: var(--white);
  border-color: var(--primary-blue);
}

.passenger-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.passenger-summary {
  font-size: 0.75rem;
  color: var(--text-light);
  font-style: italic;
}

.option-select {
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
  background: var(--white);
  transition: var(--transition);
}

.option-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Options supplémentaires */
.additional-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
}

.checkbox-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-option input {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid var(--border-color);
  border-radius: 3px;
  position: relative;
  transition: var(--transition);
}

.checkbox-option input:checked + .checkbox-custom {
  background: var(--primary-blue);
  border-color: var(--primary-blue);
}

.checkbox-option input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--white);
  font-size: 12px;
  font-weight: bold;
}

.checkbox-label {
  font-size: 0.875rem;
  color: var(--text-dark);
}

.airline-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  transition: var(--transition);
  min-width: 200px;
}

.airline-input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

/* Messages d'erreur */
.error-message {
  font-size: 0.75rem;
  color: var(--error-red);
  margin-top: 0.25rem;
}

.error-alert {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: var(--border-radius);
  color: var(--error-red);
  font-size: 0.875rem;
}

.error-icon {
  width: 16px;
  height: 16px;
}

/* Bouton de recherche */
.search-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, var(--success-green), #66bb6a);
  border: none;
  border-radius: var(--border-radius);
  color: var(--white);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 48px;
}

.search-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #43a047, var(--success-green));
  transform: translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.button-icon {
  width: 20px;
  height: 20px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Section des dernières recherches */
.latest-searches {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  overflow: hidden;
}

.searches-card {
  padding: 2rem;
}

.searches-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 0.5rem 0;
}

.searches-card p {
  font-size: 0.875rem;
  color: var(--text-light);
  margin: 0 0 1.5rem 0;
}

.search-history {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.search-item {
  padding: 0.75rem;
  background: var(--background-light);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-blue);
}

.search-route {
  font-size: 0.875rem;
  color: var(--text-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-content {
    grid-template-columns: 1fr;
    padding: 0 1rem;
  }
  
  .header-content {
    padding: 0 1rem;
    flex-direction: column;
    gap: 1rem;
  }
  
  .destination-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .swap-button {
    order: 3;
    margin: 0;
    align-self: center;
  }
  
  .date-row {
    grid-template-columns: 1fr;
  }
  
  .options-row {
    grid-template-columns: 1fr;
  }
  
  .additional-options {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .passengers-selector {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .passenger-type {
    justify-content: space-between;
  }
}
