<div class="flight-search-container">
  <!-- Header avec informations de l'agence -->
  <header class="search-header">
    <div class="header-content">
      <div class="logo-section">
        <div class="logo">
          <svg class="logo-icon" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
          </svg>
          <span class="logo-text">BLOCK TO BOOK</span>
        </div>
        <div class="agency-info" *ngIf="userInfo">
          <span class="agency-code">{{ userInfo.agency?.code }} - {{ userInfo.agency?.name }}</span>
          <span class="user-info">{{ userInfo.code }} - {{ userInfo.name }}</span>
        </div>
      </div>
      
      <div class="header-actions">
        <button class="header-btn" routerLink="/dashboard">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
          </svg>
          Home
        </button>
        <button class="header-btn">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          Tools
        </button>
        <button class="header-btn logout-btn" (click)="authService.logout(); router.navigate(['/login'])">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5z"/>
          </svg>
          Logout
        </button>
      </div>
    </div>
  </header>

  <!-- Zone principale de recherche -->
  <main class="search-main">
    <div class="search-content">
      <!-- Section de recherche -->
      <div class="search-section">
        <div class="search-card">
          <div class="search-header-title">
            <div class="title-icon">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
              </svg>
            </div>
            <div class="title-content">
              <h1>Search and Book Flights</h1>
              <p>We're bringing you a new level of comfort</p>
            </div>
          </div>

          <!-- Formulaire de recherche -->
          <form [formGroup]="searchForm" (ngSubmit)="onSubmit()" class="search-form" novalidate>
            
            <!-- Types de voyage -->
            <div class="trip-type-tabs">
              <label class="trip-tab" [class.active]="searchForm.get('searchType')?.value === FlightSearchType.ONE_WAY">
                <input type="radio" [value]="FlightSearchType.ONE_WAY" formControlName="searchType">
                <span>One way</span>
              </label>
              <label class="trip-tab" [class.active]="searchForm.get('searchType')?.value === FlightSearchType.ROUND_TRIP">
                <input type="radio" [value]="FlightSearchType.ROUND_TRIP" formControlName="searchType">
                <span>Round Trip</span>
              </label>
              <label class="trip-tab" [class.active]="searchForm.get('searchType')?.value === FlightSearchType.MULTI_CITY">
                <input type="radio" [value]="FlightSearchType.MULTI_CITY" formControlName="searchType">
                <span>Multi-City/Stop-Overs</span>
              </label>
            </div>

            <!-- Champs de destination -->
            <div class="destination-row">
              <div class="destination-group">
                <label class="destination-label">
                  <svg class="label-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                  </svg>
                  Leaving From (City, Country Or Specific Airport)
                </label>
                <input 
                  type="text" 
                  formControlName="departureLocation"
                  class="destination-input"
                  [class.error]="hasError('departureLocation', 'required') || hasError('departureLocation', 'minlength')"
                  placeholder="Enter departure location"
                  autocomplete="off"
                >
                <div class="error-message" *ngIf="hasError('departureLocation', 'required')">
                  Departure location is required
                </div>
                <div class="error-message" *ngIf="hasError('departureLocation', 'minlength')">
                  Please enter at least 3 characters
                </div>
              </div>

              <button type="button" class="swap-button" (click)="swapAirports()" title="Swap airports">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6.99 11L3 15l3.99 4v-3H14v-2H6.99v-3zM21 9l-3.99-4v3H10v2h7.01v3L21 9z"/>
                </svg>
              </button>

              <div class="destination-group">
                <label class="destination-label">
                  <svg class="label-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                  </svg>
                  Going To (City, Country Or Specific Airport)
                </label>
                <input 
                  type="text" 
                  formControlName="arrivalLocation"
                  class="destination-input"
                  [class.error]="hasError('arrivalLocation', 'required') || hasError('arrivalLocation', 'minlength')"
                  placeholder="Enter destination"
                  autocomplete="off"
                >
                <div class="error-message" *ngIf="hasError('arrivalLocation', 'required')">
                  Destination is required
                </div>
                <div class="error-message" *ngIf="hasError('arrivalLocation', 'minlength')">
                  Please enter at least 3 characters
                </div>
              </div>
            </div>

            <!-- Dates -->
            <div class="date-row">
              <div class="date-group">
                <label class="date-label">From</label>
                <input 
                  type="date" 
                  formControlName="departureDate"
                  class="date-input"
                  [class.error]="hasError('departureDate', 'required')"
                  [min]="getTodayDate()"
                >
                <div class="error-message" *ngIf="hasError('departureDate', 'required')">
                  Departure date is required
                </div>
              </div>

              <div class="date-group" *ngIf="searchForm.get('searchType')?.value === FlightSearchType.ROUND_TRIP">
                <label class="date-label">To</label>
                <input 
                  type="date" 
                  formControlName="returnDate"
                  class="date-input"
                  [class.error]="hasError('returnDate', 'required')"
                  [min]="searchForm.get('departureDate')?.value"
                >
                <div class="error-message" *ngIf="hasError('returnDate', 'required')">
                  Return date is required
                </div>
              </div>
            </div>

            <!-- Options de voyage -->
            <div class="options-row">
              <!-- Passagers -->
              <div class="option-group passengers-group">
                <label class="option-label">Passenger & Class of travel</label>
                <div class="passengers-selector">
                  <div class="passenger-type">
                    <span class="passenger-icon">👤</span>
                    <span class="passenger-count">{{ searchForm.get('adults')?.value }}</span>
                    <div class="passenger-controls">
                      <button type="button" class="passenger-btn" (click)="decrementPassenger('adults')" [disabled]="searchForm.get('adults')?.value <= 1">-</button>
                      <button type="button" class="passenger-btn" (click)="incrementPassenger('adults')" [disabled]="searchForm.get('adults')?.value >= 9">+</button>
                    </div>
                  </div>
                  
                  <div class="passenger-type">
                    <span class="passenger-icon">👶</span>
                    <span class="passenger-count">{{ searchForm.get('children')?.value }}</span>
                    <div class="passenger-controls">
                      <button type="button" class="passenger-btn" (click)="decrementPassenger('children')" [disabled]="searchForm.get('children')?.value <= 0">-</button>
                      <button type="button" class="passenger-btn" (click)="incrementPassenger('children')" [disabled]="searchForm.get('children')?.value >= 9">+</button>
                    </div>
                  </div>
                  
                  <div class="passenger-type">
                    <span class="passenger-icon">🍼</span>
                    <span class="passenger-count">{{ searchForm.get('infants')?.value }}</span>
                    <div class="passenger-controls">
                      <button type="button" class="passenger-btn" (click)="decrementPassenger('infants')" [disabled]="searchForm.get('infants')?.value <= 0">-</button>
                      <button type="button" class="passenger-btn" (click)="incrementPassenger('infants')" [disabled]="searchForm.get('infants')?.value >= searchForm.get('adults')?.value">+</button>
                    </div>
                  </div>
                </div>
                <div class="passenger-summary">{{ getPassengerText() }}</div>
              </div>

              <!-- Classe de vol -->
              <div class="option-group">
                <label class="option-label">Preferred Airline</label>
                <select formControlName="flightClass" class="option-select">
                  <option *ngFor="let flightClass of flightClasses" [value]="flightClass.value">
                    {{ flightClass.label }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Options supplémentaires -->
            <div class="additional-options">
              <label class="checkbox-option">
                <input type="checkbox" formControlName="directFlightsOnly">
                <span class="checkbox-custom"></span>
                <span class="checkbox-label">Direct flights only</span>
              </label>
              
              <div class="airline-preference">
                <input 
                  type="text" 
                  formControlName="preferredAirline"
                  class="airline-input"
                  placeholder="Preferred Airline (Optional)"
                >
              </div>
            </div>

            <!-- Message d'erreur global -->
            <div class="error-alert" *ngIf="errorMessage">
              <svg class="error-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              {{ errorMessage }}
            </div>

            <!-- Bouton de recherche -->
            <button 
              type="submit" 
              class="search-button"
              [disabled]="isLoading || searchForm.invalid"
              [class.loading]="isLoading"
            >
              <span *ngIf="!isLoading" class="button-content">
                <svg class="button-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
                SEARCH NOW
              </span>
              <span *ngIf="isLoading" class="loading-content">
                <svg class="loading-spinner" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4" fill="none" opacity="0.25"></circle>
                  <path fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" opacity="0.75"></path>
                </svg>
                Searching...
              </span>
            </button>
          </form>
        </div>
      </div>

      <!-- Section des dernières recherches (optionnel) -->
      <div class="latest-searches" *ngIf="!isLoading && !searchResults">
        <div class="searches-card">
          <h3>Latest Searches</h3>
          <p>We're bringing you a new level of comfort</p>
          <div class="search-history">
            <div class="search-item">
              <span class="search-route">Coming From IST - TUN on Jun 18, 2025</span>
            </div>
            <div class="search-item">
              <span class="search-route">Coming From IST - TUN on Jun 18, 2025</span>
            </div>
            <div class="search-item">
              <span class="search-route">Coming From TUN - IST on Jun 11, 2025</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
