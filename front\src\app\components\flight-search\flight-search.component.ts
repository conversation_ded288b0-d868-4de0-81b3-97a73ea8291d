import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { FlightSearchService } from '../../services/flight-search.service';
import { AuthService } from '../../services/auth.service';
import { 
  FlightSearchForm, 
  FlightSearchType, 
  FlightClass,
  FlightSearchResponse 
} from '../../models/flight-search-request.interface';

@Component({
  selector: 'app-flight-search',
  templateUrl: './flight-search.component.html',
  styleUrls: ['./flight-search.component.css']
})
export class FlightSearchComponent implements OnInit {
  searchForm!: FormGroup;
  isLoading = false;
  errorMessage = '';
  searchResults: any = null;
  
  // Énumérations pour le template
  FlightSearchType = FlightSearchType;
  FlightClass = FlightClass;
  
  // Options pour les sélecteurs
  flightClasses = [
    { value: FlightClass.ECONOMY, label: 'Economy' },
    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },
    { value: FlightClass.BUSINESS, label: 'Business' },
    { value: FlightClass.FIRST, label: 'First Class' }
  ];

  // Données utilisateur
  userInfo: any;

  constructor(
    private formBuilder: FormBuilder,
    private flightSearchService: FlightSearchService,
    public authService: AuthService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.loadUserInfo();
    
    // Vérifier l'authentification
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login']);
    }
  }

  /**
   * Initialise le formulaire de recherche
   */
  private initializeForm(): void {
    this.searchForm = this.formBuilder.group({
      searchType: [FlightSearchType.ONE_WAY, Validators.required],
      departureLocation: ['', [Validators.required, Validators.minLength(3)]],
      arrivalLocation: ['', [Validators.required, Validators.minLength(3)]],
      departureDate: ['', Validators.required],
      returnDate: [''],
      adults: [1, [Validators.required, Validators.min(1), Validators.max(9)]],
      children: [0, [Validators.min(0), Validators.max(9)]],
      infants: [0, [Validators.min(0), Validators.max(9)]],
      flightClass: [FlightClass.ECONOMY, Validators.required],
      directFlightsOnly: [false],
      preferredAirline: ['']
    });

    // Surveiller les changements du type de recherche
    this.searchForm.get('searchType')?.valueChanges.subscribe(value => {
      this.onSearchTypeChange(value);
    });
  }

  /**
   * Charge les informations utilisateur
   */
  private loadUserInfo(): void {
    this.userInfo = this.authService.getUserInfo();
  }

  /**
   * Gère le changement de type de recherche
   */
  onSearchTypeChange(searchType: FlightSearchType): void {
    const returnDateControl = this.searchForm.get('returnDate');
    
    if (searchType === FlightSearchType.ROUND_TRIP) {
      returnDateControl?.setValidators([Validators.required]);
    } else {
      returnDateControl?.clearValidators();
      returnDateControl?.setValue('');
    }
    
    returnDateControl?.updateValueAndValidity();
  }

  /**
   * Échange les aéroports de départ et d'arrivée
   */
  swapAirports(): void {
    const departure = this.searchForm.get('departureLocation')?.value;
    const arrival = this.searchForm.get('arrivalLocation')?.value;
    
    this.searchForm.patchValue({
      departureLocation: arrival,
      arrivalLocation: departure
    });
  }

  /**
   * Incrémente le nombre de passagers
   */
  incrementPassenger(type: string): void {
    const control = this.searchForm.get(type);
    const currentValue = control?.value || 0;
    const maxValue = type === 'infants' ? this.searchForm.get('adults')?.value : 9;
    
    if (currentValue < maxValue) {
      control?.setValue(currentValue + 1);
    }
  }

  /**
   * Décrémente le nombre de passagers
   */
  decrementPassenger(type: string): void {
    const control = this.searchForm.get(type);
    const currentValue = control?.value || 0;
    const minValue = type === 'adults' ? 1 : 0;
    
    if (currentValue > minValue) {
      control?.setValue(currentValue - 1);
    }
  }

  /**
   * Calcule le nombre total de passagers
   */
  getTotalPassengers(): number {
    const adults = this.searchForm.get('adults')?.value || 0;
    const children = this.searchForm.get('children')?.value || 0;
    const infants = this.searchForm.get('infants')?.value || 0;
    return adults + children + infants;
  }

  /**
   * Obtient le texte descriptif des passagers
   */
  getPassengerText(): string {
    const adults = this.searchForm.get('adults')?.value || 0;
    const children = this.searchForm.get('children')?.value || 0;
    const infants = this.searchForm.get('infants')?.value || 0;
    
    let text = `${adults} Adult${adults > 1 ? 's' : ''}`;
    if (children > 0) text += `, ${children} Child${children > 1 ? 'ren' : ''}`;
    if (infants > 0) text += `, ${infants} Infant${infants > 1 ? 's' : ''}`;
    
    return text;
  }

  /**
   * Vérifie si un champ a une erreur spécifique
   */
  hasError(fieldName: string, errorType: string): boolean {
    const field = this.searchForm.get(fieldName);
    return !!(field && field.hasError(errorType) && (field.dirty || field.touched));
  }

  /**
   * Marque tous les champs du formulaire comme touchés
   */
  private markFormGroupTouched(): void {
    Object.keys(this.searchForm.controls).forEach(key => {
      const control = this.searchForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Soumet le formulaire de recherche
   */
  onSubmit(): void {
    if (this.searchForm.valid && !this.isLoading) {
      this.isLoading = true;
      this.errorMessage = '';
      this.searchResults = null;

      const formData: FlightSearchForm = {
        searchType: this.searchForm.value.searchType,
        departureLocation: this.searchForm.value.departureLocation.trim(),
        arrivalLocation: this.searchForm.value.arrivalLocation.trim(),
        departureDate: this.searchForm.value.departureDate,
        returnDate: this.searchForm.value.returnDate || undefined,
        passengers: {
          adults: this.searchForm.value.adults,
          children: this.searchForm.value.children,
          infants: this.searchForm.value.infants
        },
        flightClass: this.searchForm.value.flightClass,
        directFlightsOnly: this.searchForm.value.directFlightsOnly,
        preferredAirline: this.searchForm.value.preferredAirline || undefined,
        culture: 'fr-FR', // Peut être configuré selon les préférences utilisateur
        currency: 'EUR'   // Peut être configuré selon les préférences utilisateur
      };

      this.flightSearchService.searchFlights(formData).subscribe({
        next: (response: FlightSearchResponse) => {
          this.isLoading = false;
          
          if (response.header.success) {
            this.searchResults = response.body;
            console.log('Résultats de recherche:', this.searchResults);
            // Ici vous pouvez naviguer vers une page de résultats ou afficher les résultats
          } else {
            this.handleApiError(response);
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = error.message || 'Une erreur est survenue lors de la recherche';
          console.error('Erreur de recherche:', error);
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  /**
   * Gère les erreurs retournées par l'API
   */
  private handleApiError(response: FlightSearchResponse): void {
    if (response.header.messages && response.header.messages.length > 0) {
      this.errorMessage = response.header.messages[0].message;
    } else {
      this.errorMessage = 'Aucun vol trouvé pour ces critères de recherche';
    }
  }

  /**
   * Réinitialise le formulaire
   */
  resetForm(): void {
    this.searchForm.reset();
    this.initializeForm();
    this.errorMessage = '';
    this.searchResults = null;
  }

  /**
   * Obtient la date d'aujourd'hui au format YYYY-MM-DD
   */
  getTodayDate(): string {
    const today = new Date();
    return today.toISOString().split('T')[0];
  }
}
