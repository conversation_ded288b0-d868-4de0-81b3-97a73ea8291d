.flight-search-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Section */
.search-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
  z-index: 10;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-icon {
  display: inline-block;
  width: 60px;
  height: 60px;
  margin-right: 1rem;
  vertical-align: middle;
}

.header-icon svg {
  width: 100%;
  height: 100%;
}

.header-text {
  display: inline-block;
  vertical-align: middle;
  color: white;
}

.header-text h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.header-text p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Search Form Container */
.search-form-container {
  flex: 1;
  max-width: 800px;
  margin: 180px auto 2rem;
  padding: 0 2rem;
}

.search-form {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

/* Trip Type Tabs */
.trip-type-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #e1e8ed;
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: #f8f9fa;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-right: 1px solid #e1e8ed;
}

.tab-button:last-child {
  border-right: none;
}

.tab-button.active {
  background: #2c5aa0;
  color: white;
}

.tab-button:hover:not(.active) {
  background: #e9ecef;
}

/* Input Styles */
.input-row {
  margin-bottom: 1.5rem;
}

.input-group {
  display: flex;
  gap: 1rem;
  align-items: end;
}

.from-to {
  position: relative;
}

.input-field {
  flex: 1;
  position: relative;
}

.input-field label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.location-input,
.date-input,
.airline-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.location-input:focus,
.date-input:focus,
.airline-input:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #6c757d;
  margin-top: 12px;
}

.input-icon svg {
  width: 100%;
  height: 100%;
}

/* Swap Button */
.swap-button {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
  margin-top: 1.5rem;
}

.btn-swap {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #2c5aa0;
  background: white;
  color: #2c5aa0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-swap:hover {
  background: #2c5aa0;
  color: white;
}

.btn-swap svg {
  width: 20px;
  height: 20px;
}

/* Multi-city Styles */
.multicity-inputs {
  margin-bottom: 1.5rem;
}

.sector-row {
  display: flex;
  gap: 1rem;
  align-items: end;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.btn-remove {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #dc3545;
  background: white;
  color: #dc3545;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
}

.btn-remove:hover {
  background: #dc3545;
  color: white;
}

.btn-add-sector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: 2px dashed #2c5aa0;
  background: transparent;
  color: #2c5aa0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-add-sector:hover {
  background: #2c5aa0;
  color: white;
  border-style: solid;
}

.btn-add-sector svg {
  width: 20px;
  height: 20px;
}

/* Passenger & Class Row */
.passenger-class-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.passenger-selector {
  flex: 2;
}

.passenger-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  background: white;
  flex-wrap: wrap;
}

.passenger-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.passenger-icon {
  font-size: 1.2rem;
}

.counter-buttons {
  display: flex;
  gap: 0.25rem;
}

.counter-buttons button {
  width: 30px;
  height: 30px;
  border: 1px solid #dee2e6;
  background: white;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: bold;
}

.counter-buttons button:hover {
  background: #2c5aa0;
  color: white;
  border-color: #2c5aa0;
}

.class-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.class-icon {
  font-size: 1.2rem;
}

.class-select {
  padding: 0.5rem;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  background: white;
  cursor: pointer;
}

.preferred-airline {
  flex: 1;
  min-width: 200px;
}

.airline-input {
  padding: 1rem;
}

/* Additional Options */
.additional-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.option-group {
  flex: 1;
  min-width: 150px;
}

.option-select {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  font-size: 1rem;
}

.option-select:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

/* Search Button */
.search-button-container {
  text-align: center;
}

.search-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1rem 3rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
}

.search-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(40, 167, 69, 0.4);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Latest Searches Sidebar */
.latest-searches {
  width: 350px;
  background: white;
  padding: 2rem;
  margin: 180px 2rem 2rem 0;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.latest-searches h3 {
  color: #2c5aa0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.latest-searches p {
  color: #6c757d;
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.search-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px dashed #e1e8ed;
}

.search-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.search-dot {
  width: 8px;
  height: 8px;
  background: #2c5aa0;
  border-radius: 50%;
  margin-top: 0.5rem;
  flex-shrink: 0;
}

.search-item span {
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.4;
}

.search-item strong {
  color: #2c5aa0;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .flight-search-container {
    flex-direction: column;
  }
  
  .latest-searches {
    width: auto;
    margin: 2rem;
    margin-top: 0;
  }
  
  .search-form-container {
    margin-top: 180px;
  }
}

@media (max-width: 768px) {
  .header-text h1 {
    font-size: 2rem;
  }
  
  .search-form {
    padding: 1.5rem;
  }
  
  .passenger-class-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .additional-options {
    flex-direction: column;
  }
  
  .input-group.from-to {
    flex-direction: column;
  }
  
  .swap-button {
    align-self: center;
    margin: 0.5rem 0;
  }
  
  .sector-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .passenger-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .class-selector {
    margin-left: 0;
  }
}
