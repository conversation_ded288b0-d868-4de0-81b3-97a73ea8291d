.flight-search-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Header Section */
.search-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem 0;
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
}

.header-icon {
  width: 60px;
  height: 60px;
  margin-right: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.header-icon svg {
  width: 100%;
  height: 100%;
}

.header-text {
  color: white;
}

.header-text h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-text p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
}

/* Search Form Container */
.search-form-container {
  flex: 1;
  max-width: 900px;
  margin: 140px auto 2rem;
  padding: 0 2rem;
  z-index: 5;
}

.search-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Trip Type Tabs */
.trip-type-tabs {
  display: flex;
  margin-bottom: 2.5rem;
  border-radius: 16px;
  overflow: hidden;
  background: #f8f9fa;
  padding: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-button {
  flex: 1;
  padding: 1rem 1.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  font-size: 0.95rem;
}

.tab-button.active {
  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
  transform: translateY(-1px);
}

.tab-button:hover:not(.active) {
  background: rgba(44, 90, 160, 0.1);
  color: #2c5aa0;
}

/* Input Styles */
.input-row {
  margin-bottom: 2rem;
}

.input-group {
  display: flex;
  gap: 1.5rem;
  align-items: end;
}

.from-to {
  position: relative;
}

.input-field {
  flex: 1;
  position: relative;
}

.input-field label {
  display: block;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.location-input,
.date-input,
.option-input {
  width: 100%;
  padding: 1.25rem 1.25rem 1.25rem 3.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  font-weight: 500;
}

.location-input:focus,
.date-input:focus,
.option-input:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

/* Styles pour l'autocomplétion dans le contexte du formulaire */
.input-field app-autocomplete {
  width: 100%;
}

.input-field app-autocomplete .autocomplete-input {
  padding: 1.25rem 1.25rem 1.25rem 3.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;
  font-weight: 500;
}

.input-field app-autocomplete .autocomplete-input:focus {
  border-color: #2c5aa0;
  box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);
  transform: translateY(-2px);
}

.input-icon {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #718096;
  margin-top: 12px;
}

.input-icon svg {
  width: 100%;
  height: 100%;
}

/* Swap Button */
.swap-button {
  display: flex;
  align-items: center;
  margin: 0 0.5rem;
  margin-top: 1.5rem;
}

.btn-swap {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 2px solid #2c5aa0;
  background: white;
  color: #2c5aa0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.2);
}

.btn-swap:hover {
  background: #2c5aa0;
  color: white;
  transform: rotate(180deg) scale(1.1);
  box-shadow: 0 8px 24px rgba(44, 90, 160, 0.3);
}

.btn-swap svg {
  width: 20px;
  height: 20px;
}

/* Multi-city Styles */
.multicity-inputs {
  margin-bottom: 2rem;
}

.sector-row {
  display: flex;
  gap: 1.5rem;
  align-items: end;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
}

.btn-remove {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #dc3545;
  background: white;
  color: #dc3545;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1.5rem;
}

.btn-remove:hover {
  background: #dc3545;
  color: white;
  transform: scale(1.1);
}

.btn-add-sector {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border: 2px dashed #2c5aa0;
  background: rgba(44, 90, 160, 0.05);
  color: #2c5aa0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  width: fit-content;
  margin: 0 auto;
}

.btn-add-sector:hover {
  background: #2c5aa0;
  color: white;
  border-style: solid;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(44, 90, 160, 0.3);
}

.btn-add-sector svg {
  width: 20px;
  height: 20px;
}

/* Passenger & Class Row */
.passenger-class-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.passenger-selector {
  flex: 2;
  min-width: 300px;
}

.passenger-controls {
  display: flex;
  gap: 1.5rem;
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  flex-wrap: wrap;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.passenger-type {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 80px;
}

.passenger-icon {
  font-size: 1.5rem;
}

.passenger-label {
  font-size: 0.8rem;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.counter-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f7fafc;
  border-radius: 12px;
  padding: 0.25rem;
}

.counter-buttons button {
  width: 32px;
  height: 32px;
  border: none;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: bold;
  color: #2c5aa0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.counter-buttons button:hover {
  background: #2c5aa0;
  color: white;
  transform: scale(1.1);
}

.counter-buttons .count {
  min-width: 24px;
  text-align: center;
  font-weight: 600;
  color: #2d3748;
}

.class-selector {
  flex: 1;
  min-width: 200px;
}

.class-select,
.option-select {
  width: 100%;
  padding: 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.class-select:focus,
.option-select:focus {
  outline: none;
  border-color: #2c5aa0;
  box-shadow: 0 0 0 4px rgba(44, 90, 160, 0.1);
}

/* Additional Options */
.additional-options {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.option-group {
  flex: 1;
  min-width: 200px;
}

/* Search Button */
.search-button-container {
  text-align: center;
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.search-button {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  padding: 1.25rem 3rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 12px 24px rgba(40, 167, 69, 0.3);
  min-width: 200px;
}

.search-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 16px 32px rgba(40, 167, 69, 0.4);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
}

.clear-button {
  background: transparent;
  color: #6c757d;
  border: 2px solid #e2e8f0;
  padding: 1.25rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.clear-button:hover {
  border-color: #dc3545;
  color: #dc3545;
  transform: translateY(-2px);
}

.loading-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.spinner {
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Latest Searches Sidebar */
.latest-searches {
  width: 350px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  padding: 2rem;
  margin: 140px 2rem 2rem 0;
  border-radius: 24px;
  box-shadow: 
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  height: fit-content;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.latest-searches h3 {
  color: #2c5aa0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.latest-searches p {
  color: #6c757d;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 300;
}

.search-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px dashed rgba(44, 90, 160, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.search-item:hover {
  transform: translateX(4px);
  background: rgba(44, 90, 160, 0.05);
  margin: 0 -1rem 1rem -1rem;
  padding: 1rem 1rem 1rem 0.75rem;
  border-radius: 12px;
}

.search-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
  border-radius: 50%;
  margin-top: 0.5rem;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(44, 90, 160, 0.3);
}

.search-item span {
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.4;
  font-weight: 400;
}

.search-item strong {
  color: #2c5aa0;
  font-weight: 700;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .flight-search-container {
    flex-direction: column;
  }
  
  .latest-searches {
    width: auto;
    margin: 2rem;
    margin-top: 0;
  }
  
  .search-form-container {
    margin-top: 140px;
  }
}

@media (max-width: 768px) {
  .header-text h1 {
    font-size: 2rem;
  }
  
  .search-form {
    padding: 2rem;
  }
  
  .passenger-class-row {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .additional-options {
    flex-direction: column;
  }
  
  .input-group.from-to {
    flex-direction: column;
  }
  
  .swap-button {
    align-self: center;
    margin: 1rem 0;
  }
  
  .sector-row {
    flex-direction: column;
    gap: 1rem;
  }
  
  .passenger-controls {
    justify-content: center;
  }
  
  .search-button-container {
    flex-direction: column;
    align-items: center;
  }
  
  .search-button,
  .clear-button {
    width: 100%;
    max-width: 300px;
  }
}
