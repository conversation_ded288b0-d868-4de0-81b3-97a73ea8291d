<div class="flight-search-container">
  <!-- Header Section -->
  <div class="search-header">
    <div class="header-content">
      <div class="header-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <circle cx="12" cy="12" r="10" fill="#2c5aa0"/>
          <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" fill="white"/>
        </svg>
      </div>
      <div class="header-text">
        <h1>Search and Book Flights</h1>
        <p>We're bringing you a new level of comfort</p>
      </div>
    </div>
  </div>

  <!-- Search Form -->
  <div class="search-form-container">
    <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
      
      <!-- Trip Type Tabs -->
      <div class="trip-type-tabs">
        <button type="button" 
                class="tab-button" 
                [class.active]="searchType === 'oneway'"
                (click)="onSearchTypeChange('oneway')">
          One way
        </button>
        <button type="button" 
                class="tab-button" 
                [class.active]="searchType === 'roundtrip'"
                (click)="onSearchTypeChange('roundtrip')">
          Round Trip
        </button>
        <button type="button" 
                class="tab-button" 
                [class.active]="searchType === 'multicity'"
                (click)="onSearchTypeChange('multicity')">
          Multi-City/Stop-Overs
        </button>
      </div>

      <!-- One Way & Round Trip Form -->
      <div *ngIf="searchType !== 'multicity'" class="flight-inputs">
        <div class="input-row">
          <div class="input-group from-to">
            <div class="input-field">
              <label>From</label>
              <input type="text" 
                     formControlName="from" 
                     placeholder="IST - Istanbul Airport"
                     class="location-input">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
            </div>
            
            <div class="swap-button">
              <button type="button" class="btn-swap">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M6.99 11L3 15l3.99 4v-3H14v-2H6.99v-3zM21 9l-3.99-4v3H10v2h7.01v3L21 9z"/>
                </svg>
              </button>
            </div>
            
            <div class="input-field">
              <label>To</label>
              <input type="text" 
                     formControlName="to" 
                     placeholder="TUN - Carthage Arpt"
                     class="location-input">
              <div class="input-icon">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div class="input-row">
          <div class="input-group dates">
            <div class="input-field">
              <label>Departure Date</label>
              <input type="date" 
                     formControlName="departureDate" 
                     class="date-input">
            </div>
            
            <div class="input-field" *ngIf="searchType === 'roundtrip'">
              <label>Return Date</label>
              <input type="date" 
                     formControlName="returnDate" 
                     class="date-input">
            </div>
          </div>
        </div>
      </div>

      <!-- Multi-City Form -->
      <div *ngIf="searchType === 'multicity'" class="multicity-inputs">
        <div *ngFor="let sector of multicitySectors; let i = index" class="sector-row">
          <div class="input-field">
            <label>From</label>
            <input type="text" 
                   [(ngModel)]="sector.from" 
                   [ngModelOptions]="{standalone: true}"
                   placeholder="IST - Istanbul Airport"
                   class="location-input">
          </div>
          
          <div class="input-field">
            <label>To</label>
            <input type="text" 
                   [(ngModel)]="sector.to" 
                   [ngModelOptions]="{standalone: true}"
                   placeholder="TUN - Carthage Arpt"
                   class="location-input">
          </div>
          
          <div class="input-field">
            <label>Date</label>
            <input type="date" 
                   [(ngModel)]="sector.departureDate" 
                   [ngModelOptions]="{standalone: true}"
                   class="date-input">
          </div>
          
          <button type="button" 
                  *ngIf="multicitySectors.length > 2" 
                  class="btn-remove"
                  (click)="removeMulticitySector(i)">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>
        
        <button type="button" class="btn-add-sector" (click)="addMulticitySector()">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
          </svg>
          Add Sector
        </button>
      </div>

      <!-- Passenger & Class Selection -->
      <div class="passenger-class-row">
        <div class="passenger-selector">
          <label>Passengers & Class</label>
          <div class="passenger-controls">
            <div class="passenger-type">
              <span class="passenger-icon">👤</span>
              <span class="passenger-label">Adults</span>
              <div class="counter-buttons">
                <button type="button" (click)="updatePassengerCount('adults', false)">-</button>
                <span class="count">{{ passengerCounts.adults }}</span>
                <button type="button" (click)="updatePassengerCount('adults', true)">+</button>
              </div>
            </div>
            
            <div class="passenger-type">
              <span class="passenger-icon">👶</span>
              <span class="passenger-label">Children</span>
              <div class="counter-buttons">
                <button type="button" (click)="updatePassengerCount('children', false)">-</button>
                <span class="count">{{ passengerCounts.children }}</span>
                <button type="button" (click)="updatePassengerCount('children', true)">+</button>
              </div>
            </div>
            
            <div class="passenger-type">
              <span class="passenger-icon">🍼</span>
              <span class="passenger-label">Infants</span>
              <div class="counter-buttons">
                <button type="button" (click)="updatePassengerCount('infants', false)">-</button>
                <span class="count">{{ passengerCounts.infants }}</span>
                <button type="button" (click)="updatePassengerCount('infants', true)">+</button>
              </div>
            </div>
          </div>
        </div>

        <div class="class-selector">
          <label>Class of Travel</label>
          <select formControlName="classOfTravel" class="class-select">
            <option *ngFor="let classType of classTypes" [value]="classType.value">
              {{ classType.label }}
            </option>
          </select>
        </div>
      </div>

      <!-- Additional Options -->
      <div class="additional-options">
        <div class="option-group">
          <label>Preferred Airline</label>
          <input type="text" 
                 formControlName="preferredAirline" 
                 placeholder="Any airline"
                 class="option-input">
        </div>
        
        <div class="option-group">
          <label>Refundable fares</label>
          <select formControlName="refundableFares" class="option-select">
            <option value="false">All fares</option>
            <option value="true">Refundable only</option>
          </select>
        </div>
        
        <div class="option-group">
          <label>Date flexibility</label>
          <select formControlName="calendar" class="option-select">
            <option value="+/- 3 Days">+/- 3 Days</option>
            <option value="+/- 7 Days">+/- 7 Days</option>
            <option value="Exact dates">Exact dates</option>
          </select>
        </div>
      </div>

      <!-- Search Button -->
      <div class="search-button-container">
        <button type="submit" 
                class="search-button" 
                [disabled]="!isFormValid() || loading">
          <span *ngIf="!loading">SEARCH FLIGHTS</span>
          <span *ngIf="loading" class="loading-text">
            <svg class="spinner" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
            Searching...
          </span>
        </button>
        
        <button type="button" 
                class="clear-button" 
                (click)="clearForm()">
          Clear Form
        </button>
      </div>
    </form>
  </div>

  <!-- Latest Searches Sidebar -->
  <div class="latest-searches">
    <h3>Latest Searches</h3>
    <p>We're bringing you a new level of comfort</p>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>IST</strong> - <strong>TUN</strong> on <strong>Jun 17, 2025</strong></span>
    </div>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>IST</strong> - <strong>TUN</strong> on <strong>Jun 19, 2025</strong></span>
    </div>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>IST</strong> - <strong>TUN</strong> on <strong>Jun 18, 2025</strong></span>
    </div>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>IST</strong> - <strong>TUN</strong> on <strong>Jun 10, 2025</strong></span>
    </div>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>TUN</strong> - <strong>IST</strong> on <strong>Jun 11, 2025</strong></span>
    </div>
    
    <div class="search-item">
      <div class="search-dot"></div>
      <span>Coming From <strong>TUN</strong> - <strong>PHT</strong> on <strong>Jun 1, 2025</strong></span>
    </div>
  </div>
</div>
