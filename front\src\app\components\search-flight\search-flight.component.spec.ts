import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SearchFlightComponent } from './search-flight.component';
import { FlightSearchService } from '../../services/flight-search.service';

describe('SearchFlightComponent', () => {
  let component: SearchFlightComponent;
  let fixture: ComponentFixture<SearchFlightComponent>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockFlightSearchService: jasmine.SpyObj<FlightSearchService>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const flightSearchServiceSpy = jasmine.createSpyObj('FlightSearchService', ['search']);

    await TestBed.configureTestingModule({
      declarations: [SearchFlightComponent],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: FlightSearchService, useValue: flightSearchServiceSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SearchFlightComponent);
    component = fixture.componentInstance;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockFlightSearchService = TestBed.inject(FlightSearchService) as jasmine.SpyObj<FlightSearchService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.searchType).toBe('oneway');
    expect(component.passengerCounts.adults).toBe(1);
    expect(component.passengerCounts.children).toBe(0);
    expect(component.passengerCounts.infants).toBe(0);
  });

  it('should change search type', () => {
    component.onSearchTypeChange('roundtrip');
    expect(component.searchType).toBe('roundtrip');
  });

  it('should update passenger count', () => {
    component.updatePassengerCount('adults', true);
    expect(component.passengerCounts.adults).toBe(2);
    
    component.updatePassengerCount('children', true);
    expect(component.passengerCounts.children).toBe(1);
  });

  it('should not allow adults count to go below 1', () => {
    component.updatePassengerCount('adults', false);
    expect(component.passengerCounts.adults).toBe(1);
  });

  it('should calculate total passengers correctly', () => {
    component.passengerCounts = { adults: 2, children: 1, infants: 1 };
    expect(component.getTotalPassengers()).toBe(4);
  });

  it('should add multicity sector', () => {
    const initialLength = component.multicitySectors.length;
    component.addMulticitySector();
    expect(component.multicitySectors.length).toBe(initialLength + 1);
  });

  it('should remove multicity sector', () => {
    component.addMulticitySector(); // Add one more to have 3 sectors
    const initialLength = component.multicitySectors.length;
    component.removeMulticitySector(0);
    expect(component.multicitySectors.length).toBe(initialLength - 1);
  });

  it('should not remove multicity sector if only 2 remain', () => {
    component.removeMulticitySector(0);
    expect(component.multicitySectors.length).toBe(2);
  });

  it('should clear form', () => {
    component.passengerCounts = { adults: 3, children: 2, infants: 1 };
    component.clearForm();
    
    expect(component.passengerCounts.adults).toBe(1);
    expect(component.passengerCounts.children).toBe(0);
    expect(component.passengerCounts.infants).toBe(0);
    expect(component.multicitySectors.length).toBe(2);
  });

  it('should require return date for round trip', () => {
    component.onSearchTypeChange('roundtrip');
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBeTruthy();
  });

  it('should not require return date for one way', () => {
    component.onSearchTypeChange('oneway');
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBeFalsy();
  });

  it('should navigate to results on valid search', () => {
    // Set up valid form data
    component.searchForm.patchValue({
      from: 'IST',
      to: 'TUN',
      departureDate: '2025-06-17'
    });

    component.onSearch();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/flight-results'], jasmine.any(Object));
  });

  it('should validate return date for round trip', () => {
    component.onSearchTypeChange('roundtrip');
    component.searchForm.patchValue({
      departureDate: '2025-06-17',
      returnDate: '2025-06-16' // Return date before departure
    });

    expect(component.isReturnDateValid()).toBeFalsy();

    component.searchForm.patchValue({
      returnDate: '2025-06-18' // Return date after departure
    });

    expect(component.isReturnDateValid()).toBeTruthy();
  });

  it('should generate passenger text correctly', () => {
    component.passengerCounts = { adults: 2, children: 1, infants: 0 };
    expect(component.getPassengerText()).toBe('2 adultes, 1 enfant');

    component.passengerCounts = { adults: 1, children: 0, infants: 1 };
    expect(component.getPassengerText()).toBe('1 adulte, 1 bébé');
  });
});
