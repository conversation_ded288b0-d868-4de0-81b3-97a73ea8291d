import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { SearchFlightComponent } from './search-flight.component';

describe('SearchFlightComponent', () => {
  let component: SearchFlightComponent;
  let fixture: ComponentFixture<SearchFlightComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SearchFlightComponent],
      imports: [ReactiveFormsModule, FormsModule]
    })
    .compileComponents();

    fixture = TestBed.createComponent(SearchFlightComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.searchType).toBe('oneway');
    expect(component.passengerCounts.adults).toBe(1);
    expect(component.passengerCounts.children).toBe(0);
    expect(component.passengerCounts.infants).toBe(0);
  });

  it('should change search type', () => {
    component.onSearchTypeChange('roundtrip');
    expect(component.searchType).toBe('roundtrip');
  });

  it('should update passenger count', () => {
    component.updatePassengerCount('adults', true);
    expect(component.passengerCounts.adults).toBe(2);
    
    component.updatePassengerCount('children', true);
    expect(component.passengerCounts.children).toBe(1);
  });

  it('should not allow adults count to go below 1', () => {
    component.updatePassengerCount('adults', false);
    expect(component.passengerCounts.adults).toBe(1);
  });

  it('should calculate total passengers correctly', () => {
    component.passengerCounts = { adults: 2, children: 1, infants: 1 };
    expect(component.getTotalPassengers()).toBe(4);
  });

  it('should add multicity sector', () => {
    const initialLength = component.multicitySectors.length;
    component.addMulticitySector();
    expect(component.multicitySectors.length).toBe(initialLength + 1);
  });

  it('should remove multicity sector', () => {
    component.addMulticitySector(); // Add one more to have 3 sectors
    const initialLength = component.multicitySectors.length;
    component.removeMulticitySector(0);
    expect(component.multicitySectors.length).toBe(initialLength - 1);
  });

  it('should not remove multicity sector if only 2 remain', () => {
    component.removeMulticitySector(0);
    expect(component.multicitySectors.length).toBe(2);
  });

  it('should clear form', () => {
    component.passengerCounts = { adults: 3, children: 2, infants: 1 };
    component.clearForm();
    
    expect(component.passengerCounts.adults).toBe(1);
    expect(component.passengerCounts.children).toBe(0);
    expect(component.passengerCounts.infants).toBe(0);
    expect(component.multicitySectors.length).toBe(2);
  });

  it('should require return date for round trip', () => {
    component.onSearchTypeChange('roundtrip');
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBeTruthy();
  });

  it('should not require return date for one way', () => {
    component.onSearchTypeChange('oneway');
    const returnDateControl = component.searchForm.get('returnDate');
    expect(returnDateControl?.hasError('required')).toBeFalsy();
  });
});
