import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  OneWayRequestParams,
  FlightClass
} from '../../models/oneway-request.interface';
import {
  RoundTripRequestParams
} from '../../models/roundtrip-request.interface';
import {
  MulticityRequestParams,
  MulticitySegment
} from '../../models/multicity-request.interface';
import { FlightSearchService } from '../../services/flight-search.service';
import { AutocompleteService, Airport, Airline } from '../../services/autocomplete.service';
import { AutocompleteItem } from '../autocomplete/autocomplete.component';

@Component({
  selector: 'app-search-flight',
  templateUrl: './search-flight.component.html',
  styleUrls: ['./search-flight.component.css']
})
export class SearchFlightComponent implements OnInit {
  searchForm: FormGroup;
  searchType: 'oneway' | 'roundtrip' | 'multicity' = 'oneway';
  passengerCounts = {
    adults: 1,
    children: 0,
    infants: 0
  };
  
  classTypes = [
    { value: FlightClass.ECONOMY, label: 'Economy' },
    { value: FlightClass.PREMIUM_ECONOMY, label: 'Premium Economy' },
    { value: FlightClass.BUSINESS, label: 'Business' },
    { value: FlightClass.FIRST, label: 'First' }
  ];

  multicitySectors: any[] = [
    { from: '', to: '', departureDate: '' },
    { from: '', to: '', departureDate: '' }
  ];

  loading = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private flightSearchService: FlightSearchService,
    private autocompleteService: AutocompleteService
  ) {
    this.searchForm = this.fb.group({
      from: ['', Validators.required],
      to: ['', Validators.required],
      departureDate: ['', Validators.required],
      returnDate: [''],
      classOfTravel: [FlightClass.ECONOMY],
      preferredAirline: [''],
      refundableFares: [false],
      baggage: [''],
      calendar: ['+/- 3 Days']
    });
  }

  ngOnInit(): void {
    this.updateFormValidation();
  }

  onSearchTypeChange(type: 'oneway' | 'roundtrip' | 'multicity'): void {
    this.searchType = type;
    this.updateFormValidation();
  }

  updateFormValidation(): void {
    const returnDateControl = this.searchForm.get('returnDate');
    
    if (this.searchType === 'roundtrip') {
      returnDateControl?.setValidators([Validators.required]);
    } else {
      returnDateControl?.clearValidators();
    }
    
    returnDateControl?.updateValueAndValidity();
  }

  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {
    if (increment) {
      this.passengerCounts[type]++;
    } else if (this.passengerCounts[type] > 0) {
      this.passengerCounts[type]--;
    }
    
    // Ensure at least one adult
    if (type === 'adults' && this.passengerCounts.adults < 1) {
      this.passengerCounts.adults = 1;
    }
  }

  getTotalPassengers(): number {
    return this.passengerCounts.adults + this.passengerCounts.children + this.passengerCounts.infants;
  }

  addMulticitySector(): void {
    this.multicitySectors.push({ from: '', to: '', departureDate: '' });
  }

  removeMulticitySector(index: number): void {
    if (this.multicitySectors.length > 2) {
      this.multicitySectors.splice(index, 1);
    }
  }

  onSearch(): void {
    if (this.searchForm.valid) {
      this.loading = true;
      const formValue = this.searchForm.value;
      
      // Préparer les données de recherche
      const searchData = {
        type: this.searchType,
        params: this.prepareSearchParams(formValue)
      };

      // Naviguer vers la page de résultats avec les données
      this.router.navigate(['/flight-results'], {
        queryParams: {
          searchData: encodeURIComponent(JSON.stringify(searchData))
        }
      });
    }
  }

  private prepareSearchParams(formValue: any): any {
    const baseParams = {
      departureLocation: formValue.from,
      arrivalLocation: formValue.to,
      departureDate: formValue.departureDate,
      passengers: {
        adults: this.passengerCounts.adults,
        children: this.passengerCounts.children,
        infants: this.passengerCounts.infants
      },
      flightClass: formValue.classOfTravel,
      directFlightsOnly: false,
      culture: 'fr-FR',
      currency: 'EUR'
    };

    switch (this.searchType) {
      case 'roundtrip':
        return {
          ...baseParams,
          returnDate: formValue.returnDate
        };
        
      case 'multicity':
        return {
          segments: this.multicitySectors.map(sector => ({
            from: sector.from,
            to: sector.to,
            date: sector.departureDate
          })),
          passengers: baseParams.passengers,
          directFlightsOnly: false,
          culture: 'fr-FR',
          currency: 'EUR'
        };
        
      default: // oneway
        return baseParams;
    }
  }

  clearForm(): void {
    this.searchForm.reset({
      classOfTravel: FlightClass.ECONOMY,
      refundableFares: false,
      calendar: '+/- 3 Days'
    });
    this.passengerCounts = { adults: 1, children: 0, infants: 0 };
    this.multicitySectors = [
      { from: '', to: '', departureDate: '' },
      { from: '', to: '', departureDate: '' }
    ];
  }

  // Méthodes utilitaires pour le template
  getPassengerText(): string {
    const parts: string[] = [];
    
    if (this.passengerCounts.adults > 0) {
      parts.push(`${this.passengerCounts.adults} adulte${this.passengerCounts.adults > 1 ? 's' : ''}`);
    }
    if (this.passengerCounts.children > 0) {
      parts.push(`${this.passengerCounts.children} enfant${this.passengerCounts.children > 1 ? 's' : ''}`);
    }
    if (this.passengerCounts.infants > 0) {
      parts.push(`${this.passengerCounts.infants} bébé${this.passengerCounts.infants > 1 ? 's' : ''}`);
    }
    
    return parts.join(', ');
  }

  getClassLabel(classValue: number): string {
    const classType = this.classTypes.find(c => c.value === classValue);
    return classType ? classType.label : 'Economy';
  }

  // Méthodes pour l'autocomplétion
  searchAirports = (query: string): Observable<AutocompleteItem[]> => {
    return this.autocompleteService.searchAirports(query).pipe(
      map((airports: Airport[]) => airports.map(airport => ({
        code: airport.code,
        name: airport.name,
        displayName: airport.displayName
      })))
    );
  };

  searchAirlines = (query: string): Observable<AutocompleteItem[]> => {
    return this.autocompleteService.searchAirlines(query).pipe(
      map((airlines: Airline[]) => airlines.map(airline => ({
        code: airline.code,
        name: airline.name,
        displayName: airline.displayName
      })))
    );
  };

  onAirportSelected(airport: AutocompleteItem, field: string): void {
    console.log('Aéroport sélectionné:', airport, 'pour le champ:', field);
    // Mettre à jour le formulaire si nécessaire
    if (field === 'from' || field === 'to') {
      this.searchForm.patchValue({ [field]: airport.code });
    }
  }

  onAirlineSelected(airline: AutocompleteItem): void {
    console.log('Compagnie aérienne sélectionnée:', airline);
    this.searchForm.patchValue({ preferredAirline: airline.code });
  }

  // Validation des dates
  isReturnDateValid(): boolean {
    if (this.searchType !== 'roundtrip') return true;
    
    const departureDate = this.searchForm.get('departureDate')?.value;
    const returnDate = this.searchForm.get('returnDate')?.value;
    
    if (!departureDate || !returnDate) return true;
    
    return new Date(returnDate) >= new Date(departureDate);
  }

  // Validation du formulaire
  isFormValid(): boolean {
    if (!this.searchForm.valid) return false;
    
    if (this.searchType === 'roundtrip' && !this.isReturnDateValid()) {
      return false;
    }
    
    if (this.searchType === 'multicity') {
      return this.multicitySectors.every(sector => 
        sector.from && sector.to && sector.departureDate
      );
    }
    
    return true;
  }
}
