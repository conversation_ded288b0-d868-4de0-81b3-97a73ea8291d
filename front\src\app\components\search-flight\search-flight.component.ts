import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OneWayRequest } from '../../models/oneway-request.interface';
import { RoundTripRequest } from '../../models/roundtrip-request.interface';
import { MulticityRequest } from '../../models/multicity-request.interface';

@Component({
  selector: 'app-search-flight',
  templateUrl: './search-flight.component.html',
  styleUrls: ['./search-flight.component.css']
})
export class SearchFlightComponent implements OnInit {
  searchForm: FormGroup;
  searchType: 'oneway' | 'roundtrip' | 'multicity' = 'oneway';
  passengerCounts = {
    adults: 1,
    children: 0,
    infants: 0
  };
  
  classTypes = [
    { value: 'Economy', label: 'Economy' },
    { value: 'Premium Economy', label: 'Premium Economy' },
    { value: 'Business', label: 'Business' },
    { value: 'First', label: 'First' }
  ];

  multicitySectors: any[] = [
    { from: '', to: '', departureDate: '' },
    { from: '', to: '', departureDate: '' }
  ];

  constructor(private fb: FormBuilder) {
    this.searchForm = this.fb.group({
      from: ['', Validators.required],
      to: ['', Validators.required],
      departureDate: ['', Validators.required],
      returnDate: [''],
      classOfTravel: ['Economy'],
      preferredAirline: [''],
      refundableFares: [false],
      baggage: [''],
      calendar: ['+/- 3 Days']
    });
  }

  ngOnInit(): void {
    this.updateFormValidation();
  }

  onSearchTypeChange(type: 'oneway' | 'roundtrip' | 'multicity'): void {
    this.searchType = type;
    this.updateFormValidation();
  }

  updateFormValidation(): void {
    const returnDateControl = this.searchForm.get('returnDate');
    
    if (this.searchType === 'roundtrip') {
      returnDateControl?.setValidators([Validators.required]);
    } else {
      returnDateControl?.clearValidators();
    }
    
    returnDateControl?.updateValueAndValidity();
  }

  updatePassengerCount(type: 'adults' | 'children' | 'infants', increment: boolean): void {
    if (increment) {
      this.passengerCounts[type]++;
    } else if (this.passengerCounts[type] > 0) {
      this.passengerCounts[type]--;
    }
    
    // Ensure at least one adult
    if (type === 'adults' && this.passengerCounts.adults < 1) {
      this.passengerCounts.adults = 1;
    }
  }

  getTotalPassengers(): number {
    return this.passengerCounts.adults + this.passengerCounts.children + this.passengerCounts.infants;
  }

  addMulticitySector(): void {
    this.multicitySectors.push({ from: '', to: '', departureDate: '' });
  }

  removeMulticitySector(index: number): void {
    if (this.multicitySectors.length > 2) {
      this.multicitySectors.splice(index, 1);
    }
  }

  onSearch(): void {
    if (this.searchForm.valid) {
      const formValue = this.searchForm.value;
      
      switch (this.searchType) {
        case 'oneway':
          this.searchOneWay(formValue);
          break;
        case 'roundtrip':
          this.searchRoundTrip(formValue);
          break;
        case 'multicity':
          this.searchMulticity();
          break;
      }
    }
  }

  private searchOneWay(formValue: any): void {
    const request: OneWayRequest = {
      origin: { code: formValue.from },
      destination: { code: formValue.to },
      departureDate: formValue.departureDate,
      passengers: {
        adults: this.passengerCounts.adults,
        children: this.passengerCounts.children,
        infants: this.passengerCounts.infants
      },
      classOfTravel: formValue.classOfTravel,
      additionalParameters: {
        refundableFares: formValue.refundableFares,
        preferredAirline: formValue.preferredAirline || null
      }
    };
    
    console.log('One Way Search:', request);
    // TODO: Call flight search service
  }

  private searchRoundTrip(formValue: any): void {
    const request: RoundTripRequest = {
      origin: { code: formValue.from },
      destination: { code: formValue.to },
      departureDate: formValue.departureDate,
      returnDate: formValue.returnDate,
      passengers: {
        adults: this.passengerCounts.adults,
        children: this.passengerCounts.children,
        infants: this.passengerCounts.infants
      },
      classOfTravel: formValue.classOfTravel,
      additionalParameters: {
        refundableFares: formValue.refundableFares,
        preferredAirline: formValue.preferredAirline || null
      }
    };
    
    console.log('Round Trip Search:', request);
    // TODO: Call flight search service
  }

  private searchMulticity(): void {
    const request: MulticityRequest = {
      sectors: this.multicitySectors.map(sector => ({
        origin: { code: sector.from },
        destination: { code: sector.to },
        departureDate: sector.departureDate
      })),
      passengers: {
        adults: this.passengerCounts.adults,
        children: this.passengerCounts.children,
        infants: this.passengerCounts.infants
      },
      classOfTravel: this.searchForm.value.classOfTravel,
      additionalParameters: {
        refundableFares: this.searchForm.value.refundableFares,
        preferredAirline: this.searchForm.value.preferredAirline || null
      }
    };
    
    console.log('Multi-city Search:', request);
    // TODO: Call flight search service
  }

  clearForm(): void {
    this.searchForm.reset({
      classOfTravel: 'Economy',
      refundableFares: false,
      calendar: '+/- 3 Days'
    });
    this.passengerCounts = { adults: 1, children: 0, infants: 0 };
    this.multicitySectors = [
      { from: '', to: '', departureDate: '' },
      { from: '', to: '', departureDate: '' }
    ];
  }
}
