/**
 * Interfaces TypeScript correspondant exactement aux modèles Java backend
 * Générées à partir des classes OneWayRequest et RoundTripRequest
 */

// ===== INTERFACES COMMUNES =====

/**
 * Interface correspondant à OneWayRequest.Location
 */
export interface Location {
  id: string;
  type: number;
  provider?: number;
}

/**
 * Interface correspondant à OneWayRequest.Passenger
 */
export interface Passenger {
  type: number;
  count: number;
}

/**
 * Interface correspondant à OneWayRequest.CorporateRule
 */
export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

/**
 * Interface correspondant à OneWayRequest.CorporateCode
 */
export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

/**
 * Interface correspondant à OneWayRequest.GetOptionsParameters
 */
export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

/**
 * Interface correspondant à OneWayRequest.AdditionalParameters
 */
export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

// ===== ONE WAY REQUEST =====

/**
 * Interface correspondant exactement à la classe Java OneWayRequest
 * Tous les noms de propriétés correspondent aux @JsonProperty du backend
 */
export interface OneWayRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: number[];
  Culture: string;
  Currency: string;
}

// ===== ROUND TRIP REQUEST =====

/**
 * Interface correspondant exactement à la classe Java RoundTripRequest
 * Tous les noms de propriétés correspondent aux @JsonProperty du backend
 */
export interface RoundTripRequest {
  ProductType: number;
  ServiceTypes: string[];
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  CheckIn: string;
  Night: number;
  Passengers: Passenger[];
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  supportedFlightReponseListTypes: number[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  calculateFlightFees: boolean;
  Culture: string;
  Currency: string;
}

// ===== TYPES D'ÉNUMÉRATION POUR LE FRONTEND =====

/**
 * Types de recherche de vol
 */
export enum FlightSearchType {
  ONE_WAY = 'oneWay',
  ROUND_TRIP = 'roundTrip',
  MULTI_CITY = 'multiCity'
}

/**
 * Types de passagers (correspond aux valeurs utilisées dans le backend)
 */
export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

/**
 * Types de localisation (correspond aux valeurs utilisées dans le backend)
 */
export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

/**
 * Classes de vol (correspond aux valeurs utilisées dans le backend)
 */
export enum FlightClass {
  ECONOMY = 1,
  PREMIUM_ECONOMY = 2,
  BUSINESS = 3,
  FIRST = 4
}

// ===== INTERFACE POUR LE FORMULAIRE FRONTEND =====

/**
 * Interface pour le formulaire de recherche dans le frontend
 * Sera transformée en OneWayRequest ou RoundTripRequest selon le type
 */
export interface FlightSearchForm {
  searchType: FlightSearchType;
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  flightClass: FlightClass;
  directFlightsOnly: boolean;
  preferredAirline?: string;
  culture: string;
  currency: string;
}

// ===== INTERFACES POUR LES RÉPONSES =====

/**
 * Structure de base pour les réponses de l'API
 */
export interface ApiResponse<T> {
  header: {
    requestId: string;
    success: boolean;
    messages: ApiMessage[];
  };
  body: T;
}

/**
 * Messages d'erreur ou d'information de l'API
 */
export interface ApiMessage {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

/**
 * Type pour les réponses de recherche de vols
 * Le body sera défini selon la structure de réponse de votre API
 */
export type FlightSearchResponse = ApiResponse<any>;

// ===== INTERFACES UTILITAIRES =====

/**
 * Interface pour les options de création de requête
 */
export interface RequestOptions {
  productType?: number;
  serviceTypes?: string[];
  acceptPendingProviders?: boolean;
  forceFlightBundlePackage?: boolean;
  disablePackageOfferTotalPrice?: boolean;
  calculateFlightFees?: boolean;
  culture?: string;
  currency?: string;
}

/**
 * Valeurs par défaut pour les requêtes
 */
export const DEFAULT_REQUEST_OPTIONS: RequestOptions = {
  productType: 2, // Type produit pour les vols
  serviceTypes: ['Flight'],
  acceptPendingProviders: true,
  forceFlightBundlePackage: false,
  disablePackageOfferTotalPrice: false,
  calculateFlightFees: true,
  culture: 'fr-FR',
  currency: 'EUR'
};

/**
 * Interface pour les paramètres de recherche simplifiés
 */
export interface SimpleSearchParams {
  from: string;
  to: string;
  departureDate: string;
  returnDate?: string;
  adults: number;
  children?: number;
  infants?: number;
  flightClass?: FlightClass;
  directOnly?: boolean;
}
