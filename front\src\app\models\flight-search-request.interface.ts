/**
 * Interfaces TypeScript correspondant aux modèles de requête de recherche de vols du backend Spring Boot
 */

// ===== INTERFACES COMMUNES =====

export interface Location {
  id: string;
  type: number;
  provider?: number;
}

export interface Passenger {
  type: number;
  count: number;
}

export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

// ===== ONE WAY REQUEST =====

export interface OneWayRequest {
  ProductType: number;
  ServiceTypes: string[];
  CheckIn: string;
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  Passengers: Passenger[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  calculateFlightFees: boolean;
  flightClasses: number[];
  Culture: string;
  Currency: string;
}

// ===== ROUND TRIP REQUEST =====

export interface RoundTripRequest {
  ProductType: number;
  ServiceTypes: string[];
  DepartureLocations: Location[];
  ArrivalLocations: Location[];
  CheckIn: string;
  Night: number;
  Passengers: Passenger[];
  acceptPendingProviders: boolean;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  supportedFlightReponseListTypes: number[];
  showOnlyNonStopFlight: boolean;
  additionalParameters?: AdditionalParameters;
  calculateFlightFees: boolean;
  Culture: string;
  Currency: string;
}

// ===== MULTI-CITY REQUEST =====

export interface MulticityRequest {
  serviceTypes: string[];
  productType: number;
  arrivalLocations: Location[];
  departureLocations: Location[];
  passengers: Passenger[];
  checkIns: string[];
  calculateFlightFees: boolean;
  acceptPendingProviders: boolean;
  additionalParameters?: AdditionalParameters;
  forceFlightBundlePackage: boolean;
  disablePackageOfferTotalPrice: boolean;
  showOnlyNonStopFlight: boolean;
  supportedFlightReponseListTypes: number[];
  culture: string;
  currency: string;
}

// ===== TYPES D'ÉNUMÉRATION =====

export enum FlightSearchType {
  ONE_WAY = 'oneWay',
  ROUND_TRIP = 'roundTrip',
  MULTI_CITY = 'multiCity'
}

export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

export enum FlightClass {
  ECONOMY = 1,
  PREMIUM_ECONOMY = 2,
  BUSINESS = 3,
  FIRST = 4
}

// ===== INTERFACE POUR LE FORMULAIRE =====

export interface FlightSearchForm {
  searchType: FlightSearchType;
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  returnDate?: string;
  passengers: {
    adults: number;
    children: number;
    infants: number;
  };
  flightClass: FlightClass;
  directFlightsOnly: boolean;
  preferredAirline?: string;
  culture: string;
  currency: string;
}

// ===== INTERFACE POUR LES RÉSULTATS =====

export interface FlightSearchResponse {
  header: {
    requestId: string;
    success: boolean;
    messages: Array<{
      id: number;
      code: string;
      messageType: number;
      message: string;
    }>;
  };
  body: any; // À définir selon la structure de réponse de votre API
}
