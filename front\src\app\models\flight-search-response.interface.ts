/**
 * Interfaces TypeScript pour les réponses de recherche de vols
 * À adapter selon la structure exacte de votre API backend
 */

// ===== STRUCTURE DE BASE DES RÉPONSES =====

/**
 * Structure générique pour toutes les réponses de l'API
 */
export interface ApiResponse<T> {
  header: ResponseHeader;
  body: T;
}

/**
 * En-tête de réponse standard
 */
export interface ResponseHeader {
  requestId: string;
  success: boolean;
  messages: ApiMessage[];
  timestamp?: string;
  processingTime?: number;
}

/**
 * Messages d'erreur ou d'information
 */
export interface ApiMessage {
  id: number;
  code: string;
  messageType: MessageType;
  message: string;
  details?: string;
}

/**
 * Types de messages
 */
export enum MessageType {
  INFO = 1,
  WARNING = 2,
  ERROR = 3,
  SUCCESS = 4
}

// ===== RÉPONSES DE RECHERCHE DE VOLS =====

/**
 * Réponse pour la recherche de vols (OneWay et RoundTrip)
 */
export type FlightSearchResponse = ApiResponse<FlightSearchResult>;

/**
 * Résultat de recherche de vols
 * Structure à adapter selon votre API
 */
export interface FlightSearchResult {
  searchId: string;
  flights: Flight[];
  totalResults: number;
  searchCriteria: SearchCriteria;
  filters?: SearchFilters;
  pagination?: PaginationInfo;
}

/**
 * Informations sur un vol
 */
export interface Flight {
  id: string;
  flightNumber: string;
  airline: Airline;
  departure: FlightSegment;
  arrival: FlightSegment;
  duration: string;
  stops: number;
  aircraft?: string;
  price: Price;
  availability: Availability;
  baggage?: BaggageInfo;
  amenities?: string[];
  bookingClass: string;
  fareType: string;
  refundable: boolean;
  changeable: boolean;
}

/**
 * Informations sur une compagnie aérienne
 */
export interface Airline {
  code: string;
  name: string;
  logo?: string;
  alliance?: string;
}

/**
 * Segment de vol (départ ou arrivée)
 */
export interface FlightSegment {
  airport: Airport;
  dateTime: string;
  terminal?: string;
  gate?: string;
}

/**
 * Informations sur un aéroport
 */
export interface Airport {
  code: string;
  name: string;
  city: string;
  country: string;
  timezone: string;
}

/**
 * Informations de prix
 */
export interface Price {
  total: number;
  base: number;
  taxes: number;
  fees: number;
  currency: string;
  breakdown?: PriceBreakdown[];
}

/**
 * Détail des prix par passager
 */
export interface PriceBreakdown {
  passengerType: string;
  count: number;
  basePrice: number;
  taxes: number;
  total: number;
}

/**
 * Disponibilité des places
 */
export interface Availability {
  seats: number;
  bookingClass: string;
  cabinClass: string;
}

/**
 * Informations sur les bagages
 */
export interface BaggageInfo {
  carryOn: BaggageAllowance;
  checked: BaggageAllowance;
}

/**
 * Franchise de bagages
 */
export interface BaggageAllowance {
  pieces: number;
  weight: number;
  dimensions?: string;
  unit: 'kg' | 'lbs';
}

/**
 * Critères de recherche utilisés
 */
export interface SearchCriteria {
  searchType: string;
  departure: string;
  arrival: string;
  departureDate: string;
  returnDate?: string;
  passengers: PassengerCount;
  class: string;
  directOnly: boolean;
}

/**
 * Nombre de passagers
 */
export interface PassengerCount {
  adults: number;
  children: number;
  infants: number;
}

/**
 * Filtres disponibles pour affiner la recherche
 */
export interface SearchFilters {
  airlines: FilterOption[];
  priceRange: PriceRange;
  departureTime: TimeRange[];
  arrivalTime: TimeRange[];
  duration: DurationRange;
  stops: StopOption[];
  airports: FilterOption[];
}

/**
 * Option de filtre générique
 */
export interface FilterOption {
  code: string;
  name: string;
  count: number;
  selected: boolean;
}

/**
 * Plage de prix
 */
export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

/**
 * Plage horaire
 */
export interface TimeRange {
  label: string;
  start: string;
  end: string;
  count: number;
}

/**
 * Plage de durée
 */
export interface DurationRange {
  min: number; // en minutes
  max: number; // en minutes
}

/**
 * Options d'escales
 */
export interface StopOption {
  stops: number;
  label: string;
  count: number;
}

/**
 * Informations de pagination
 */
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// ===== RÉPONSES D'ERREUR =====

/**
 * Réponse en cas d'erreur
 */
export interface ErrorResponse {
  header: ResponseHeader;
  error: ErrorDetails;
}

/**
 * Détails de l'erreur
 */
export interface ErrorDetails {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
  path?: string;
}

// ===== TYPES UTILITAIRES =====

/**
 * État de la recherche
 */
export enum SearchStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

/**
 * Types de tri disponibles
 */
export enum SortType {
  PRICE_ASC = 'price_asc',
  PRICE_DESC = 'price_desc',
  DURATION_ASC = 'duration_asc',
  DURATION_DESC = 'duration_desc',
  DEPARTURE_TIME_ASC = 'departure_asc',
  DEPARTURE_TIME_DESC = 'departure_desc',
  ARRIVAL_TIME_ASC = 'arrival_asc',
  ARRIVAL_TIME_DESC = 'arrival_desc'
}

/**
 * Paramètres de tri et filtrage
 */
export interface SearchParams {
  sortBy?: SortType;
  filters?: {
    airlines?: string[];
    maxPrice?: number;
    minPrice?: number;
    maxStops?: number;
    departureTimeRange?: TimeRange;
    arrivalTimeRange?: TimeRange;
  };
  pagination?: {
    page: number;
    size: number;
  };
}
