/**
 * Interface TypeScript correspondant exactement à la classe Java MulticityRequest
 * Générée à partir du modèle backend com.paximum.demo.models.MulticityRequest
 */

// ===== INTERFACES COMMUNES =====

/**
 * Interface correspondant à MulticityRequest.Location
 * Note: Plus simple que OneWay/RoundTrip (pas de provider)
 */
export interface Location {
  id: string;
  type: number;
}

/**
 * Interface correspondant à MulticityRequest.Passenger
 */
export interface Passenger {
  type: number;
  count: number;
}

/**
 * Interface correspondant à MulticityRequest.CorporateRule
 */
export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

/**
 * Interface correspondant à MulticityRequest.CorporateCode
 */
export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

/**
 * Interface correspondant à MulticityRequest.AdditionalParameters
 * Note: Plus simple que OneWay/RoundTrip (pas de getOptionsParameters)
 */
export interface AdditionalParameters {
  CorporateCodes?: CorporateCode[];
}

// ===== MULTI-CITY REQUEST =====

/**
 * Interface correspondant exactement à la classe Java MulticityRequest
 * Tous les noms de propriétés correspondent aux @JsonProperty du backend
 * 
 * @example
 * ```typescript
 * const multicityRequest: MulticityRequest = {
 *   serviceTypes: ['Flight'],
 *   productType: 2,
 *   arrivalLocations: [
 *     { id: 'TUN', type: 1 },
 *     { id: 'IST', type: 1 },
 *     { id: 'PAR', type: 1 }
 *   ],
 *   departureLocations: [
 *     { id: 'IST', type: 1 },
 *     { id: 'TUN', type: 1 },
 *     { id: 'IST', type: 1 }
 *   ],
 *   passengers: [{ type: 1, count: 1 }],
 *   checkIns: ['2024-06-15', '2024-06-20', '2024-06-25'],
 *   calculateFlightFees: true,
 *   acceptPendingProviders: true,
 *   forceFlightBundlePackage: false,
 *   disablePackageOfferTotalPrice: false,
 *   showOnlyNonStopFlight: false,
 *   supportedFlightReponseListTypes: [1, 2],
 *   culture: 'fr-FR',
 *   currency: 'EUR'
 * };
 * ```
 */
export interface MulticityRequest {
  /**
   * Types de services (ex: ['Flight'])
   */
  serviceTypes: string[];

  /**
   * Type de produit (généralement 2 pour les vols)
   */
  productType: number;

  /**
   * Liste des lieux d'arrivée pour chaque segment
   */
  arrivalLocations: Location[];

  /**
   * Liste des lieux de départ pour chaque segment
   */
  departureLocations: Location[];

  /**
   * Liste des passagers avec leur type et nombre
   */
  passengers: Passenger[];

  /**
   * Liste des dates de départ pour chaque segment au format ISO (YYYY-MM-DD)
   */
  checkIns: string[];

  /**
   * Calculer les frais de vol
   */
  calculateFlightFees: boolean;

  /**
   * Accepter les fournisseurs en attente
   */
  acceptPendingProviders: boolean;

  /**
   * Paramètres additionnels optionnels
   */
  additionalParameters?: AdditionalParameters;

  /**
   * Forcer le package de vol groupé
   */
  forceFlightBundlePackage: boolean;

  /**
   * Désactiver le prix total de l'offre package
   */
  disablePackageOfferTotalPrice: boolean;

  /**
   * Afficher uniquement les vols sans escale
   */
  showOnlyNonStopFlight: boolean;

  /**
   * Types de réponse de vol supportés
   */
  supportedFlightReponseListTypes: number[];

  /**
   * Culture/Langue (ex: 'fr-FR', 'en-US')
   */
  culture: string;

  /**
   * Devise (ex: 'EUR', 'USD')
   */
  currency: string;
}

// ===== TYPES D'ÉNUMÉRATION =====

/**
 * Types de passagers selon le backend
 */
export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

/**
 * Types de localisation selon le backend
 */
export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

/**
 * Types de produit selon le backend
 */
export enum ProductType {
  FLIGHT = 2
}

/**
 * Types de réponse de vol supportés
 */
export enum FlightResponseListType {
  STANDARD = 1,
  DETAILED = 2
}

// ===== INTERFACES UTILITAIRES =====

/**
 * Interface pour créer facilement une Location MultiCity
 */
export interface LocationBuilder {
  id: string;
  type?: LocationType;
}

/**
 * Interface pour créer facilement un Passenger
 */
export interface PassengerBuilder {
  type: PassengerType;
  count: number;
}

/**
 * Interface pour un segment de voyage MultiCity
 */
export interface MulticitySegment {
  from: string;
  to: string;
  date: string;
}

/**
 * Interface pour les paramètres de base d'une requête MultiCity
 */
export interface MulticityRequestParams {
  segments: MulticitySegment[];
  passengers: {
    adults: number;
    children?: number;
    infants?: number;
  };
  directFlightsOnly?: boolean;
  culture?: string;
  currency?: string;
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Crée une Location à partir de paramètres simplifiés
 */
export function createLocation(params: LocationBuilder): Location {
  return {
    id: params.id,
    type: params.type || LocationType.AIRPORT
  };
}

/**
 * Crée un Passenger à partir de paramètres simplifiés
 */
export function createPassenger(params: PassengerBuilder): Passenger {
  return {
    type: params.type,
    count: params.count
  };
}

/**
 * Crée une liste de passagers à partir de paramètres simplifiés
 */
export function createPassengerList(passengers: { adults: number; children?: number; infants?: number }): Passenger[] {
  const passengerList: Passenger[] = [];
  
  if (passengers.adults > 0) {
    passengerList.push(createPassenger({ type: PassengerType.ADULT, count: passengers.adults }));
  }
  
  if (passengers.children && passengers.children > 0) {
    passengerList.push(createPassenger({ type: PassengerType.CHILD, count: passengers.children }));
  }
  
  if (passengers.infants && passengers.infants > 0) {
    passengerList.push(createPassenger({ type: PassengerType.INFANT, count: passengers.infants }));
  }
  
  return passengerList;
}

/**
 * Valeurs par défaut pour une requête MultiCity
 */
export const DEFAULT_MULTICITY_REQUEST: Partial<MulticityRequest> = {
  serviceTypes: ['Flight'],
  productType: ProductType.FLIGHT,
  calculateFlightFees: true,
  acceptPendingProviders: true,
  forceFlightBundlePackage: false,
  disablePackageOfferTotalPrice: false,
  showOnlyNonStopFlight: false,
  supportedFlightReponseListTypes: [FlightResponseListType.STANDARD, FlightResponseListType.DETAILED],
  culture: 'fr-FR',
  currency: 'EUR'
};

/**
 * Crée une requête MultiCity complète à partir de paramètres simplifiés
 */
export function createMulticityRequest(params: MulticityRequestParams): MulticityRequest {
  const departureLocations = params.segments.map(segment => createLocation({ id: segment.from }));
  const arrivalLocations = params.segments.map(segment => createLocation({ id: segment.to }));
  const checkIns = params.segments.map(segment => segment.date);
  
  return {
    ...DEFAULT_MULTICITY_REQUEST,
    arrivalLocations,
    departureLocations,
    passengers: createPassengerList(params.passengers),
    checkIns,
    showOnlyNonStopFlight: params.directFlightsOnly || false,
    culture: params.culture || 'fr-FR',
    currency: params.currency || 'EUR'
  } as MulticityRequest;
}

/**
 * Valide une requête MultiCity
 */
export function validateMulticityRequest(request: MulticityRequest): string[] {
  const errors: string[] = [];
  
  if (!request.checkIns || request.checkIns.length === 0) {
    errors.push('At least one check-in date is required');
  }
  
  if (!request.departureLocations || request.departureLocations.length === 0) {
    errors.push('At least one departure location is required');
  }
  
  if (!request.arrivalLocations || request.arrivalLocations.length === 0) {
    errors.push('At least one arrival location is required');
  }
  
  // Vérifier que les tableaux ont la même longueur
  if (request.departureLocations && request.arrivalLocations && request.checkIns) {
    const depLength = request.departureLocations.length;
    const arrLength = request.arrivalLocations.length;
    const dateLength = request.checkIns.length;
    
    if (depLength !== arrLength || depLength !== dateLength) {
      errors.push('Departure locations, arrival locations, and check-in dates must have the same length');
    }
  }
  
  if (!request.passengers || request.passengers.length === 0) {
    errors.push('At least one passenger is required');
  }
  
  if (!request.culture) {
    errors.push('Culture is required');
  }
  
  if (!request.currency) {
    errors.push('Currency is required');
  }
  
  // Vérifier que les dates sont dans l'ordre chronologique
  if (request.checkIns && request.checkIns.length > 1) {
    for (let i = 1; i < request.checkIns.length; i++) {
      const prevDate = new Date(request.checkIns[i - 1]);
      const currentDate = new Date(request.checkIns[i]);
      
      if (currentDate <= prevDate) {
        errors.push(`Check-in date ${i + 1} must be after check-in date ${i}`);
      }
    }
  }
  
  return errors;
}

/**
 * Crée un exemple de requête MultiCity pour les tests
 */
export function createSampleMulticityRequest(): MulticityRequest {
  return createMulticityRequest({
    segments: [
      { from: 'IST', to: 'TUN', date: '2024-06-15' },
      { from: 'TUN', to: 'PAR', date: '2024-06-20' },
      { from: 'PAR', to: 'IST', date: '2024-06-25' }
    ],
    passengers: {
      adults: 1,
      children: 0,
      infants: 0
    }
  });
}

/**
 * Convertit une requête MultiCity en segments lisibles
 */
export function getReadableSegments(request: MulticityRequest): string[] {
  const segments: string[] = [];
  
  for (let i = 0; i < request.departureLocations.length; i++) {
    const from = request.departureLocations[i]?.id || 'Unknown';
    const to = request.arrivalLocations[i]?.id || 'Unknown';
    const date = request.checkIns[i] || 'Unknown';
    
    segments.push(`${from} → ${to} on ${date}`);
  }
  
  return segments;
}
