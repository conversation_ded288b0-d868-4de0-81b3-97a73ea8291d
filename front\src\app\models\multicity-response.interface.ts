/**
 * Interfaces TypeScript correspondant exactement à la classe Java MulticityResponse
 * Générées à partir du modèle backend com.paximum.demo.models.MulticityResponse
 */

// ===== STRUCTURE PRINCIPALE =====

/**
 * Interface correspondant exactement à la classe Java MulticityResponse
 */
export interface MulticityResponse {
  header: Header;
  body: Body;
}

/**
 * Interface correspondant à MulticityResponse.Header
 */
export interface Header {
  requestId: string;
  success: boolean;
  responseTime: string; // ZonedDateTime en string ISO
  messages: Message[];
}

/**
 * Interface correspondant à MulticityResponse.Message
 */
export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

/**
 * Interface correspondant à MulticityResponse.Body
 */
export interface Body {
  searchId: string;
  expiresOn: string; // ZonedDateTime en string ISO
  flights: Flight[];
}

// ===== STRUCTURES DE VOL =====

/**
 * Interface correspondant à MulticityResponse.Flight
 */
export interface Flight {
  provider: number;
  id: string;
  items: FlightItem[];
  offers: Offer[];
  groupKeys: string[];
}

/**
 * Interface correspondant à MulticityResponse.FlightItem
 */
export interface FlightItem {
  segmentNumber: number;
  flightNo: string;
  pnlName: string;
  flightDate: string; // LocalDateTime en string ISO
  airline: Airline;
  marketingAirline: Airline;
  duration: number; // en minutes
  dayChange: number;
  departure: LocationInfo;
  arrival: LocationInfo;
  flightClass: FlightClass;
  route: number;
  segments: Segment[];
  stopCount: number;
  flightProvider: Provider;
  baggageInformations: BaggageInformation[];
  passengers: PassengerInfo[];
  flightType: number;
}

/**
 * Interface correspondant à MulticityResponse.Airline
 */
export interface Airline {
  internationalCode: string;
  thumbnail: string;
  thumbnailFull: string;
  logo: string;
  logoFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à MulticityResponse.LocationInfo
 */
export interface LocationInfo {
  country: Region;
  city: Region;
  airport: Airport;
  date: string; // LocalDateTime en string ISO
  geoLocation: GeoLocation;
}

/**
 * Interface correspondant à MulticityResponse.Region
 */
export interface Region {
  name: string;
  provider: number;
  isTopRegion: boolean;
  id: string;
}

/**
 * Interface correspondant à MulticityResponse.Airport
 */
export interface Airport {
  geolocation: GeoLocation;
  name: string;
  id: string;
  code: string;
}

/**
 * Interface correspondant à MulticityResponse.GeoLocation
 */
export interface GeoLocation {
  longitude: string;
  latitude: string;
}

/**
 * Interface correspondant à MulticityResponse.FlightClass
 */
export interface FlightClass {
  type?: number;
  name: string;
  id: string;
  code: string;
}

/**
 * Interface correspondant à MulticityResponse.Segment
 */
export interface Segment {
  id: string;
  flightNo: string;
  pnlName: string;
  flightClass: FlightClass;
  airline: Airline;
  marketingAirline: Airline;
  departure: LocationInfo;
  arrival: LocationInfo;
  duration: number; // en minutes
  baggageInformations: BaggageInformation[];
  services: Service[];
  aircraft: string;
}

/**
 * Interface correspondant à MulticityResponse.BaggageInformation
 */
export interface BaggageInformation {
  segmentId: string;
  weight?: number;
  piece: number;
  baggageType: number;
  unitType: number;
  passengerType: number;
  description: string;
}

/**
 * Interface correspondant à MulticityResponse.Service
 */
export interface Service {
  segments: string[];
  explanations: Explanation[];
  thumbnail: string;
  thumbnailFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à MulticityResponse.Explanation
 */
export interface Explanation {
  text: string;
}

/**
 * Interface correspondant à MulticityResponse.PassengerInfo
 */
export interface PassengerInfo {
  type: number;
  count: number;
}

/**
 * Interface correspondant à MulticityResponse.Provider
 */
export interface Provider {
  displayName: string;
  id: string;
  name: string;
}

// ===== STRUCTURES D'OFFRES ET PRIX =====

/**
 * Interface correspondant à MulticityResponse.Offer
 */
export interface Offer {
  segmentNumber: number;
  singleAdultPrice: Price;
  priceBreakDown: PriceBreakDown;
  serviceFee: Price;
  seatInfo: SeatInfo;
  flightClassInformations: FlightClassInfo[];
  baggageInformations: BaggageInformation[];
  services: Service[];
  reservableInfo: ReservableInfo;
  groupKeys: string[];
  offerIds: OfferId[];
  isPackageOffer: boolean;
  hasBrand: boolean;
  route: number;
  flightProvider: Provider;
  flightBrandInfo: FlightBrandInfo;
  expiresOn: string; // ZonedDateTime en string ISO
  price: Price;
  provider: number;
}

/**
 * Interface correspondant à MulticityResponse.Price
 */
export interface Price {
  amount: number;
  currency: string;
}

/**
 * Interface correspondant à MulticityResponse.PriceBreakDown
 */
export interface PriceBreakDown {
  items: PriceBreakDownItem[];
}

/**
 * Interface correspondant à MulticityResponse.PriceBreakDownItem
 */
export interface PriceBreakDownItem {
  passengerType: number;
  passengerCount: number;
  price: Price;
  airportTax: Price;
}

/**
 * Interface correspondant à MulticityResponse.SeatInfo
 */
export interface SeatInfo {
  availableSeatCount: number;
  availableSeatCountType: number;
}

/**
 * Interface correspondant à MulticityResponse.FlightClassInfo
 */
export interface FlightClassInfo {
  type: number;
  segmentId: string;
  name: string;
  id: string;
  code: string;
}

/**
 * Interface correspondant à MulticityResponse.ReservableInfo
 */
export interface ReservableInfo {
  reservable: boolean;
  optionDate: string;
}

/**
 * Interface correspondant à MulticityResponse.OfferId
 */
export interface OfferId {
  groupKey: string;
  offerId: string;
  provider: Provider;
}

/**
 * Interface correspondant à MulticityResponse.FlightBrandInfo
 */
export interface FlightBrandInfo {
  id: string;
  name: string;
}

// ===== TYPES UTILITAIRES =====

/**
 * État de la recherche MultiCity
 */
export enum MulticitySearchStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

/**
 * Types de tri disponibles pour MultiCity
 */
export enum MulticitySortType {
  PRICE_ASC = 'price_asc',
  PRICE_DESC = 'price_desc',
  DURATION_ASC = 'duration_asc',
  DURATION_DESC = 'duration_desc',
  DEPARTURE_TIME_ASC = 'departure_asc',
  DEPARTURE_TIME_DESC = 'departure_desc',
  STOPS_ASC = 'stops_asc',
  SEGMENT_ASC = 'segment_asc'
}

/**
 * Paramètres de tri et filtrage pour MultiCity
 */
export interface MulticitySearchParams {
  sortBy?: MulticitySortType;
  filters?: {
    airlines?: string[];
    maxPrice?: number;
    minPrice?: number;
    maxStops?: number;
    segmentNumber?: number;
    maxDuration?: number; // en minutes
  };
  pagination?: {
    page: number;
    size: number;
  };
}

/**
 * Résumé de recherche MultiCity
 */
export interface MulticitySearchSummary {
  totalFlights: number;
  totalSegments: number;
  cheapestPrice: number;
  fastestDuration: string;
  directFlights: number;
  airlinesCount: number;
  currency: string;
  segmentSummaries: SegmentSummary[];
}

/**
 * Résumé par segment
 */
export interface SegmentSummary {
  segmentNumber: number;
  route: string;
  date: string;
  flightCount: number;
  cheapestPrice: number;
  fastestDuration: string;
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Extrait les segments uniques d'une réponse MultiCity
 */
export function extractSegments(response: MulticityResponse): SegmentSummary[] {
  const segmentMap = new Map<number, SegmentSummary>();
  
  response.body.flights.forEach(flight => {
    flight.items.forEach(item => {
      const segmentNumber = item.segmentNumber;
      const route = `${item.departure.airport.code} → ${item.arrival.airport.code}`;
      const date = item.flightDate.split('T')[0]; // Extraire la date
      
      if (!segmentMap.has(segmentNumber)) {
        segmentMap.set(segmentNumber, {
          segmentNumber,
          route,
          date,
          flightCount: 0,
          cheapestPrice: Infinity,
          fastestDuration: ''
        });
      }
      
      const segment = segmentMap.get(segmentNumber)!;
      segment.flightCount++;
      
      // Trouver le prix le moins cher pour ce segment
      flight.offers.forEach(offer => {
        if (offer.segmentNumber === segmentNumber && offer.price.amount < segment.cheapestPrice) {
          segment.cheapestPrice = offer.price.amount;
        }
      });
      
      // Trouver la durée la plus courte
      if (!segment.fastestDuration || item.duration < parseInt(segment.fastestDuration)) {
        const hours = Math.floor(item.duration / 60);
        const minutes = item.duration % 60;
        segment.fastestDuration = `${hours}h ${minutes}m`;
      }
    });
  });
  
  return Array.from(segmentMap.values()).sort((a, b) => a.segmentNumber - b.segmentNumber);
}

/**
 * Calcule le prix total d'un itinéraire MultiCity
 */
export function calculateTotalPrice(flight: Flight): number {
  return flight.offers.reduce((total, offer) => total + offer.price.amount, 0);
}

/**
 * Formate la durée totale d'un itinéraire MultiCity
 */
export function formatTotalDuration(flight: Flight): string {
  const totalMinutes = flight.items.reduce((total, item) => total + item.duration, 0);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours}h ${minutes}m`;
}

/**
 * Vérifie si un itinéraire MultiCity a des vols directs uniquement
 */
export function hasOnlyDirectFlights(flight: Flight): boolean {
  return flight.items.every(item => item.stopCount === 0);
}
