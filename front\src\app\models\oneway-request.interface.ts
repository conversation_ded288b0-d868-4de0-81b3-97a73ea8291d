/**
 * Interface TypeScript correspondant exactement à la classe Java OneWayRequest
 * Générée à partir du modèle backend com.paximum.demo.models.OneWayRequest
 */

// ===== INTERFACES COMMUNES =====

/**
 * Interface correspondant à OneWayRequest.Location
 */
export interface Location {
  id: string;
  type: number;
  provider?: number;
}

/**
 * Interface correspondant à OneWayRequest.Passenger
 */
export interface Passenger {
  type: number;
  count: number;
}

/**
 * Interface correspondant à OneWayRequest.CorporateRule
 */
export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

/**
 * Interface correspondant à OneWayRequest.CorporateCode
 */
export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

/**
 * Interface correspondant à OneWayRequest.GetOptionsParameters
 */
export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

/**
 * Interface correspondant à OneWayRequest.AdditionalParameters
 */
export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

// ===== ONE WAY REQUEST =====

/**
 * Interface correspondant exactement à la classe Java OneWayRequest
 * Tous les noms de propriétés correspondent aux @JsonProperty du backend
 * 
 * @example
 * ```typescript
 * const oneWayRequest: OneWayRequest = {
 *   ProductType: 2,
 *   ServiceTypes: ['Flight'],
 *   CheckIn: '2024-06-15',
 *   DepartureLocations: [{ id: 'IST', type: 1, provider: 1 }],
 *   ArrivalLocations: [{ id: 'TUN', type: 1, provider: 1 }],
 *   Passengers: [{ type: 1, count: 1 }],
 *   showOnlyNonStopFlight: false,
 *   acceptPendingProviders: true,
 *   forceFlightBundlePackage: false,
 *   disablePackageOfferTotalPrice: false,
 *   calculateFlightFees: true,
 *   flightClasses: [1],
 *   Culture: 'fr-FR',
 *   Currency: 'EUR'
 * };
 * ```
 */
export interface OneWayRequest {
  /**
   * Type de produit (généralement 2 pour les vols)
   */
  ProductType: number;

  /**
   * Types de services (ex: ['Flight'])
   */
  ServiceTypes: string[];

  /**
   * Date de départ au format ISO (YYYY-MM-DD)
   */
  CheckIn: string;

  /**
   * Liste des lieux de départ
   */
  DepartureLocations: Location[];

  /**
   * Liste des lieux d'arrivée
   */
  ArrivalLocations: Location[];

  /**
   * Liste des passagers avec leur type et nombre
   */
  Passengers: Passenger[];

  /**
   * Afficher uniquement les vols sans escale
   */
  showOnlyNonStopFlight: boolean;

  /**
   * Paramètres additionnels optionnels
   */
  additionalParameters?: AdditionalParameters;

  /**
   * Accepter les fournisseurs en attente
   */
  acceptPendingProviders: boolean;

  /**
   * Forcer le package de vol groupé
   */
  forceFlightBundlePackage: boolean;

  /**
   * Désactiver le prix total de l'offre package
   */
  disablePackageOfferTotalPrice: boolean;

  /**
   * Calculer les frais de vol
   */
  calculateFlightFees: boolean;

  /**
   * Classes de vol (1: Economy, 2: Premium Economy, 3: Business, 4: First)
   */
  flightClasses: number[];

  /**
   * Culture/Langue (ex: 'fr-FR', 'en-US')
   */
  Culture: string;

  /**
   * Devise (ex: 'EUR', 'USD')
   */
  Currency: string;
}

// ===== TYPES D'ÉNUMÉRATION =====

/**
 * Types de passagers selon le backend
 */
export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

/**
 * Types de localisation selon le backend
 */
export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

/**
 * Classes de vol selon le backend
 */
export enum FlightClass {
  ECONOMY = 1,
  PREMIUM_ECONOMY = 2,
  BUSINESS = 3,
  FIRST = 4
}

/**
 * Types de produit selon le backend
 */
export enum ProductType {
  FLIGHT = 2
}

// ===== INTERFACES UTILITAIRES =====

/**
 * Interface pour créer facilement une Location
 */
export interface LocationBuilder {
  id: string;
  type?: LocationType;
  provider?: number;
}

/**
 * Interface pour créer facilement un Passenger
 */
export interface PassengerBuilder {
  type: PassengerType;
  count: number;
}

/**
 * Interface pour les paramètres de base d'une requête OneWay
 */
export interface OneWayRequestParams {
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  passengers: {
    adults: number;
    children?: number;
    infants?: number;
  };
  flightClass?: FlightClass;
  directFlightsOnly?: boolean;
  culture?: string;
  currency?: string;
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Crée une Location à partir de paramètres simplifiés
 */
export function createLocation(params: LocationBuilder): Location {
  return {
    id: params.id.toUpperCase(), // Convertir en majuscules pour les codes IATA
    type: params.type || LocationType.AIRPORT,
    provider: params.provider || 1
  };
}

/**
 * Crée un Passenger à partir de paramètres simplifiés
 */
export function createPassenger(params: PassengerBuilder): Passenger {
  return {
    type: params.type,
    count: params.count
  };
}

/**
 * Crée une liste de passagers à partir de paramètres simplifiés
 */
export function createPassengerList(passengers: { adults: number; children?: number; infants?: number }): Passenger[] {
  const passengerList: Passenger[] = [];
  
  if (passengers.adults > 0) {
    passengerList.push(createPassenger({ type: PassengerType.ADULT, count: passengers.adults }));
  }
  
  if (passengers.children && passengers.children > 0) {
    passengerList.push(createPassenger({ type: PassengerType.CHILD, count: passengers.children }));
  }
  
  if (passengers.infants && passengers.infants > 0) {
    passengerList.push(createPassenger({ type: PassengerType.INFANT, count: passengers.infants }));
  }
  
  return passengerList;
}

/**
 * Valeurs par défaut pour une requête OneWay
 */
export const DEFAULT_ONEWAY_REQUEST: Partial<OneWayRequest> = {
  ProductType: ProductType.FLIGHT,
  ServiceTypes: ['Flight'],
  showOnlyNonStopFlight: false,
  acceptPendingProviders: true,
  forceFlightBundlePackage: false,
  disablePackageOfferTotalPrice: false,
  calculateFlightFees: true,
  flightClasses: [FlightClass.ECONOMY],
  Culture: 'fr-FR',
  Currency: 'EUR'
};

/**
 * Crée une requête OneWay complète à partir de paramètres simplifiés
 */
export function createOneWayRequest(params: OneWayRequestParams): OneWayRequest {
  return {
    ...DEFAULT_ONEWAY_REQUEST,
    CheckIn: params.departureDate,
    DepartureLocations: [createLocation({ id: params.departureLocation })],
    ArrivalLocations: [createLocation({ id: params.arrivalLocation })],
    Passengers: createPassengerList(params.passengers),
    showOnlyNonStopFlight: params.directFlightsOnly || false,
    flightClasses: params.flightClass ? [params.flightClass] : [FlightClass.ECONOMY],
    Culture: params.culture || 'fr-FR',
    Currency: params.currency || 'EUR'
  } as OneWayRequest;
}
