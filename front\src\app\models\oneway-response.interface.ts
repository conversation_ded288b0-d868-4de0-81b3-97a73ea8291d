/**
 * Interfaces TypeScript pour les réponses de recherche OneWay
 * À adapter selon la structure exacte de votre API backend
 */

// ===== STRUCTURE DE BASE DES RÉPONSES =====

/**
 * Structure générique pour toutes les réponses de l'API
 */
export interface ApiResponse<T> {
  header: ResponseHeader;
  body: T;
}

/**
 * En-tête de réponse standard
 */
export interface ResponseHeader {
  requestId: string;
  success: boolean;
  messages: ApiMessage[];
  timestamp?: string;
  processingTime?: number;
}

/**
 * Messages d'erreur ou d'information
 */
export interface ApiMessage {
  id: number;
  code: string;
  messageType: MessageType;
  message: string;
  details?: string;
}

/**
 * Types de messages
 */
export enum MessageType {
  INFO = 1,
  WARNING = 2,
  ERROR = 3,
  SUCCESS = 4
}

// ===== RÉPONSE ONEWAY =====

/**
 * Réponse pour la recherche de vols OneWay
 */
export type OneWayResponse = ApiResponse<OneWaySearchResult>;

/**
 * Résultat de recherche OneWay
 */
export interface OneWaySearchResult {
  searchId: string;
  flights: OneWayFlight[];
  totalResults: number;
  searchCriteria: OneWaySearchCriteria;
  filters?: SearchFilters;
  pagination?: PaginationInfo;
  processingTime?: number;
}

/**
 * Informations sur un vol OneWay
 */
export interface OneWayFlight {
  id: string;
  flightNumber: string;
  airline: Airline;
  departure: FlightSegment;
  arrival: FlightSegment;
  duration: string; // Format: "2h 30m"
  stops: number;
  stopDetails?: StopDetail[];
  aircraft?: string;
  price: FlightPrice;
  availability: Availability;
  baggage?: BaggageInfo;
  amenities?: string[];
  bookingClass: string;
  fareType: string;
  refundable: boolean;
  changeable: boolean;
  validatingCarrier?: string;
  operatingCarrier?: string;
}

/**
 * Informations sur une compagnie aérienne
 */
export interface Airline {
  code: string; // Code IATA (ex: "TK")
  name: string; // Nom complet (ex: "Turkish Airlines")
  logo?: string; // URL du logo
  alliance?: string; // Alliance (ex: "Star Alliance")
}

/**
 * Segment de vol (départ ou arrivée)
 */
export interface FlightSegment {
  airport: Airport;
  dateTime: string; // Format ISO: "2024-06-15T10:30:00"
  terminal?: string;
  gate?: string;
  checkInTime?: string; // Heure limite d'enregistrement
}

/**
 * Informations sur un aéroport
 */
export interface Airport {
  code: string; // Code IATA (ex: "IST")
  name: string; // Nom complet (ex: "Istanbul Airport")
  city: string; // Ville (ex: "Istanbul")
  country: string; // Pays (ex: "Turkey")
  timezone: string; // Fuseau horaire (ex: "Europe/Istanbul")
}

/**
 * Détails d'une escale
 */
export interface StopDetail {
  airport: Airport;
  duration: string; // Durée de l'escale (ex: "1h 45m")
  overnight: boolean; // Escale de nuit
}

/**
 * Informations de prix pour OneWay
 */
export interface FlightPrice {
  total: number; // Prix total
  base: number; // Prix de base
  taxes: number; // Taxes
  fees: number; // Frais
  currency: string; // Devise (ex: "EUR")
  breakdown?: PriceBreakdown[]; // Détail par passager
  pricePerPassenger?: number; // Prix par passager
}

/**
 * Détail des prix par type de passager
 */
export interface PriceBreakdown {
  passengerType: 'adult' | 'child' | 'infant';
  count: number;
  basePrice: number;
  taxes: number;
  fees: number;
  total: number;
}

/**
 * Disponibilité des places
 */
export interface Availability {
  seats: number; // Nombre de places disponibles
  bookingClass: string; // Classe de réservation (ex: "Y")
  cabinClass: string; // Classe de cabine (ex: "Economy")
  fareBasis?: string; // Base tarifaire
}

/**
 * Informations sur les bagages
 */
export interface BaggageInfo {
  carryOn: BaggageAllowance;
  checked: BaggageAllowance;
}

/**
 * Franchise de bagages
 */
export interface BaggageAllowance {
  pieces: number; // Nombre de pièces
  weight: number; // Poids autorisé
  dimensions?: string; // Dimensions (ex: "55x40x20cm")
  unit: 'kg' | 'lbs'; // Unité de poids
}

/**
 * Critères de recherche OneWay utilisés
 */
export interface OneWaySearchCriteria {
  searchType: 'oneWay';
  departure: string; // Code aéroport de départ
  arrival: string; // Code aéroport d'arrivée
  departureDate: string; // Date de départ
  passengers: PassengerCount;
  class: string; // Classe demandée
  directOnly: boolean; // Vols directs uniquement
  currency: string; // Devise
  culture: string; // Culture/langue
}

/**
 * Nombre de passagers
 */
export interface PassengerCount {
  adults: number;
  children: number;
  infants: number;
}

/**
 * Filtres disponibles pour affiner la recherche
 */
export interface SearchFilters {
  airlines: FilterOption[];
  priceRange: PriceRange;
  departureTime: TimeRange[];
  arrivalTime: TimeRange[];
  duration: DurationRange;
  stops: StopOption[];
  airports: FilterOption[];
}

/**
 * Option de filtre générique
 */
export interface FilterOption {
  code: string;
  name: string;
  count: number;
  selected: boolean;
}

/**
 * Plage de prix
 */
export interface PriceRange {
  min: number;
  max: number;
  currency: string;
}

/**
 * Plage horaire
 */
export interface TimeRange {
  label: string; // Ex: "Matin (06:00 - 12:00)"
  start: string; // Heure de début
  end: string; // Heure de fin
  count: number; // Nombre de vols dans cette plage
}

/**
 * Plage de durée
 */
export interface DurationRange {
  min: number; // Durée minimale en minutes
  max: number; // Durée maximale en minutes
}

/**
 * Options d'escales
 */
export interface StopOption {
  stops: number; // Nombre d'escales (0 = direct)
  label: string; // Libellé (ex: "Direct", "1 escale")
  count: number; // Nombre de vols avec ce nombre d'escales
}

/**
 * Informations de pagination
 */
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// ===== RÉPONSES D'ERREUR =====

/**
 * Réponse en cas d'erreur
 */
export interface OneWayErrorResponse {
  header: ResponseHeader;
  error: ErrorDetails;
}

/**
 * Détails de l'erreur
 */
export interface ErrorDetails {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
  path?: string;
}

// ===== TYPES UTILITAIRES =====

/**
 * État de la recherche OneWay
 */
export enum OneWaySearchStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

/**
 * Types de tri disponibles pour OneWay
 */
export enum OneWaySortType {
  PRICE_ASC = 'price_asc',
  PRICE_DESC = 'price_desc',
  DURATION_ASC = 'duration_asc',
  DURATION_DESC = 'duration_desc',
  DEPARTURE_TIME_ASC = 'departure_asc',
  DEPARTURE_TIME_DESC = 'departure_desc',
  ARRIVAL_TIME_ASC = 'arrival_asc',
  ARRIVAL_TIME_DESC = 'arrival_desc',
  STOPS_ASC = 'stops_asc'
}

/**
 * Paramètres de tri et filtrage pour OneWay
 */
export interface OneWaySearchParams {
  sortBy?: OneWaySortType;
  filters?: {
    airlines?: string[];
    maxPrice?: number;
    minPrice?: number;
    maxStops?: number;
    departureTimeRange?: TimeRange;
    arrivalTimeRange?: TimeRange;
    maxDuration?: number; // en minutes
  };
  pagination?: {
    page: number;
    size: number;
  };
}

/**
 * Résumé de recherche OneWay
 */
export interface OneWaySearchSummary {
  totalFlights: number;
  cheapestPrice: number;
  fastestDuration: string;
  directFlights: number;
  airlinesCount: number;
  currency: string;
}
