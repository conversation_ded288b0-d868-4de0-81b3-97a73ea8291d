/**
 * Interface TypeScript correspondant exactement à la classe Java RoundTripRequest
 * Générée à partir du modèle backend com.paximum.demo.models.RoundTripRequest
 */

// ===== INTERFACES COMMUNES (réutilisées depuis OneWay) =====

/**
 * Interface correspondant à RoundTripRequest.Location
 */
export interface Location {
  id: string;
  type: number;
  provider?: number;
}

/**
 * Interface correspondant à RoundTripRequest.Passenger
 */
export interface Passenger {
  type: number;
  count: number;
}

/**
 * Interface correspondant à RoundTripRequest.CorporateRule
 */
export interface CorporateRule {
  Airline: string;
  Supplier: string;
}

/**
 * Interface correspondant à RoundTripRequest.CorporateCode
 */
export interface CorporateCode {
  Code: string;
  Rule: CorporateRule;
}

/**
 * Interface correspondant à RoundTripRequest.GetOptionsParameters
 */
export interface GetOptionsParameters {
  flightBaggageGetOption: number;
}

/**
 * Interface correspondant à RoundTripRequest.AdditionalParameters
 */
export interface AdditionalParameters {
  getOptionsParameters?: GetOptionsParameters;
  CorporateCodes?: CorporateCode[];
}

// ===== ROUND TRIP REQUEST =====

/**
 * Interface correspondant exactement à la classe Java RoundTripRequest
 * Tous les noms de propriétés correspondent aux @JsonProperty du backend
 * 
 * @example
 * ```typescript
 * const roundTripRequest: RoundTripRequest = {
 *   ProductType: 2,
 *   ServiceTypes: ['Flight'],
 *   DepartureLocations: [{ id: 'IST', type: 1, provider: 1 }],
 *   ArrivalLocations: [{ id: 'TUN', type: 1, provider: 1 }],
 *   CheckIn: '2024-06-15',
 *   Night: 7,
 *   Passengers: [{ type: 1, count: 1 }],
 *   acceptPendingProviders: true,
 *   forceFlightBundlePackage: false,
 *   disablePackageOfferTotalPrice: false,
 *   supportedFlightReponseListTypes: [1, 2],
 *   showOnlyNonStopFlight: false,
 *   calculateFlightFees: true,
 *   Culture: 'fr-FR',
 *   Currency: 'EUR'
 * };
 * ```
 */
export interface RoundTripRequest {
  /**
   * Type de produit (généralement 2 pour les vols)
   */
  ProductType: number;

  /**
   * Types de services (ex: ['Flight'])
   */
  ServiceTypes: string[];

  /**
   * Liste des lieux de départ
   */
  DepartureLocations: Location[];

  /**
   * Liste des lieux d'arrivée
   */
  ArrivalLocations: Location[];

  /**
   * Date de départ au format ISO (YYYY-MM-DD)
   */
  CheckIn: string;

  /**
   * Nombre de nuits du séjour
   */
  Night: number;

  /**
   * Liste des passagers avec leur type et nombre
   */
  Passengers: Passenger[];

  /**
   * Accepter les fournisseurs en attente
   */
  acceptPendingProviders: boolean;

  /**
   * Forcer le package de vol groupé
   */
  forceFlightBundlePackage: boolean;

  /**
   * Désactiver le prix total de l'offre package
   */
  disablePackageOfferTotalPrice: boolean;

  /**
   * Types de réponse de vol supportés
   */
  supportedFlightReponseListTypes: number[];

  /**
   * Afficher uniquement les vols sans escale
   */
  showOnlyNonStopFlight: boolean;

  /**
   * Paramètres additionnels (obligatoire selon le backend)
   */
  additionalParameters: AdditionalParameters;

  /**
   * Calculer les frais de vol
   */
  calculateFlightFees: boolean;

  /**
   * Culture/Langue (ex: 'fr-FR', 'en-US')
   */
  Culture: string;

  /**
   * Devise (ex: 'EUR', 'USD')
   */
  Currency: string;
}

// ===== TYPES D'ÉNUMÉRATION =====

/**
 * Types de passagers selon le backend
 */
export enum PassengerType {
  ADULT = 1,
  CHILD = 2,
  INFANT = 3
}

/**
 * Types de localisation selon le backend
 */
export enum LocationType {
  AIRPORT = 1,
  CITY = 2,
  COUNTRY = 3
}

/**
 * Classes de vol selon le backend
 */
export enum FlightClass {
  ECONOMY = 1,
  PREMIUM_ECONOMY = 2,
  BUSINESS = 3,
  FIRST = 4
}

/**
 * Types de produit selon le backend
 */
export enum ProductType {
  FLIGHT = 2
}

/**
 * Types de réponse de vol supportés
 */
export enum FlightResponseListType {
  STANDARD = 1,
  DETAILED = 2
}

// ===== INTERFACES UTILITAIRES =====

/**
 * Interface pour créer facilement une Location
 */
export interface LocationBuilder {
  id: string;
  type?: LocationType;
  provider?: number;
}

/**
 * Interface pour créer facilement un Passenger
 */
export interface PassengerBuilder {
  type: PassengerType;
  count: number;
}

/**
 * Interface pour les paramètres de base d'une requête RoundTrip
 */
export interface RoundTripRequestParams {
  departureLocation: string;
  arrivalLocation: string;
  departureDate: string;
  returnDate: string;
  passengers: {
    adults: number;
    children?: number;
    infants?: number;
  };
  flightClass?: FlightClass;
  directFlightsOnly?: boolean;
  culture?: string;
  currency?: string;
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Crée une Location à partir de paramètres simplifiés
 */
export function createLocation(params: LocationBuilder): Location {
  return {
    id: params.id.toUpperCase(), // Convertir en majuscules pour les codes IATA
    type: params.type || LocationType.AIRPORT,
    provider: params.provider || 1
  };
}

/**
 * Crée un Passenger à partir de paramètres simplifiés
 */
export function createPassenger(params: PassengerBuilder): Passenger {
  return {
    type: params.type,
    count: params.count
  };
}

/**
 * Crée une liste de passagers à partir de paramètres simplifiés
 */
export function createPassengerList(passengers: { adults: number; children?: number; infants?: number }): Passenger[] {
  const passengerList: Passenger[] = [];
  
  if (passengers.adults > 0) {
    passengerList.push(createPassenger({ type: PassengerType.ADULT, count: passengers.adults }));
  }
  
  if (passengers.children && passengers.children > 0) {
    passengerList.push(createPassenger({ type: PassengerType.CHILD, count: passengers.children }));
  }
  
  if (passengers.infants && passengers.infants > 0) {
    passengerList.push(createPassenger({ type: PassengerType.INFANT, count: passengers.infants }));
  }
  
  return passengerList;
}

/**
 * Calcule le nombre de nuits entre deux dates
 */
export function calculateNights(checkInDate: string, returnDate: string): number {
  const checkIn = new Date(checkInDate);
  const checkOut = new Date(returnDate);
  const timeDiff = checkOut.getTime() - checkIn.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**
 * Valeurs par défaut pour une requête RoundTrip
 */
export const DEFAULT_ROUNDTRIP_REQUEST: Partial<RoundTripRequest> = {
  ProductType: ProductType.FLIGHT,
  ServiceTypes: ['Flight'],
  acceptPendingProviders: true,
  forceFlightBundlePackage: false,
  disablePackageOfferTotalPrice: false,
  supportedFlightReponseListTypes: [FlightResponseListType.STANDARD, FlightResponseListType.DETAILED],
  showOnlyNonStopFlight: false,
  additionalParameters: {
    getOptionsParameters: {
      flightBaggageGetOption: 1
    },
    CorporateCodes: []
  },
  calculateFlightFees: true,
  Culture: 'fr-FR',
  Currency: 'EUR'
};

/**
 * Crée une requête RoundTrip complète à partir de paramètres simplifiés
 */
export function createRoundTripRequest(params: RoundTripRequestParams): RoundTripRequest {
  const nights = calculateNights(params.departureDate, params.returnDate);
  
  return {
    ...DEFAULT_ROUNDTRIP_REQUEST,
    DepartureLocations: [createLocation({ id: params.departureLocation })],
    ArrivalLocations: [createLocation({ id: params.arrivalLocation })],
    CheckIn: params.departureDate,
    Night: nights,
    Passengers: createPassengerList(params.passengers),
    showOnlyNonStopFlight: params.directFlightsOnly || false,
    Culture: params.culture || 'fr-FR',
    Currency: params.currency || 'EUR'
  } as RoundTripRequest;
}

/**
 * Valide une requête RoundTrip
 */
export function validateRoundTripRequest(request: RoundTripRequest): string[] {
  const errors: string[] = [];
  
  if (!request.CheckIn) {
    errors.push('CheckIn date is required');
  }
  
  if (request.Night <= 0) {
    errors.push('Night must be greater than 0');
  }
  
  if (!request.DepartureLocations || request.DepartureLocations.length === 0) {
    errors.push('At least one departure location is required');
  }
  
  if (!request.ArrivalLocations || request.ArrivalLocations.length === 0) {
    errors.push('At least one arrival location is required');
  }
  
  if (!request.Passengers || request.Passengers.length === 0) {
    errors.push('At least one passenger is required');
  }
  
  if (!request.Culture) {
    errors.push('Culture is required');
  }
  
  if (!request.Currency) {
    errors.push('Currency is required');
  }
  
  return errors;
}
