/**
 * Interfaces TypeScript correspondant exactement à la classe Java RoundTripResponse
 * Générées à partir du modèle backend com.paximum.demo.models.RoundTripResponse
 */

// ===== STRUCTURE PRINCIPALE =====

/**
 * Interface correspondant exactement à la classe Java RoundTripResponse
 */
export interface RoundTripResponse {
  header: Header;
  body: Body;
}

/**
 * Interface correspondant à RoundTripResponse.Header
 */
export interface Header {
  requestId: string;
  success: boolean;
  responseTime: string; // ZonedDateTime en string ISO
  messages: Message[];
}

/**
 * Interface correspondant à RoundTripResponse.Message
 */
export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

/**
 * Interface correspondant à RoundTripResponse.Body
 */
export interface Body {
  searchId: string;
  expiresOn: string; // ZonedDateTime en string ISO
  flights: Flight[];
  details: Details;
}

/**
 * Interface correspondant à RoundTripResponse.Details
 */
export interface Details {
  flightResponseListType: number;
  enablePaging: boolean;
}

// ===== STRUCTURES DE VOL =====

/**
 * Interface correspondant à RoundTripResponse.Flight
 */
export interface Flight {
  provider: number;
  id: string;
  items: FlightItem[];
  offers: Offer[];
  groupKeys: string[];
}

/**
 * Interface correspondant à RoundTripResponse.FlightItem
 */
export interface FlightItem {
  flightNo: string;
  pnlName: string;
  flightDate: string; // LocalDateTime en string ISO
  airline: Airline;
  duration: number; // en minutes
  dayChange: number;
  departure: LocationInfo;
  arrival: LocationInfo;
  flightClass: FlightClass;
  route: number;
  segments: Segment[];
  stopCount: number;
  baggageInformations: BaggageInformation[];
  passengers: PassengerInfo[];
  flightType: number;
}

/**
 * Interface correspondant à RoundTripResponse.Airline
 */
export interface Airline {
  internationalCode: string;
  thumbnail: string;
  thumbnailFull: string;
  logo: string;
  logoFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.LocationInfo
 */
export interface LocationInfo {
  country: Region;
  city: Region;
  airport: Airport;
  date: string; // LocalDateTime en string ISO
  geoLocation: GeoLocation;
}

/**
 * Interface correspondant à RoundTripResponse.Region
 */
export interface Region {
  name: string;
  provider: number;
  isTopRegion: boolean;
  id: string;
}

/**
 * Interface correspondant à RoundTripResponse.Airport
 */
export interface Airport {
  geolocation: GeoLocation;
  name: string;
  id: string;
  code: string;
}

/**
 * Interface correspondant à RoundTripResponse.GeoLocation
 */
export interface GeoLocation {
  longitude: string;
  latitude: string;
}

/**
 * Interface correspondant à RoundTripResponse.FlightClass
 */
export interface FlightClass {
  name: string;
  id: string;
  code: string;
  type?: number;
}

/**
 * Interface correspondant à RoundTripResponse.Segment
 */
export interface Segment {
  id: string;
  flightNo: string;
  pnlName: string;
  flightClass: FlightClass;
  airline: Airline;
  departure: LocationInfo;
  arrival: LocationInfo;
  duration: number; // en minutes
  baggageInformations: BaggageInformation[];
  services: Service[];
  aircraft: string;
}

/**
 * Interface correspondant à RoundTripResponse.BaggageInformation
 */
export interface BaggageInformation {
  segmentId: string;
  weight?: number;
  piece: number;
  baggageType: number;
  unitType: number;
  passengerType: number;
}

/**
 * Interface correspondant à RoundTripResponse.Service
 */
export interface Service {
  segments: string[];
  thumbnail: string;
  thumbnailFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.PassengerInfo
 */
export interface PassengerInfo {
  type: number;
  count: number;
  ages: number[];
}
