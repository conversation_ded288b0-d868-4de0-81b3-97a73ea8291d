/**
 * Interfaces TypeScript correspondant exactement à la classe Java RoundTripResponse
 * Générées à partir du modèle backend com.paximum.demo.models.RoundTripResponse
 */

// ===== STRUCTURE PRINCIPALE =====

/**
 * Interface correspondant exactement à la classe Java RoundTripResponse
 */
export interface RoundTripResponse {
  header: Header;
  body: Body;
}

/**
 * Interface correspondant à RoundTripResponse.Header
 */
export interface Header {
  requestId: string;
  success: boolean;
  responseTime: string; // ZonedDateTime en string ISO
  messages: Message[];
}

/**
 * Interface correspondant à RoundTripResponse.Message
 */
export interface Message {
  id: number;
  code: string;
  messageType: number;
  message: string;
}

/**
 * Interface correspondant à RoundTripResponse.Body
 */
export interface Body {
  searchId: string;
  expiresOn: string; // ZonedDateTime en string ISO
  flights: Flight[];
  details: Details;
}

/**
 * Interface correspondant à RoundTripResponse.Details
 */
export interface Details {
  flightResponseListType: number;
  enablePaging: boolean;
}

// ===== STRUCTURES DE VOL =====

/**
 * Interface correspondant à RoundTripResponse.Flight
 */
export interface Flight {
  provider: number;
  id: string;
  items: FlightItem[];
  offers: Offer[];
  groupKeys: string[];
}

/**
 * Interface correspondant à RoundTripResponse.FlightItem
 */
export interface FlightItem {
  flightNo: string;
  pnlName: string;
  flightDate: string; // LocalDateTime en string ISO
  airline: Airline;
  duration: number; // en minutes
  dayChange: number;
  departure: LocationInfo;
  arrival: LocationInfo;
  flightClass: FlightClass;
  route: number;
  segments: Segment[];
  stopCount: number;
  baggageInformations: BaggageInformation[];
  passengers: PassengerInfo[];
  flightType: number;
}

/**
 * Interface correspondant à RoundTripResponse.Airline
 */
export interface Airline {
  internationalCode: string;
  thumbnail: string;
  thumbnailFull: string;
  logo: string;
  logoFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.LocationInfo
 */
export interface LocationInfo {
  country: Region;
  city: Region;
  airport: Airport;
  date: string; // LocalDateTime en string ISO
  geoLocation: GeoLocation;
}

/**
 * Interface correspondant à RoundTripResponse.Region
 */
export interface Region {
  name: string;
  provider: number;
  isTopRegion: boolean;
  id: string;
}

/**
 * Interface correspondant à RoundTripResponse.Airport
 */
export interface Airport {
  geolocation: GeoLocation;
  name: string;
  id: string;
  code: string;
}

/**
 * Interface correspondant à RoundTripResponse.GeoLocation
 */
export interface GeoLocation {
  longitude: string;
  latitude: string;
}

/**
 * Interface correspondant à RoundTripResponse.FlightClass
 */
export interface FlightClass {
  name: string;
  id: string;
  code: string;
  type?: number;
}

/**
 * Interface correspondant à RoundTripResponse.Segment
 */
export interface Segment {
  id: string;
  flightNo: string;
  pnlName: string;
  flightClass: FlightClass;
  airline: Airline;
  departure: LocationInfo;
  arrival: LocationInfo;
  duration: number; // en minutes
  baggageInformations: BaggageInformation[];
  services: Service[];
  aircraft: string;
}

/**
 * Interface correspondant à RoundTripResponse.BaggageInformation
 */
export interface BaggageInformation {
  segmentId: string;
  weight?: number;
  piece: number;
  baggageType: number;
  unitType: number;
  passengerType: number;
}

/**
 * Interface correspondant à RoundTripResponse.Service
 */
export interface Service {
  segments: string[];
  thumbnail: string;
  thumbnailFull: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.PassengerInfo
 */
export interface PassengerInfo {
  type: number;
  count: number;
  ages: number[];
}

// ===== STRUCTURES D'OFFRES ET PRIX =====

/**
 * Interface correspondant à RoundTripResponse.Offer
 */
export interface Offer {
  singleAdultPrice: Price;
  serviceFee: Price;
  seatInfo: SeatInfo;
  flightClassInformations: FlightClassInfo[];
  baggageInformations: BaggageInformation[];
  services: Service[];
  reservableInfo: ReservableInfo;
  groupKeys: string[];
  fees: Fees;
  offerIds: OfferId[];
  isPackageOffer: boolean;
  hasBrand: boolean;
  route: number;
  flightBrandInfo: FlightBrandInfo;
  expiresOn: string; // ZonedDateTime en string ISO
  priceBreakDown: PriceBreakDown;
  price: Price;
  provider: number;
  flightProvider: Provider;
}

/**
 * Interface correspondant à RoundTripResponse.Price
 */
export interface Price {
  amount: number;
  currency: string;
}

/**
 * Interface correspondant à RoundTripResponse.SeatInfo
 */
export interface SeatInfo {
  availableSeatCount: number;
  availableSeatCountType: number;
}

/**
 * Interface correspondant à RoundTripResponse.FlightClassInfo
 */
export interface FlightClassInfo {
  segmentId: string;
  name: string;
  id: string;
  code: string;
  type?: number;
}

/**
 * Interface correspondant à RoundTripResponse.ReservableInfo
 */
export interface ReservableInfo {
  reservable: boolean;
}

/**
 * Interface correspondant à RoundTripResponse.Fees
 */
export interface Fees {
  key: string;
  oneWay: FeeDetails;
  roundTrip: FeeDetails;
}

/**
 * Interface correspondant à RoundTripResponse.FeeDetails
 */
export interface FeeDetails {
  items: FeeItem[];
  price: Price;
}

/**
 * Interface correspondant à RoundTripResponse.FeeItem
 */
export interface FeeItem {
  passengerType: number;
  quantity: number;
  price: number;
  totalPrice: number;
}

/**
 * Interface correspondant à RoundTripResponse.OfferId
 */
export interface OfferId {
  groupKey: string;
  offerId: string;
  provider: Provider;
}

/**
 * Interface correspondant à RoundTripResponse.Provider
 */
export interface Provider {
  displayName: string;
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.FlightBrandInfo
 */
export interface FlightBrandInfo {
  id: string;
  name: string;
}

/**
 * Interface correspondant à RoundTripResponse.PriceBreakDown
 */
export interface PriceBreakDown {
  items: PriceBreakDownItem[];
}

/**
 * Interface correspondant à RoundTripResponse.PriceBreakDownItem
 */
export interface PriceBreakDownItem {
  passengerType: number;
  passengerCount: number;
  price: Price;
}

// ===== TYPES UTILITAIRES =====

/**
 * État de la recherche RoundTrip
 */
export enum RoundTripSearchStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  TIMEOUT = 'timeout'
}

/**
 * Types de tri disponibles pour RoundTrip
 */
export enum RoundTripSortType {
  PRICE_ASC = 'price_asc',
  PRICE_DESC = 'price_desc',
  DURATION_ASC = 'duration_asc',
  DURATION_DESC = 'duration_desc',
  DEPARTURE_TIME_ASC = 'departure_asc',
  DEPARTURE_TIME_DESC = 'departure_desc',
  ARRIVAL_TIME_ASC = 'arrival_asc',
  ARRIVAL_TIME_DESC = 'arrival_desc',
  STOPS_ASC = 'stops_asc'
}

/**
 * Paramètres de tri et filtrage pour RoundTrip
 */
export interface RoundTripSearchParams {
  sortBy?: RoundTripSortType;
  filters?: {
    airlines?: string[];
    maxPrice?: number;
    minPrice?: number;
    maxStops?: number;
    departureTimeRange?: TimeRange;
    arrivalTimeRange?: TimeRange;
    maxDuration?: number; // en minutes
  };
  pagination?: {
    page: number;
    size: number;
  };
}

/**
 * Plage horaire
 */
export interface TimeRange {
  label: string; // Ex: "Matin (06:00 - 12:00)"
  start: string; // Heure de début
  end: string; // Heure de fin
  count: number; // Nombre de vols dans cette plage
}

/**
 * Résumé de recherche RoundTrip
 */
export interface RoundTripSearchSummary {
  totalFlights: number;
  cheapestPrice: number;
  fastestDuration: string;
  directFlights: number;
  airlinesCount: number;
  currency: string;
  outboundSummary: RouteSummary;
  inboundSummary: RouteSummary;
}

/**
 * Résumé par route (aller ou retour)
 */
export interface RouteSummary {
  route: string; // Ex: "IST → TUN"
  date: string;
  flightCount: number;
  cheapestPrice: number;
  fastestDuration: string;
}

// ===== RÉPONSES D'ERREUR =====

/**
 * Réponse en cas d'erreur
 */
export interface RoundTripErrorResponse {
  header: Header;
  error: ErrorDetails;
}

/**
 * Détails de l'erreur
 */
export interface ErrorDetails {
  code: string;
  message: string;
  details?: string;
  timestamp: string;
  path?: string;
}

// ===== FONCTIONS UTILITAIRES =====

/**
 * Sépare les vols aller et retour d'une réponse RoundTrip
 */
export function separateOutboundInbound(response: RoundTripResponse): {
  outbound: Flight[];
  inbound: Flight[];
} {
  const outbound: Flight[] = [];
  const inbound: Flight[] = [];

  response.body.flights.forEach(flight => {
    flight.items.forEach(item => {
      if (item.route === 0) { // Route aller
        outbound.push(flight);
      } else if (item.route === 1) { // Route retour
        inbound.push(flight);
      }
    });
  });

  return { outbound, inbound };
}

/**
 * Calcule le prix total d'un itinéraire RoundTrip
 */
export function calculateTotalPrice(flight: Flight): number {
  return flight.offers.reduce((total, offer) => total + offer.price.amount, 0);
}

/**
 * Formate la durée totale d'un itinéraire RoundTrip
 */
export function formatTotalDuration(flight: Flight): string {
  const totalMinutes = flight.items.reduce((total, item) => total + item.duration, 0);
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours}h ${minutes}m`;
}

/**
 * Vérifie si un itinéraire RoundTrip a des vols directs uniquement
 */
export function hasOnlyDirectFlights(flight: Flight): boolean {
  return flight.items.every(item => item.stopCount === 0);
}

/**
 * Extrait les compagnies aériennes uniques d'une réponse
 */
export function extractUniqueAirlines(response: RoundTripResponse): Airline[] {
  const airlineMap = new Map<string, Airline>();

  response.body.flights.forEach(flight => {
    flight.items.forEach(item => {
      airlineMap.set(item.airline.id, item.airline);
    });
  });

  return Array.from(airlineMap.values());
}

/**
 * Trouve le vol le moins cher dans une réponse
 */
export function findCheapestFlight(response: RoundTripResponse): { flight: Flight; price: number } | null {
  let cheapestFlight: Flight | null = null;
  let cheapestPrice = Infinity;

  response.body.flights.forEach(flight => {
    const totalPrice = calculateTotalPrice(flight);
    if (totalPrice < cheapestPrice) {
      cheapestPrice = totalPrice;
      cheapestFlight = flight;
    }
  });

  return cheapestFlight ? { flight: cheapestFlight, price: cheapestPrice } : null;
}
