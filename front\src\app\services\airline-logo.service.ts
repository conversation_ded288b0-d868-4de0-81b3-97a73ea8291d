import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AirlineLogoService {
  private logoMap: { [key: string]: string[] } = {
    'TK': [
      'https://content.airhex.com/content/logos/airlines_TK_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png'
    ],
    'TU': [
      'https://content.airhex.com/content/logos/airlines_TU_200_200_s.png',
      'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/200px-Tunisair_Logo.svg.png'
    ],
    'AF': [
      'https://content.airhex.com/content/logos/airlines_AF_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png'
    ],
    'LH': [
      'https://content.airhex.com/content/logos/airlines_LH_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png'
    ],
    'EK': [
      'https://content.airhex.com/content/logos/airlines_EK_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png'
    ],
    'QR': [
      'https://content.airhex.com/content/logos/airlines_QR_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png'
    ],
    'BA': [
      'https://content.airhex.com/content/logos/airlines_BA_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png'
    ],
    'KL': [
      'https://content.airhex.com/content/logos/airlines_KL_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png'
    ],
    'IB': [
      'https://content.airhex.com/content/logos/airlines_IB_200_200_s.png',
      'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png'
    ],
    'AZ': [
      'https://content.airhex.com/content/logos/airlines_AZ_200_200_s.png',
      'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/200px-Alitalia_logo_2015.svg.png'
    ],
    'MS': [
      'https://content.airhex.com/content/logos/airlines_MS_200_200_s.png'
    ],
    'SV': [
      'https://content.airhex.com/content/logos/airlines_SV_200_200_s.png'
    ],
    'RJ': [
      'https://content.airhex.com/content/logos/airlines_RJ_200_200_s.png'
    ],
    'UX': [
      'https://content.airhex.com/content/logos/airlines_UX_200_200_s.png'
    ]
  };

  constructor() { }

  /**
   * Obtient le logo principal d'une compagnie aérienne
   */
  getAirlineLogo(airlineCode: string): string | null {
    const logos = this.logoMap[airlineCode];
    return logos && logos.length > 0 ? logos[0] : null;
  }

  /**
   * Obtient tous les logos alternatifs d'une compagnie aérienne
   */
  getAlternativeLogos(airlineCode: string): string[] {
    const logos = this.logoMap[airlineCode];
    return logos ? logos.slice(1) : [];
  }

  /**
   * Obtient le logo suivant en cas d'erreur
   */
  getNextLogo(airlineCode: string, currentUrl: string): string | null {
    const logos = this.logoMap[airlineCode];
    if (!logos) return null;

    const currentIndex = logos.indexOf(currentUrl);
    if (currentIndex >= 0 && currentIndex < logos.length - 1) {
      return logos[currentIndex + 1];
    }

    return null;
  }

  /**
   * Vérifie si un logo existe pour une compagnie
   */
  hasLogo(airlineCode: string): boolean {
    return airlineCode in this.logoMap;
  }

  /**
   * Obtient tous les codes de compagnies avec logos
   */
  getAvailableAirlines(): string[] {
    return Object.keys(this.logoMap);
  }
}
