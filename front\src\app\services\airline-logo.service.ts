import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class AirlineLogoService {
  private logoMap: { [key: string]: string } = {
    'TK': 'https://logos-world.net/wp-content/uploads/2023/01/Turkish-Airlines-Logo.png',
    'TU': 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/79/Tunisair_Logo.svg/1200px-Tunisair_Logo.svg.png',
    'AF': 'https://logos-world.net/wp-content/uploads/2020/03/Air-France-Logo.png',
    'LH': 'https://logos-world.net/wp-content/uploads/2020/03/Lufthansa-Logo.png',
    'EK': 'https://logos-world.net/wp-content/uploads/2020/03/Emirates-Logo.png',
    'QR': 'https://logos-world.net/wp-content/uploads/2020/03/Qatar-Airways-Logo.png',
    'BA': 'https://logos-world.net/wp-content/uploads/2020/03/British-Airways-Logo.png',
    'KL': 'https://logos-world.net/wp-content/uploads/2020/03/KLM-Logo.png',
    'IB': 'https://logos-world.net/wp-content/uploads/2020/03/Iberia-Logo.png',
    'AZ': 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Alitalia_logo_2015.svg/1200px-Alitalia_logo_2015.svg.png'
  };

  constructor() { }

  /**
   * Obtient le logo d'une compagnie aérienne
   */
  getAirlineLogo(airlineCode: string): string | null {
    return this.logoMap[airlineCode] || null;
  }

  /**
   * Vérifie si un logo existe pour une compagnie
   */
  hasLogo(airlineCode: string): boolean {
    return airlineCode in this.logoMap;
  }

  /**
   * Obtient tous les codes de compagnies avec logos
   */
  getAvailableAirlines(): string[] {
    return Object.keys(this.logoMap);
  }
}
