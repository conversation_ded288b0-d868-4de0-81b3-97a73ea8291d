import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, filter } from 'rxjs/operators';

export interface Airport {
  code: string;
  name: string;
  city: string;
  country: string;
  displayName: string;
}

export interface Airline {
  code: string;
  name: string;
  displayName: string;
}

@Injectable({
  providedIn: 'root'
})
export class AutocompleteService {

  // Liste des aéroports populaires
  private airports: Airport[] = [
    // France
    { code: 'CDG', name: 'Charles de Gaulle Airport', city: 'Paris', country: 'France', displayName: 'CDG - Paris Charles de Gaulle' },
    { code: 'ORY', name: 'Orly Airport', city: 'Paris', country: 'France', displayName: 'ORY - Paris Orly' },
    { code: 'NCE', name: 'Nice Côte d\'Azur Airport', city: 'Nice', country: 'France', displayName: 'NCE - Nice Côte d\'Azur' },
    { code: 'LYS', name: 'Lyon-Saint Exupéry Airport', city: 'Lyon', country: 'France', displayName: 'LYS - Lyon Saint Exupéry' },
    { code: 'MRS', name: 'Marseille Provence Airport', city: 'Marseille', country: 'France', displayName: 'MRS - Marseille Provence' },
    
    // Turquie
    { code: 'IST', name: 'Istanbul Airport', city: 'Istanbul', country: 'Turkey', displayName: 'IST - Istanbul Airport' },
    { code: 'SAW', name: 'Sabiha Gökçen Airport', city: 'Istanbul', country: 'Turkey', displayName: 'SAW - Istanbul Sabiha Gökçen' },
    { code: 'AYT', name: 'Antalya Airport', city: 'Antalya', country: 'Turkey', displayName: 'AYT - Antalya Airport' },
    { code: 'ESB', name: 'Esenboğa Airport', city: 'Ankara', country: 'Turkey', displayName: 'ESB - Ankara Esenboğa' },
    { code: 'ADB', name: 'Adnan Menderes Airport', city: 'Izmir', country: 'Turkey', displayName: 'ADB - Izmir Adnan Menderes' },
    
    // Tunisie
    { code: 'TUN', name: 'Tunis-Carthage Airport', city: 'Tunis', country: 'Tunisia', displayName: 'TUN - Tunis Carthage' },
    { code: 'SFA', name: 'Sfax-Thyna Airport', city: 'Sfax', country: 'Tunisia', displayName: 'SFA - Sfax Thyna' },
    { code: 'DJE', name: 'Djerba-Zarzis Airport', city: 'Djerba', country: 'Tunisia', displayName: 'DJE - Djerba Zarzis' },
    { code: 'MIR', name: 'Monastir Habib Bourguiba Airport', city: 'Monastir', country: 'Tunisia', displayName: 'MIR - Monastir Habib Bourguiba' },
    
    // Maroc
    { code: 'CMN', name: 'Mohammed V Airport', city: 'Casablanca', country: 'Morocco', displayName: 'CMN - Casablanca Mohammed V' },
    { code: 'RAK', name: 'Marrakech Menara Airport', city: 'Marrakech', country: 'Morocco', displayName: 'RAK - Marrakech Menara' },
    { code: 'RBA', name: 'Rabat-Salé Airport', city: 'Rabat', country: 'Morocco', displayName: 'RBA - Rabat Salé' },
    
    // Allemagne
    { code: 'FRA', name: 'Frankfurt Airport', city: 'Frankfurt', country: 'Germany', displayName: 'FRA - Frankfurt am Main' },
    { code: 'MUC', name: 'Munich Airport', city: 'Munich', country: 'Germany', displayName: 'MUC - Munich Airport' },
    { code: 'BER', name: 'Berlin Brandenburg Airport', city: 'Berlin', country: 'Germany', displayName: 'BER - Berlin Brandenburg' },
    
    // Royaume-Uni
    { code: 'LHR', name: 'Heathrow Airport', city: 'London', country: 'United Kingdom', displayName: 'LHR - London Heathrow' },
    { code: 'LGW', name: 'Gatwick Airport', city: 'London', country: 'United Kingdom', displayName: 'LGW - London Gatwick' },
    { code: 'STN', name: 'Stansted Airport', city: 'London', country: 'United Kingdom', displayName: 'STN - London Stansted' },
    
    // Espagne
    { code: 'MAD', name: 'Madrid-Barajas Airport', city: 'Madrid', country: 'Spain', displayName: 'MAD - Madrid Barajas' },
    { code: 'BCN', name: 'Barcelona-El Prat Airport', city: 'Barcelona', country: 'Spain', displayName: 'BCN - Barcelona El Prat' },
    { code: 'PMI', name: 'Palma de Mallorca Airport', city: 'Palma', country: 'Spain', displayName: 'PMI - Palma de Mallorca' },
    
    // Italie
    { code: 'FCO', name: 'Leonardo da Vinci Airport', city: 'Rome', country: 'Italy', displayName: 'FCO - Rome Fiumicino' },
    { code: 'MXP', name: 'Malpensa Airport', city: 'Milan', country: 'Italy', displayName: 'MXP - Milan Malpensa' },
    { code: 'VCE', name: 'Venice Marco Polo Airport', city: 'Venice', country: 'Italy', displayName: 'VCE - Venice Marco Polo' },
    
    // États-Unis
    { code: 'JFK', name: 'John F. Kennedy Airport', city: 'New York', country: 'United States', displayName: 'JFK - New York JFK' },
    { code: 'LAX', name: 'Los Angeles Airport', city: 'Los Angeles', country: 'United States', displayName: 'LAX - Los Angeles' },
    { code: 'MIA', name: 'Miami Airport', city: 'Miami', country: 'United States', displayName: 'MIA - Miami International' },
    
    // Émirats Arabes Unis
    { code: 'DXB', name: 'Dubai Airport', city: 'Dubai', country: 'UAE', displayName: 'DXB - Dubai International' },
    { code: 'AUH', name: 'Abu Dhabi Airport', city: 'Abu Dhabi', country: 'UAE', displayName: 'AUH - Abu Dhabi International' },
    
    // Qatar
    { code: 'DOH', name: 'Hamad Airport', city: 'Doha', country: 'Qatar', displayName: 'DOH - Doha Hamad' }
  ];

  // Liste des compagnies aériennes
  private airlines: Airline[] = [
    { code: 'TK', name: 'Turkish Airlines', displayName: 'TK - Turkish Airlines' },
    { code: 'TU', name: 'Tunisair', displayName: 'TU - Tunisair' },
    { code: 'AF', name: 'Air France', displayName: 'AF - Air France' },
    { code: 'LH', name: 'Lufthansa', displayName: 'LH - Lufthansa' },
    { code: 'BA', name: 'British Airways', displayName: 'BA - British Airways' },
    { code: 'IB', name: 'Iberia', displayName: 'IB - Iberia' },
    { code: 'AZ', name: 'Alitalia', displayName: 'AZ - Alitalia' },
    { code: 'KL', name: 'KLM', displayName: 'KL - KLM Royal Dutch Airlines' },
    { code: 'LX', name: 'Swiss', displayName: 'LX - Swiss International Air Lines' },
    { code: 'OS', name: 'Austrian Airlines', displayName: 'OS - Austrian Airlines' },
    { code: 'SN', name: 'Brussels Airlines', displayName: 'SN - Brussels Airlines' },
    { code: 'EK', name: 'Emirates', displayName: 'EK - Emirates' },
    { code: 'QR', name: 'Qatar Airways', displayName: 'QR - Qatar Airways' },
    { code: 'EY', name: 'Etihad Airways', displayName: 'EY - Etihad Airways' },
    { code: 'MS', name: 'EgyptAir', displayName: 'MS - EgyptAir' },
    { code: 'RJ', name: 'Royal Jordanian', displayName: 'RJ - Royal Jordanian' },
    { code: 'AT', name: 'Royal Air Maroc', displayName: 'AT - Royal Air Maroc' },
    { code: 'UX', name: 'Air Europa', displayName: 'UX - Air Europa' },
    { code: 'TP', name: 'TAP Air Portugal', displayName: 'TP - TAP Air Portugal' },
    { code: 'FR', name: 'Ryanair', displayName: 'FR - Ryanair' },
    { code: 'U2', name: 'easyJet', displayName: 'U2 - easyJet' },
    { code: 'W6', name: 'Wizz Air', displayName: 'W6 - Wizz Air' },
    { code: 'PC', name: 'Pegasus Airlines', displayName: 'PC - Pegasus Airlines' },
    { code: 'XQ', name: 'SunExpress', displayName: 'XQ - SunExpress' }
  ];

  constructor() { }

  /**
   * Recherche d'aéroports par terme de recherche
   */
  searchAirports(query: string): Observable<Airport[]> {
    if (!query || query.length < 2) {
      return of([]);
    }

    const searchTerm = query.toLowerCase();
    const filteredAirports = this.airports.filter(airport => 
      airport.code.toLowerCase().includes(searchTerm) ||
      airport.name.toLowerCase().includes(searchTerm) ||
      airport.city.toLowerCase().includes(searchTerm) ||
      airport.country.toLowerCase().includes(searchTerm)
    );

    return of(filteredAirports.slice(0, 10)); // Limiter à 10 résultats
  }

  /**
   * Recherche de compagnies aériennes par terme de recherche
   */
  searchAirlines(query: string): Observable<Airline[]> {
    if (!query || query.length < 1) {
      return of([]);
    }

    const searchTerm = query.toLowerCase();
    const filteredAirlines = this.airlines.filter(airline => 
      airline.code.toLowerCase().includes(searchTerm) ||
      airline.name.toLowerCase().includes(searchTerm)
    );

    return of(filteredAirlines.slice(0, 10)); // Limiter à 10 résultats
  }

  /**
   * Obtenir tous les aéroports
   */
  getAllAirports(): Observable<Airport[]> {
    return of(this.airports);
  }

  /**
   * Obtenir toutes les compagnies aériennes
   */
  getAllAirlines(): Observable<Airline[]> {
    return of(this.airlines);
  }

  /**
   * Obtenir un aéroport par code
   */
  getAirportByCode(code: string): Airport | undefined {
    return this.airports.find(airport => airport.code.toLowerCase() === code.toLowerCase());
  }

  /**
   * Obtenir une compagnie aérienne par code
   */
  getAirlineByCode(code: string): Airline | undefined {
    return this.airlines.find(airline => airline.code.toLowerCase() === code.toLowerCase());
  }

  /**
   * Obtenir les aéroports populaires (les 20 premiers)
   */
  getPopularAirports(): Observable<Airport[]> {
    return of(this.airports.slice(0, 20));
  }

  /**
   * Obtenir les compagnies aériennes populaires (les 15 premières)
   */
  getPopularAirlines(): Observable<Airline[]> {
    return of(this.airlines.slice(0, 15));
  }
}
