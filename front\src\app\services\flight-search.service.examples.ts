/**
 * Exemples d'utilisation du FlightSearchService
 * Ce fichier montre comment utiliser le service avec vos endpoints backend
 */

import { FlightSearchService } from './flight-search.service';
import { OneWayRequest } from '../models/oneway-request.interface';
import { RoundTripRequest } from '../models/roundtrip-request.interface';
import { MulticityRequest } from '../models/multicity-request.interface';

/**
 * Exemples d'utilisation dans un composant Angular
 */
export class FlightSearchExamples {
  
  constructor(private flightSearchService: FlightSearchService) {}

  // ===== EXEMPLE 1: RECHERCHE ALLER SIMPLE =====
  
  /**
   * Recherche aller simple Istanbul → Tunis
   */
  searchOneWayExample() {
    // Méthode 1: Utilisation directe avec createOneWaySearch
    const searchParams = {
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-15',
      passengers: {
        adults: 1,
        children: 0,
        infants: 0
      },
      flightClass: 1, // Economy
      directFlightsOnly: false,
      culture: 'fr-FR',
      currency: 'EUR'
    };

    const oneWayRequest = this.flightSearchService.createOneWaySearch(searchParams);
    
    this.flightSearchService.searchOneWay(oneWayRequest).subscribe({
      next: (response) => {
        console.log('Résultats OneWay:', response);
        // Traiter les résultats
        if (response.header.success) {
          const flights = response.body.flights;
          console.log(`${flights.length} vols trouvés`);
        }
      },
      error: (error) => {
        console.error('Erreur recherche OneWay:', error.message);
      }
    });

    // Méthode 2: Utilisation avec quickSearch
    this.flightSearchService.quickSearch({
      type: 'oneway',
      from: 'IST',
      to: 'TUN',
      departureDate: '2024-06-15',
      adults: 1
    }).subscribe({
      next: (response) => console.log('Résultats quickSearch OneWay:', response),
      error: (error) => console.error('Erreur quickSearch OneWay:', error)
    });
  }

  // ===== EXEMPLE 2: RECHERCHE ALLER-RETOUR =====
  
  /**
   * Recherche aller-retour Istanbul → Tunis → Istanbul
   */
  searchRoundTripExample() {
    const searchParams = {
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-15',
      returnDate: '2024-06-22',
      passengers: {
        adults: 2,
        children: 1,
        infants: 0
      },
      flightClass: 2, // Premium Economy
      directFlightsOnly: true,
      culture: 'fr-FR',
      currency: 'EUR'
    };

    const roundTripRequest = this.flightSearchService.createRoundTripSearch(searchParams);
    
    this.flightSearchService.searchRoundTrip(roundTripRequest).subscribe({
      next: (response) => {
        console.log('Résultats RoundTrip:', response);
        // Traiter les résultats aller-retour
      },
      error: (error) => {
        console.error('Erreur recherche RoundTrip:', error.message);
      }
    });
  }

  // ===== EXEMPLE 3: RECHERCHE MULTI-DESTINATIONS =====
  
  /**
   * Recherche multi-destinations: Istanbul → Tunis → Paris → Istanbul
   */
  searchMultiCityExample() {
    const searchParams = {
      segments: [
        { from: 'IST', to: 'TUN', date: '2024-06-15' },
        { from: 'TUN', to: 'PAR', date: '2024-06-20' },
        { from: 'PAR', to: 'IST', date: '2024-06-25' }
      ],
      passengers: {
        adults: 1,
        children: 0,
        infants: 0
      },
      directFlightsOnly: false,
      culture: 'fr-FR',
      currency: 'EUR'
    };

    const multicityRequest = this.flightSearchService.createMultiCitySearch(searchParams);
    
    this.flightSearchService.searchMultiCity(multicityRequest).subscribe({
      next: (response) => {
        console.log('Résultats MultiCity:', response);
        // Traiter les résultats multi-destinations
        if (response.header.success) {
          const flights = response.body.flights;
          console.log(`${flights.length} itinéraires trouvés`);
          
          // Analyser les segments
          flights.forEach(flight => {
            flight.items.forEach(item => {
              console.log(`Segment ${item.segmentNumber}: ${item.departure.airport.code} → ${item.arrival.airport.code}`);
            });
          });
        }
      },
      error: (error) => {
        console.error('Erreur recherche MultiCity:', error.message);
      }
    });
  }

  // ===== EXEMPLE 4: GESTION DES ERREURS =====
  
  /**
   * Exemple de gestion complète des erreurs
   */
  searchWithErrorHandling() {
    // Validation avant envoi
    const searchParams = {
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-15',
      passengers: { adults: 1 }
    };

    const errors = this.flightSearchService.validateSearchParams(searchParams);
    if (errors.length > 0) {
      console.error('Erreurs de validation:', errors);
      return;
    }

    // Vérification de l'authentification
    if (!this.flightSearchService.isAuthenticated()) {
      console.error('Utilisateur non authentifié');
      return;
    }

    // Recherche avec gestion d'erreurs
    this.flightSearchService.quickSearch({
      type: 'oneway',
      from: searchParams.departureLocation,
      to: searchParams.arrivalLocation,
      departureDate: searchParams.departureDate,
      adults: searchParams.passengers.adults
    }).subscribe({
      next: (response) => {
        if (response.header.success) {
          console.log('Recherche réussie:', response);
        } else {
          console.warn('Recherche avec avertissements:', response.header.messages);
        }
      },
      error: (error) => {
        // Gestion spécifique selon le type d'erreur
        if (error.message.includes('authentification')) {
          // Rediriger vers la page de connexion
          console.log('Redirection vers login');
        } else if (error.message.includes('validation')) {
          // Afficher les erreurs de validation
          console.log('Erreurs de formulaire');
        } else {
          // Erreur générale
          console.log('Erreur technique');
        }
      }
    });
  }

  // ===== EXEMPLE 5: UTILISATION AVEC ASYNC/AWAIT =====
  
  /**
   * Utilisation avec async/await pour un code plus lisible
   */
  async searchWithAsyncAwait() {
    try {
      // Recherche aller simple
      const oneWayResponse = await this.flightSearchService.quickSearch({
        type: 'oneway',
        from: 'IST',
        to: 'TUN',
        departureDate: '2024-06-15',
        adults: 1
      }).toPromise();

      console.log('OneWay terminé:', oneWayResponse);

      // Si succès, faire une recherche aller-retour
      if (oneWayResponse?.header.success) {
        const roundTripResponse = await this.flightSearchService.quickSearch({
          type: 'roundtrip',
          from: 'IST',
          to: 'TUN',
          departureDate: '2024-06-15',
          returnDate: '2024-06-22',
          adults: 1
        }).toPromise();

        console.log('RoundTrip terminé:', roundTripResponse);
      }

    } catch (error: any) {
      console.error('Erreur async/await:', error.message);
    }
  }

  // ===== EXEMPLE 6: FORMATAGE DES RÉSULTATS =====
  
  /**
   * Exemple de formatage des résultats pour l'affichage
   */
  formatSearchResults() {
    this.flightSearchService.quickSearch({
      type: 'oneway',
      from: 'IST',
      to: 'TUN',
      departureDate: '2024-06-15',
      adults: 2,
      children: 1
    }).subscribe({
      next: (response) => {
        if (response.header.success) {
          // Formater le texte des passagers
          const passengerText = this.flightSearchService.formatPassengerText({
            adults: 2,
            children: 1,
            infants: 0
          });
          console.log('Passagers:', passengerText); // "2 adultes, 1 enfant"

          // Traiter les vols
          const flights = response.body.flights;
          flights.forEach((flight: any) => {
            flight.offers.forEach((offer: any) => {
              console.log(`Vol ${flight.id}: ${offer.price.amount} ${offer.price.currency}`);
            });
          });
        }
      }
    });
  }

  // ===== EXEMPLE 7: RECHERCHE AVEC FILTRES =====
  
  /**
   * Exemple de recherche avec options avancées
   */
  searchWithAdvancedOptions() {
    const advancedParams = {
      departureLocation: 'IST',
      arrivalLocation: 'TUN',
      departureDate: '2024-06-15',
      passengers: {
        adults: 1,
        children: 0,
        infants: 0
      },
      flightClass: 3, // Business
      directFlightsOnly: true, // Vols directs uniquement
      culture: 'en-US',
      currency: 'USD'
    };

    const oneWayRequest = this.flightSearchService.createOneWaySearch(advancedParams);
    
    this.flightSearchService.searchOneWay(oneWayRequest).subscribe({
      next: (response) => {
        console.log('Recherche avec filtres:', response);
        
        // Filtrer les résultats côté client si nécessaire
        if (response.header.success) {
          const directFlights = response.body.flights.filter((flight: any) => 
            flight.items.every((item: any) => item.stopCount === 0)
          );
          console.log(`${directFlights.length} vols directs trouvés`);
        }
      }
    });
  }
}
