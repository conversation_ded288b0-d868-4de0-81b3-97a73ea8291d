import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import { 
  OneWayRequest, 
  RoundTripRequest, 
  MulticityRequest, 
  FlightSearchResponse,
  FlightSearchForm,
  FlightSearchType,
  PassengerType,
  LocationType,
  FlightClass,
  Location,
  Passenger
} from '../models/flight-search-request.interface';
import { environment } from '../../environments/environment';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class FlightSearchService {
  private readonly baseUrl = environment.apiUrl;
  private readonly flightSearchEndpoint = environment.flightSearchEndpoint;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Recherche de vols aller simple
   * @param request Requête de recherche aller simple
   * @returns Observable<FlightSearchResponse>
   */
  searchOneWay(request: OneWayRequest): Observable<FlightSearchResponse> {
    const headers = this.getHeaders();
    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/oneway`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Recherche de vols aller-retour
   * @param request Requête de recherche aller-retour
   * @returns Observable<FlightSearchResponse>
   */
  searchRoundTrip(request: RoundTripRequest): Observable<FlightSearchResponse> {
    const headers = this.getHeaders();
    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/roundtrip`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Recherche de vols multi-destinations
   * @param request Requête de recherche multi-destinations
   * @returns Observable<FlightSearchResponse>
   */
  searchMultiCity(request: MulticityRequest): Observable<FlightSearchResponse> {
    const headers = this.getHeaders();
    return this.http.post<FlightSearchResponse>(`${this.baseUrl}${this.flightSearchEndpoint}/multicity`, request, { headers })
      .pipe(
        catchError(this.handleError)
      );
  }

  /**
   * Recherche de vols basée sur le formulaire utilisateur
   * @param formData Données du formulaire de recherche
   * @returns Observable<FlightSearchResponse>
   */
  searchFlights(formData: FlightSearchForm): Observable<FlightSearchResponse> {
    switch (formData.searchType) {
      case FlightSearchType.ONE_WAY:
        return this.searchOneWay(this.buildOneWayRequest(formData));
      case FlightSearchType.ROUND_TRIP:
        return this.searchRoundTrip(this.buildRoundTripRequest(formData));
      case FlightSearchType.MULTI_CITY:
        return this.searchMultiCity(this.buildMultiCityRequest(formData));
      default:
        return throwError(() => new Error('Type de recherche non supporté'));
    }
  }

  /**
   * Construit une requête OneWay à partir des données du formulaire
   */
  private buildOneWayRequest(formData: FlightSearchForm): OneWayRequest {
    return {
      ProductType: 2, // Type produit pour les vols
      ServiceTypes: ['Flight'],
      CheckIn: formData.departureDate,
      DepartureLocations: [this.createLocation(formData.departureLocation)],
      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],
      Passengers: this.buildPassengers(formData.passengers),
      showOnlyNonStopFlight: formData.directFlightsOnly,
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      calculateFlightFees: true,
      flightClasses: [formData.flightClass],
      Culture: formData.culture,
      Currency: formData.currency
    };
  }

  /**
   * Construit une requête RoundTrip à partir des données du formulaire
   */
  private buildRoundTripRequest(formData: FlightSearchForm): RoundTripRequest {
    const nights = this.calculateNights(formData.departureDate, formData.returnDate!);
    
    return {
      ProductType: 2,
      ServiceTypes: ['Flight'],
      DepartureLocations: [this.createLocation(formData.departureLocation)],
      ArrivalLocations: [this.createLocation(formData.arrivalLocation)],
      CheckIn: formData.departureDate,
      Night: nights,
      Passengers: this.buildPassengers(formData.passengers),
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      supportedFlightReponseListTypes: [1, 2],
      showOnlyNonStopFlight: formData.directFlightsOnly,
      calculateFlightFees: true,
      Culture: formData.culture,
      Currency: formData.currency
    };
  }

  /**
   * Construit une requête MultiCity à partir des données du formulaire
   */
  private buildMultiCityRequest(formData: FlightSearchForm): MulticityRequest {
    return {
      serviceTypes: ['Flight'],
      productType: 2,
      arrivalLocations: [this.createLocation(formData.arrivalLocation)],
      departureLocations: [this.createLocation(formData.departureLocation)],
      passengers: this.buildPassengers(formData.passengers),
      checkIns: [formData.departureDate],
      calculateFlightFees: true,
      acceptPendingProviders: true,
      forceFlightBundlePackage: false,
      disablePackageOfferTotalPrice: false,
      showOnlyNonStopFlight: formData.directFlightsOnly,
      supportedFlightReponseListTypes: [1, 2],
      culture: formData.culture,
      currency: formData.currency
    };
  }

  /**
   * Crée un objet Location
   */
  private createLocation(locationId: string): Location {
    return {
      id: locationId,
      type: LocationType.AIRPORT, // Par défaut, peut être ajusté
      provider: 1
    };
  }

  /**
   * Construit la liste des passagers
   */
  private buildPassengers(passengers: { adults: number; children: number; infants: number }): Passenger[] {
    const passengerList: Passenger[] = [];
    
    if (passengers.adults > 0) {
      passengerList.push({ type: PassengerType.ADULT, count: passengers.adults });
    }
    if (passengers.children > 0) {
      passengerList.push({ type: PassengerType.CHILD, count: passengers.children });
    }
    if (passengers.infants > 0) {
      passengerList.push({ type: PassengerType.INFANT, count: passengers.infants });
    }
    
    return passengerList;
  }

  /**
   * Calcule le nombre de nuits entre deux dates
   */
  private calculateNights(checkIn: string, checkOut: string): number {
    const startDate = new Date(checkIn);
    const endDate = new Date(checkOut);
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  /**
   * Obtient les headers pour les requêtes
   */
  private getHeaders(): HttpHeaders {
    return this.authService.getAuthHeaders();
  }

  /**
   * Gestion des erreurs HTTP
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Une erreur est survenue lors de la recherche de vols';
    
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      switch (error.status) {
        case 400:
          errorMessage = 'Paramètres de recherche invalides';
          break;
        case 401:
          errorMessage = 'Authentification requise';
          break;
        case 403:
          errorMessage = 'Accès refusé';
          break;
        case 404:
          errorMessage = 'Service de recherche non trouvé';
          break;
        case 500:
          errorMessage = 'Erreur interne du serveur';
          break;
        case 0:
          errorMessage = 'Impossible de contacter le serveur';
          break;
        default:
          errorMessage = `Erreur ${error.status}: ${error.message}`;
      }
    }
    
    console.error('Erreur de recherche de vols:', error);
    return throwError(() => new Error(errorMessage));
  }
}
