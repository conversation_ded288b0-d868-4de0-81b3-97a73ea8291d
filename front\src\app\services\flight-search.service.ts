import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// Import des interfaces de requête
import { OneWayRequest, createOneWayRequest } from '../models/oneway-request.interface';
import { RoundTripRequest, createRoundTripRequest } from '../models/roundtrip-request.interface';
import { MulticityRequest, createMulticityRequest } from '../models/multicity-request.interface';

// Import des interfaces de réponse
import { OneWayResponse } from '../models/oneway-response.interface';
import { RoundTripResponse } from '../models/roundtrip-response.interface';
import { MulticityResponse } from '../models/multicity-response.interface';

/**
 * Service pour la recherche de vols correspondant exactement aux endpoints backend
 * Endpoints backend:
 * - POST /product/search/oneway
 * - POST /product/search/roundtrip  
 * - POST /product/search/multicity
 * - POST /product/getoffers
 */
@Injectable({
  providedIn: 'root'
})
export class FlightSearchService {
  private readonly apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) {}

  // ===== RECHERCHES DE VOLS =====

  /**
   * Recherche de vols aller simple
   * Correspond à: POST /product/search/oneway
   */
  searchOneWay(request: OneWayRequest): Observable<OneWayResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<OneWayResponse>(
      `${this.apiUrl}/product/search/oneway`,
      request,
      { headers }
    ).pipe(
      catchError(this.handleError('Erreur lors de la recherche de vols aller simple'))
    );
  }

  /**
   * Recherche de vols aller-retour
   * Correspond à: POST /product/search/roundtrip
   */
  searchRoundTrip(request: RoundTripRequest): Observable<RoundTripResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<RoundTripResponse>(
      `${this.apiUrl}/product/search/roundtrip`,
      request,
      { headers }
    ).pipe(
      catchError(this.handleError('Erreur lors de la recherche de vols aller-retour'))
    );
  }

  /**
   * Recherche de vols multi-destinations
   * Correspond à: POST /product/search/multicity
   */
  searchMultiCity(request: MulticityRequest): Observable<MulticityResponse> {
    const headers = this.getHeaders();
    
    return this.http.post<MulticityResponse>(
      `${this.apiUrl}/product/search/multicity`,
      request,
      { headers }
    ).pipe(
      catchError(this.handleError('Erreur lors de la recherche de vols multi-destinations'))
    );
  }

  /**
   * Récupération des détails d'offres
   * Correspond à: POST /product/getoffers
   */
  getOffers(request: any): Observable<any> {
    const headers = this.getHeaders();
    
    return this.http.post<any>(
      `${this.apiUrl}/product/getoffers`,
      request,
      { headers }
    ).pipe(
      catchError(this.handleError('Erreur lors de la récupération des détails des offres'))
    );
  }

  // ===== MÉTHODES UTILITAIRES POUR LE FORMULAIRE =====

  /**
   * Crée une requête OneWay à partir de paramètres simplifiés
   */
  createOneWaySearch(params: {
    departureLocation: string;
    arrivalLocation: string;
    departureDate: string;
    passengers: { adults: number; children?: number; infants?: number };
    flightClass?: number;
    directFlightsOnly?: boolean;
    culture?: string;
    currency?: string;
  }): OneWayRequest {
    return createOneWayRequest({
      departureLocation: params.departureLocation,
      arrivalLocation: params.arrivalLocation,
      departureDate: params.departureDate,
      passengers: params.passengers,
      flightClass: params.flightClass,
      directFlightsOnly: params.directFlightsOnly,
      culture: params.culture,
      currency: params.currency
    });
  }

  /**
   * Crée une requête RoundTrip à partir de paramètres simplifiés
   */
  createRoundTripSearch(params: {
    departureLocation: string;
    arrivalLocation: string;
    departureDate: string;
    returnDate: string;
    passengers: { adults: number; children?: number; infants?: number };
    flightClass?: number;
    directFlightsOnly?: boolean;
    culture?: string;
    currency?: string;
  }): RoundTripRequest {
    return createRoundTripRequest({
      departureLocation: params.departureLocation,
      arrivalLocation: params.arrivalLocation,
      departureDate: params.departureDate,
      returnDate: params.returnDate,
      passengers: params.passengers,
      flightClass: params.flightClass,
      directFlightsOnly: params.directFlightsOnly,
      culture: params.culture,
      currency: params.currency
    });
  }

  /**
   * Crée une requête MultiCity à partir de paramètres simplifiés
   */
  createMultiCitySearch(params: {
    segments: Array<{ from: string; to: string; date: string }>;
    passengers: { adults: number; children?: number; infants?: number };
    directFlightsOnly?: boolean;
    culture?: string;
    currency?: string;
  }): MulticityRequest {
    return createMulticityRequest({
      segments: params.segments,
      passengers: params.passengers,
      directFlightsOnly: params.directFlightsOnly,
      culture: params.culture,
      currency: params.currency
    });
  }

  // ===== MÉTHODES DE RECHERCHE UNIFIÉES =====

  /**
   * Méthode unifiée pour rechercher des vols selon le type
   */
  searchFlights(searchType: 'oneway' | 'roundtrip' | 'multicity', params: any): Observable<any> {
    switch (searchType) {
      case 'oneway':
        const oneWayRequest = this.createOneWaySearch(params);
        return this.searchOneWay(oneWayRequest);

      case 'roundtrip':
        const roundTripRequest = this.createRoundTripSearch(params);
        return this.searchRoundTrip(roundTripRequest);

      case 'multicity':
        const multicityRequest = this.createMultiCitySearch(params);
        return this.searchMultiCity(multicityRequest);

      default:
        return throwError(() => new Error('Type de recherche non supporté'));
    }
  }

  /**
   * Recherche rapide avec paramètres simplifiés
   */
  quickSearch(params: {
    type: 'oneway' | 'roundtrip' | 'multicity';
    from: string;
    to: string;
    departureDate: string;
    returnDate?: string;
    segments?: Array<{ from: string; to: string; date: string }>;
    adults?: number;
    children?: number;
    infants?: number;
    directOnly?: boolean;
  }): Observable<any> {
    const searchParams = {
      departureLocation: params.from,
      arrivalLocation: params.to,
      departureDate: params.departureDate,
      returnDate: params.returnDate,
      segments: params.segments,
      passengers: {
        adults: params.adults || 1,
        children: params.children || 0,
        infants: params.infants || 0
      },
      directFlightsOnly: params.directOnly || false,
      culture: 'fr-FR',
      currency: 'EUR'
    };

    return this.searchFlights(params.type, searchParams);
  }

  // ===== MÉTHODES UTILITAIRES =====

  /**
   * Vérifie si le token d'authentification est valide
   */
  isAuthenticated(): boolean {
    const token = localStorage.getItem('authToken');
    return !!token;
  }

  /**
   * Formate les paramètres de passagers pour l'affichage
   */
  formatPassengerText(passengers: { adults: number; children?: number; infants?: number }): string {
    const parts: string[] = [];

    if (passengers.adults > 0) {
      parts.push(`${passengers.adults} adulte${passengers.adults > 1 ? 's' : ''}`);
    }

    if (passengers.children && passengers.children > 0) {
      parts.push(`${passengers.children} enfant${passengers.children > 1 ? 's' : ''}`);
    }

    if (passengers.infants && passengers.infants > 0) {
      parts.push(`${passengers.infants} bébé${passengers.infants > 1 ? 's' : ''}`);
    }

    return parts.join(', ');
  }

  /**
   * Valide les paramètres de recherche de base
   */
  validateSearchParams(params: any): string[] {
    const errors: string[] = [];

    if (!params.departureLocation) {
      errors.push('Lieu de départ requis');
    }

    if (!params.arrivalLocation) {
      errors.push('Lieu d\'arrivée requis');
    }

    if (!params.departureDate) {
      errors.push('Date de départ requise');
    }

    if (params.passengers?.adults < 1) {
      errors.push('Au moins un adulte requis');
    }

    return errors;
  }

  // ===== MÉTHODES PRIVÉES =====

  /**
   * Génère les en-têtes HTTP avec le token d'authentification
   * Le backend attend: Authorization: Bearer <token>
   */
  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');

    if (!token) {
      throw new Error('Token d\'authentification manquant');
    }

    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });
  }

  /**
   * Gestion des erreurs avec message personnalisé
   */
  private handleError(defaultMessage: string) {
    return (error: any): Observable<never> => {
      console.error('Erreur API:', error);

      let errorMessage = defaultMessage;

      // Gestion des erreurs spécifiques du backend
      if (error.error?.message) {
        errorMessage = error.error.message;
      } else if (error.status === 401) {
        errorMessage = 'Token d\'authentification invalide ou expiré';
      } else if (error.status === 403) {
        errorMessage = 'Accès non autorisé';
      } else if (error.status === 404) {
        errorMessage = 'Service non trouvé';
      } else if (error.status === 500) {
        errorMessage = 'Erreur interne du serveur';
      } else if (error.message) {
        errorMessage = error.message;
      }

      return throwError(() => new Error(errorMessage));
    };
  }
}
